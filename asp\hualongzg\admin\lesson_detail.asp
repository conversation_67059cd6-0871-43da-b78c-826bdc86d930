<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->

<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->

<h1 class="b_ch1">在线教学</h1>
<table class=table0 width=100% cellpadding=0 border=0>
<tr valign=top>
	<td class="td0 p12" style="padding-right:3px" align=left nowrap><%
set rs=server.createobject("adodb.recordset")	
sql = "Select id,name from lessons order by cate_id asc,sort_id asc"
rs.open sql,MM_conn_STRING
While not rs.eof
response.write "<a href=lesson_detail.asp?id=" & rs("id") & ">" & rs("name") & "</a><br>"
rs.movenext()
wend
rs.close
Set rs=nothing
	%></td>
	<td class=td0 align=left VALIGN=top><%
If request.querystring("id") <> "" Then
set rs=server.createobject("adodb.recordset")	
sql = "Select * from lessons where id=" & request.querystring("id") 
rs.open sql,MM_conn_STRING%>
<div style="border:<%=(back_menubackcolor)%> solid;border-width:1px;width:<%=rs("swfwidth")%>px;height:<%=rs("swfheight")%>px">
<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="<%=rs("swfwidth")%>" height="<%=rs("swfheight")%>" ID="Captivate1">
  <param name="movie" value="../images/lesson/<%=rs("swfname")%>">
  <param name="quality" value="high">
  <param name="menu" value="false">
  <param name="loop" value="0">
  <param name="wmode" value="transparent" />
  <embed src="<%=rs("swfname")%>" width="<%=rs("swfwidth")%>" height="<%=rs("swfheight")%>" loop="0" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" menu="false"></embed>
</object></div>
<%
Else
	response.redirect("lesson.asp")
End if
%>	
	</td>
</tr>
</table>

<!--#include file ="_bottom.asp"-->