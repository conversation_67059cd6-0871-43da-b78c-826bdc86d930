<!--#include file="_config.asp"-->
<%
'以上为管理员权限认证

if Session("hb_isadmin") <> true then Response.Redirect("../default.asp") 
'以上为加强的权限认证

dim page_title
' 执行每天只需处理一次的事件
Call BrandNewDay()

' 初始化数据库连接
Call DBConnBegin()

' 公用变量
Dim sAction
sAction = UCase(Trim(Request.QueryString("action")))

' ********************************************
' 以下为页面公用区函数
' ********************************************
' ============================================
' 输出每页公用的顶部内容
' ============================================
Sub Header()
	Response.Write "<html><head>"
	
	' 输出 meta 标记
	Response.Write "<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>"
	
	' 输出标题
	Response.Write "<title>htmlbuilder在线文本编辑器管理 - " & page_title & "</title>"
	
	' 输出每页都使用的基本客户端脚本
	Response.Write "<script language='javaScript' SRC='private.js'></SCRIPT>" & vbcrlf & _
"<style type=text/css>" & vbcrlf & _
"<!--" & vbcrlf & _
"table {border-collapse: collapse}" & vbcrlf & _
"sub,sup {font-size:8px;font-family: ""Arial"", ""宋体""}" & vbcrlf & _
"td,body,th,.p12,input,option,select {font-size:12px;line-height:135%;font-family:""Arial"",""宋体""}" & vbcrlf & _
".p14 {font-size:14px}" & vbcrlf & _
"a.nav:link {color: #3366cc; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav:visited {color: #3366cc; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav:hover {color: #333333; text-decoration: none;border:0px}" &  vbcrlf & _
"a.nav0:link {color: #FFFFFF; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav0:visited {color: #FFFFFF; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav0:hover {color: #cccccc; text-decoration: none;border:0px}" &  vbcrlf & _
"a.nav1:link {color: #333333; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav1:visited {color: #333333; text-decoration: none;border:0px}" & vbcrlf & _
"a.nav1:hover {color: #ffffff; text-decoration: none;border:0px}" &  vbcrlf & _
"a:link {color: #3366cc; text-decoration: none;border: 1px #3366cc solid;padding-left:1px;padding-right:1px}" & vbcrlf & _
"a:visited {color: #999999; text-decoration: none;border: 1px #999999 solid;padding-left:1px;padding-right:1px}" & vbcrlf & _
"a:hover { color: #333333; text-decoration: none;border: 1px #333333 solid;padding-left:1px;padding-right:1px;left: 1px;position:relative;top: 1px}" &  vbcrlf & _
".highlight1{color:#3366cc} " & vbcrlf & _
"-->" & vbcrlf & _
"</style>"
	
	Response.Write "</head>"

	Response.Write "<body topmargin=20 leftmargin=0 bgcolor=#ffffff>"
	Response.Write "<a name=top></a>"
	
	' 输出页面顶部(Header1)
	Response.Write "<table bordercolor=#999999 cellpadding=0 cellspacing=0 width=700 align=center>" & _
		"<tr><td height=35 bgcolor=#999999><table border=0 cellpadding=0 cellspacing=0 'width=100%' height=35><tr align=center><td width=60 nowrap class=p14 bgcolor=#3366CC style='padding-left:5px;padding-right:5px'><a href=hb_style.asp class=nav0>界面管理</a><a name=top></a></td><td width=1 bgcolor=#999999></td><td width=60 class=p14 nowrap bgcolor=#3366CC style='padding-left:5px;padding-right:5px'><a href=hb_decode.asp class=nav0>生成代码</a></td><td width=1 bgcolor=#999999></td><td width=60 class=p14 bgcolor=#3366CC style='padding-left:5px;padding-right:5px'><a href=hb_uploadfile.asp class=nav0>文件管理</a></td><td width=1 bgcolor=#999999></td><td width=60 class=p14 nowrap bgcolor=#3366CC style='padding-left:5px;padding-right:5px'><a href='hb_style.asp?action=logout' class=nav0>退出</a></td><td></td></tr></table></td></tr>"
	' 后台管理模块
	Response.Write "<tr><td height=5></td></tr><tr><td style='padding-bottom:4px' valign=top>"

End Sub

' ============================================
' 输出每页公用的底部内容
' ============================================
Sub Footer()
	' 释放数据连接对象
	Call DBConnEnd()

	Response.Write "</td></tr><tr><td align=right bgcolor=#999999 class=p12 style='padding-right:3px;padding-top:2px'><a href=#top class=nav1>↑ 顶部</a></td></tr></table></body></html>"
End Sub


' ===============================================
' 初始化下拉框
'	s_FieldName	: 返回的下拉框名	
'	a_Name		: 定值名数组
'	a_Value		: 定值值数组
'	v_InitValue	: 初始值
'	s_Sql		: 从数据库中取值时,select name,value from table
'	s_AllName	: 空值的称,如:"全部","所有","默认"
' ===============================================
Function InitSelect(s_FieldName, a_Name, a_Value, v_InitValue, s_Sql, s_AllName)
	Dim i
	InitSelect = "<select name='" & s_FieldName & "' size=1>"
	If s_AllName <> "" Then
		InitSelect = InitSelect & "<option value=''>" & s_AllName & "</option>"
	End If
	If s_Sql <> "" Then
		oRs.Open s_Sql, oConn, 0, 1
		Do While Not oRs.Eof
			InitSelect = InitSelect & "<option value=""" & inHTML(oRs(1)) & """"
			If oRs(1) = v_InitValue Then
				InitSelect = InitSelect & " selected"
			End If
			InitSelect = InitSelect & ">" & outHTML(oRs(0)) & "</option>"
			oRs.MoveNext
		Loop
		oRs.Close
	Else
		For i = 0 To UBound(a_Name)
			InitSelect = InitSelect & "<option value=""" & inHTML(a_Value(i)) & """"
			If a_Value(i) = v_InitValue Then
				InitSelect = InitSelect & " selected"
			End If
			InitSelect = InitSelect & ">" & outHTML(a_Name(i)) & "</option>"
		Next
	End If
	InitSelect = InitSelect & "</select>"
End Function


%>