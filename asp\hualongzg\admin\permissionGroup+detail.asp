<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<%
function checked(i,j)
	If i=j Then
		checked=" checked"
	Else
		checked=""
	End if
End Function

REM 权限设置，从视图menu来
set rs=server.createobject("adodb.recordset")
sql = "select MenuID,Menu from Menu order by MenuID asc"
rs.open sql,MM_conn_STRING,1,1
redim menu(rs.recordcount,1)
for i=1 to rs.recordcount
menu(i,0) = rs("MenuID")
menu(i,1) = rs("Menu")
rs.movenext()
next
rs.close()
set rs=nothing
'-------------
Function new_flag(j)
	new_flag = ""
	for i=1 to j
		If Right(request.Form("m" & i),1) <> "0" Then new_flag = new_flag & request.Form("m" & i) & ","
	Next
	If Right(new_flag,1)="," Then new_flag = left(new_flag,Len(new_flag)-1)
End Function

set conn=server.createobject("ADODB.CONNECTION")
conn.open MM_conn_STRING

%>
<h1 class="b_ch1">权限管理</h1>
<%
call main()
set rs=nothing
conn.close
set conn=nothing

sub main()
%>
<script LANGUAGE="JavaScript">
function check()
{
document.Form1.submit()
}
</SCRIPT>
<%
if request("action")="editsave" then 
call editsave()
elseif request("action")="add" then 
call add()
elseif request("action")="addsave" then 
call addsave()
else
call manager()
end if
%>
<p><%=body%></p>
<%
end sub

sub manager()
dim sql,rs,id
id=request("group_id")
sql="select * from usergroup where group_id="&cstr(id)
Set rs= Server.CreateObject("ADODB.Recordset")
rs.open sql,MM_conn_STRING,1,1
%>

<TABLE border=0 cellPadding=3 cellSpacing=0 width="600" border=1 bordercolor=<%=back_menubackcolor0%>>
<FORM action="permissionGroup+detail.asp?action=editsave&group_id=<%=id%>" method="post" name="Form1">
<thead>
<TR>
<TD align="left" height="28" colspan=2>部门或角色（用户组）<span style="font-size:12px;color:#f00">修改组权限后，组成员要重新登录，权限才生效。</span></td>
</TR>
<TR> 
<TD align="center" height="28">名称</td>
<TD><%=rs("name")%></TD>
</TR>
<TR>
<TD align="center" height="28">说明</TD>
<TD class=p12><%=rs("descript")%>&nbsp;</TD>
</TR>
</thead>
<TR> 
<TD align="left" height="28" colspan=2 colspan=2>权限</td>
</TR>
<TR>
<TD colspan=2>
<table border=0 cellpadding=0 width=700 cellspacing=0>
<%for i=1 to ubound(menu)
if isempty(menu(i,0)) then exit for
menuname=menu(i,1)


%>
<tr><td><%=menuname%></td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,0"<%=checked(inint2(rs("flag"),menu(i,0)),0)%> />无权限</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,1"<%=checked(inint2(rs("flag"),menu(i,0)),1)%> />浏览</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,2"<%=checked(inint2(rs("flag"),menu(i,0)),2)%> />浏览并增加信息</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,3"<%=checked(inint2(rs("flag"),menu(i,0)),3)%> />内容管理（浏览、增删除信息）</td></tr>
<%next%>
</table>
</TD></TR>
<TR>
<TD colspan=2 height="28" align="center"> 
<input type="button" value="修 改"<%if id=1 then response.write(" disabled")%> class="smallInput" onclick="check()">
</TD>
</TR>
</TABLE></FORM>
<%
rs.close
set rs=nothing
end sub

sub editsave()
set rs=server.createobject("adodb.recordset")
sql="select group_id,flag from usergroup where group_id="&request("group_id")
rs.open sql,conn,3,3
rs("flag")= new_flag(ubound(menu))
rs.update
response.redirect("permissionGroup+detail.asp?group_id=") & rs("group_id")
end sub

sub add()
%>

<TABLE border=0 cellPadding=3 cellSpacing=0 width="600" border=1 bordercolor=<%=back_menubackcolor0%>>
<FORM action="permissionGroup+detail.asp?action=addsave" method="post" name="Form1">
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>>部门或角色（用户组）</td>
</TR>
<TR> 
<TD align="center" height="28">名称</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=name size="20"></font>
</TD>
</TR>
<TR>
<TD align="center" height="28">说明</TD>
<TD>&nbsp;&nbsp;<input type=text name=descript size="50" class=i500></TD>
</TR>
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>>权限</td>
</TR>
<TR>
<TD colspan=2>
<table border=0 cellpadding=0 width=700 cellspacing=0>
<%for i=1 to ubound(menu)
if isempty(menu(i,0)) then exit for
menuname=menu(i,1)
%>
<tr><td><%=menuname%></td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,0" checked />无权限</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,1" />浏览</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,2" />浏览并增加信息</td><td><input type="radio" name="m<%=i%>" value="<%=menu(i,0)%>,3" />内容管理（浏览、增删除信息）</td></tr>
<%next%>
</table>
</TD>
</TR>
<TR>
<TD colspan=2 height="28" align="center" bgcolor=<%=back_menubackcolor%>> 
<input type="button" value="添 加" class="smallInput" onclick="check()">
</TD>
</TR>
</FORM>
</TABLE>
<%
end sub

sub addsave()
set rs=server.createobject("adodb.recordset")
sql="select * from usergroup"
rs.open sql,conn,3,3
rs.addnew
rs("name")=request("name")
rs("descript")=request("descript")
rs("flag")= new_flag(ubound(menu))
rs.update
response.write "用户组信息添加成功"
end sub
%>

 
<!--#include file ="_bottom.asp"-->