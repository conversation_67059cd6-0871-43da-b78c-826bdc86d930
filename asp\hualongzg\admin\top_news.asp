<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%if request.form("admin") <> "" then '批准与取消
	set rs1=server.createobject("adodb.recordset")
	sql="select pageid,top_submit,data_mod from page where pageid=" & request("pageid")
	rs1.open sql,MM_conn_STRING,3,3
	if request.form("admin") <> "更新" then  rs1("top_submit")= not rs1("top_submit")
	rs1("data_mod") = now()
	rs1.update
	rs1.close()
	set rs1=nothing
end if

if request.form("cancel") <> "" then '批准与取消
	set rs1=server.createobject("adodb.recordset")
	sql="select pageid,top_style from page where pageid=" & request("pageid")
	rs1.open sql,M<PERSON>_conn_STRING,3,3
	rs1("top_style") = 2
	rs1.update
	rs1.close()
	set rs1=nothing
end if
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<%page_title = "图片新闻"%>
<h1 class="b_ch1"><%=page_title%></h1>
<%

'定义记录集
set rs=server.createobject("adodb.recordset") 
sql="select page.pageid,page.title,page.top_submit,page.top_style,page.data_mod,page.data_creat,subclass.name as subname,class.id as classid,subclass.subid,class.name from ((page inner join subclass on page.subid=subclass.subid) inner join class on class.id=subclass.id) where page.subid=2 AND top_style=2 order by top_submit asc,data_mod desc,data_creat desc" 

rs.open sql,MM_conn_STRING,1,1 
if not rs.eof then
dim temp_i
temp_i =0
%>
<table width=650 cellpadding=2 cellspacing=0 border=1 bordercolor=<%=(back_menubackcolor)%>>
<tr align=center bgcolor=<%=(back_menubackcolor)%>>
	<td>主题（点击主题修改相应页面）</td>
	<td>发布时间</td>
	<td>状态</td>
	<td>决定</td>
</tr>
<%while not rs.eof
temp_i = temp_i +1
if rs("top_submit") = true then
'	if temp_i > 6 then
'		td_con_1 = "过期"
'		td_con_2 = "更　新"
'		td_con_22 = "重新让这个页面在" & page_title & "显示？"
'	else
		td_con_1 = "显示"
		td_con_2 = "不显示"
		td_con_22 = "不让这个页面在" & page_title & "显示？"
'	end if
	else
	td_con_1 = "不显示"
	td_con_2 = "显　示"
	td_con_22 = "确定让这个页面在" & page_title & "显示？"
end if
%>
<form method=post>
<tr>
<td class=p12><a href=page_mod.asp?subname=<%=server.urlencode(rs("subname"))%>&action=mod&pageid=<%=rs("pageid")%> title="点击修改这个页面"><%=HTMLEncode(rs("title"))%></a></td>
<td width=120 class=p12 nowrap><%=rs("data_creat")%></td>

<td width=40 class=p12 align=center><%=td_con_1%></td>


<td align=center width=30><input name="admin" type="submit" value="<%=td_con_2%>" onClick="GP_popupConfirmMsg('<%=td_con_22%>');return document.MM_returnValue"></td>

</tr>
<%'if temp_i=6 then response.write("<tr><td height=2 colspan=5>&nbsp;</td></tr>")%>
<input type=hidden name="pageid" value="<%=rs("pageid")%>">
</form>
<%
rs.movenext()
wend%>
</table>
<br>

<%
else			'如果没有记录
response.write("<h3>没有首页图片新闻</h3>")
end if
rs.close()
set rs=nothing
%>
<font color=#ff0000 class=p14>说明：</font><br>
<div class=p12 style="width:600px">
<li>首页“图片新闻”和“要闻集萃”的内容，都是从后台“新闻”栏目输入，如果新闻不在首页图片新闻显示，则在要闻集萃显示。</li>
<li>图片新闻按批准时间顺序显示。</li>
<li>要闻集萃按发布时间顺序显示。</li>
</div>
<!--#include file ="_bottom.asp"-->