<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("/admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<%
set rsLINKS2 = Server.CreateObject("ADODB.Recordset")
rsLINKS2.ActiveConnection = MM_conn_STRING
rsLINKS2.Source = "SELECT agree, id, my_url, site_contact, site_data, site_decript, site_demo, site_email, site_name, site_url FROM link_exchang ORDER BY site_data DESC"
rsLINKS2.CursorType = 0
rsLINKS2.CursorLocation = 2
rsLINKS2.LockType = 3
rsLINKS2.Open()
rsLINKS2_numRows = 0
%>
<%
Dim Repeat2__numRows
Repeat2__numRows = -1
Dim Repeat2__index
Repeat2__index = 0
rsLINKS2_numRows = rsLINKS2_numRows + Repeat2__numRows
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">友情链接删除</h1>
<form name="form1" method="post" action="links_del.asp">
<table border=1 align=center cellspacing="0" cellpadding="4" width=500>
<thead><tr align=right><td class=p14 align=center>网站名称</td><td class=p14 align=center nowrap width=30>选择</td></tr></thead>
  <% 
While ((Repeat2__numRows <> 0) AND (NOT rsLINKS2.EOF)) 
%><tr><td class=p12 align=left nowrap><%=(rsLINKS2.Fields.Item("site_name").Value)%></td><td align=center class=p12 width=20 valign=middle nowrap><input type="checkbox" name="id" value="<%=(rsLINKS2.Fields.Item("id").Value)%>"></td></tr>
  <% 
  Repeat2__index=Repeat2__index+1
  Repeat2__numRows=Repeat2__numRows-1
  rsLINKS2.MoveNext()
Wend
%>
<tr><td nowrap class=p14 colspan=2 align=right><input type="submit" value="删除"  onClick="GP_popupConfirmMsg('确认删除？');return document.MM_returnValue"></td></tr>
</table>
</form>
<!--#include file ="_bottom.asp"--><%
rsLINKS2.Close()
set rsLINKS2 = nothing
%>
