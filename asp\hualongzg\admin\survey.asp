<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">在线调查</h1>
<script LANGUAGE="javascript">
<!--
function ShowDialog(url, width, height, optValidate) {
	if (optValidate) {
		if (!validateMode()) return;
	}
	var arr = showModalDialog(url, window, "dialogWidth:" + width + "px;dialogHeight:" + height + "px;help:no;scroll:yes;status:no");
}

function check()
{
if (document.frmadd.newballot.value==""){
  alert("请输入调查项目的主题");
  document.frmadd.newballot.focus();
  return false;
  }
}

function check2(field) { 
	document.MM_returnValue = true;
    if (field.value == '') {
alert('请输入内容');document.MM_returnValue = false;field.focus();return;}
}
//-->
</script>
<table cellpadding=0 cellspacing=1 border=0 width=600 class=table0>
<tr><td class=td0>
<a href=survey.asp?action=add>→ 增加调查项目</a></td><td align=right class="td0 p12"><a href=# onclick="ShowDialog('survey_help.asp','400','300')">帮助</a></td></tr>
</table>
<%
set rs=server.createobject("adodb.recordset")
sql = "select ballottop.id,ballottop.ballottop,ballottop.mymode,ballottop.is_date,sum(ballot.hit) as this_hit from ballottop,ballot where ballottop.id=ballot.top_id group by ballottop.id,ballottop.ballottop, ballottop.mymode,ballottop.is_date"
rs.open sql,MM_conn_STRING,1,1
if not rs.eof then
%>
<table cellpadding=3 cellspacing=1 border=1 width=600>
<tr align=center bgcolor=<%=(back_menubackcolor)%>>
	<td>投票项目</td>
	<td>投票总数</td>
	<td>发起时间</td>
	<td>打开</td>
	<td>编辑</td>
	<td>删除</td>
</tr>
<%while not rs.eof%>
<form action=survey.asp?id=<%=rs("id")%>&action=open method=post>
<tr align=center class=p12>
<td class=p12><a href=survey.asp?id=<%=rs("id")%>&action=edit><%=rs("ballottop")%></a></td>
<td class=p12><%=rs("this_hit")%></td>
<td class=p12><%=rs("is_date")%></td>
<td class=p12><%if rs("mymode") =true then
%><input type="submit" value="已打开" style="background-color:<%=(back_menubackcolor)%>" title="点击关闭这个调查">
<%else
	'先判断有没有选项，如果选项少于2个则不显示打开
	set rs1=server.createobject("adodb.recordset")
	sql1 = "select id from ballot where top_id =" & cstr(rs("id"))
	rs1.open sql1,MM_conn_STRING,1,1
	if rs1.recordcount > 1 then
	response.write("<input type=submit value=已关闭 style='background-color:#eeeeee' title=点击打开这个调查>")
	else
	response.write("无选项，先添加")
	end if
	rs1.close()
	set rs1=nothing%>
<%end if%></td>
<td class=p12><a href=survey.asp?id=<%=rs("id")%>&action=edit>查看编辑</a></td>
<td class=p12><a href=survey.asp?id=<%=rs("id")%>&action=del onClick="GP_popupConfirmMsg('确定删除本项目？\n删除项目后，其调查结果也会被删除');return document.MM_returnValue">删除</td></tr></form>
<%
rs.movenext()
wend%>
</table>
<%
else
response.write("<h1 aling=center>没有投票项目</h1>")
end if
rs.close()
set rs=nothing%>

<%
select case request("action")
'///
case "open"				'打开关闭调查项目

set rs=server.CreateObject("adodb.recordset")
sql = "select mymode,is_date from ballottop where id =" & request.querystring("id")
rs.open sql, MM_conn_STRING,1,3
	if rs("mymode") = true then
	rs("mymode") = false
	else
		'先把所有的都设定为flase
		set command=server.createobject("adodb.command")
		sql="update ballottop set mymode=false"
		Command.ActiveConnection = MM_conn_STRING
		Command.CommandText = sql
		Command.Execute()
		set command =nothing

	rs("mymode") = true
	rs("is_date") = Now()
	end if
	rs.update
rs.close()
set rs=nothing

response.redirect("survey.asp")

'/////

case "add"				'要增加调查项目%>
<form method=post action="survey.asp" method=post name="frmadd"  onSubmit="return check()">
<input type="text" name="newballot" size=12 maxlength=12 class=i400> <input type="submit" value="增加项目（最多12个字）">
<input type=hidden name="action" value="addnew">
</form>

<%case "edit"			'编辑查看调查项目
set rs=server.CreateObject("adodb.recordset")
sql="select top 5 ballot.*,ballottop.ballottop from ballot inner join ballottop on ballottop.id=ballot.top_id where top_id =" & request.querystring("id")
rs.open sql,MM_conn_STRING,1,1
%>
<br><br>

<fieldset style="width:600px">
<legend>查看与编辑项目<%if not rs.eof then response.write("：<font color=#000000><b>" & rs("ballottop") & "</b></font>")%></legend>
<%if not rs.eof then%>
<table width=300 cellpadding=3 cellspacing=1 border=1 align=center>
<tr align=center bgcolor=<%=(back_menubackcolor)%>>
	<td class=p12>项目（最多11个字，5个选项）</td>
	<td class=p12>投票值</td>
	<td class=p12>修改</td>
	<td class=p12>删除</td>
</tr>
<%while not rs.eof
response.write("<form method=post action=survey.asp?id=" & rs("id") & "&action=sedit><tr><td class=p12><input onChange=""check2(this)"" name=ballot type=text maxlength=11 value=""" & rs("ballot") & """></td><td><input name=hit size=3 type=text value=""" & rs("hit") & """></td><td class=p12><input type=submit value=修改></td><td class=p12><a href=survey.asp?id=" & rs("id") & "&action=sdel onClick=""GP_popupConfirmMsg('确定删除选项？');return document.MM_returnValue"">删除</a></td></tr></form>")
rs.movenext()
wend%>
</table>
<%
else
response.write("<div class=p12 align=center>没有选项，请增加</div>")
end if

if rs.recordcount < 5 then '如果小于5个选项，显示增加选项
%>
<br>
<table width=300 cellpadding=3 cellspacing=1 border=1 align=center bgcolor=#cccccc>
<form method=post action="survey.asp?id=<%=request.querystring("id")%>&action=sadd">
<tr align=center><td>&nbsp;</td><td class="p12">选项</td><td class="p12">初始值</td>
<tr align=center><td class="p12">增加选项：</td>
<td><input name=ballot type=text maxlength=11></td>
<td><input name=hit size=3 type=text></td>
</tr>
<tr><td colspan=3 class=p12 align=right><input type="submit" value="确定"></td></tr>
</form>
</table>
<%
end if
rs.close()
set rs=nothing%>
</fieldset>

<%
'///
case "sadd"				'增加选项
sql="select ballot,hit,top_id from ballot"
set rs=server.CreateObject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,3
rs.addnew

rs("ballot") = replace(request("ballot"),"'","")
if IsNumeric(request("hit")) then
	rs("hit") = cint(request("hit"))
	else
	rs("hit") = 0
end if
rs("top_id") = cint(request.querystring("id"))
rs.update
rs_t = rs("top_id")
rs.close()
set rs=nothing

response.redirect("survey.asp?id=" & rs_t & "&action=edit")

'///
case "sedit"			'编辑选项
sql="select id,ballot,hit,top_id from ballot where id =" & request.querystring("id")
set rs=server.CreateObject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,3
rs("ballot") = replace(request("ballot"),"'","")
if IsNumeric(request("hit")) then
	rs("hit") = cint(request("hit"))
	else
	rs("hit") = 0
end if
rs.update
rs_t = rs("top_id")
rs.close()
set rs=nothing

response.redirect("survey.asp?id=" & rs_t & "&action=edit")


'///
case "sdel"				'删除项目
sql="select id,ballot,hit,top_id from ballot where id =" & request.querystring("id")
set rs=server.CreateObject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,3
rs_t=rs("top_id")
rs.delete
rs.update
rs.close()
set rs=nothing

response.redirect("survey.asp?id=" & rs_t & "&action=edit")
'////////
case "addnew"			'增加调查项目
sql="select id,ballottop from ballottop"
set rs=server.CreateObject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,3
rs.addnew
rs("ballottop") = replace(request("newballot"),"'","")
rs.update
rs_t=cstr(rs("id"))
rs.close()
response.redirect("survey.asp?id=" & rs_t & "&action=edit")

case "del"				'删除调查项目
set command=server.createobject("adodb.command")
sql="delete from ballottop where id=" & request.querystring("id")
Command.ActiveConnection = MM_conn_STRING
Command.CommandText = sql
Command.Execute()
response.redirect("survey.asp")
set command =nothing

end select
%>
 
<!--#include file ="_bottom.asp"-->