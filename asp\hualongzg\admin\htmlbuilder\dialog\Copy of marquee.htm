<HTML>
<HEAD>
<META content="text/html; charset=UTF-8" http-equiv=Content-Type>
<style>
BODY {PADDING:5PX}
TD,BODY,SELECT,P,INPUT {FONT-SIZE:9PT}
</style>

<script language="JavaScript" src="dialog.js"></script>

<script language=javascript>
var sAction = "INSERT";
var sTitle = "插入";
var sel = dialogArguments.htmlbuilder.document.selection.createRange();
sel.type = dialogArguments.htmlbuilder.document.selection.type;

var el;
var sText = "";
var sBehavior = "";
var sdirection = "";
var sscrolldelay = "";

if (sel.type=="Control") {
	if (sel.item(0).tagName=="MARQUEE"){
		sAction = "MODI";
		sTitle = "修改";
		el = sel.item(0);
		sBehavior = el.behavior;
		sdirection = el.direction;
		sscrolldelay = el.scrolldelay;
		sText = el.innerHTML;
	}
}

document.write("<title>字幕属性（" + sTitle + "）</title>");


// 单选的点击事件
function check(){
	sBehavior = event.srcElement.value;
}

function check_direction(){
	sdirection = event.srcElement.value;
}

function check_scrolldelay(){
	sscrolldelay = event.srcElement.value;
}

// 初始值
function InitDocument() {
	d_text.value = sText;
	switch (sBehavior) {
	case "scroll":
		document.all("d_behavior")[0].checked = true;
		break;
	case "slide":
		document.all("d_behavior")[1].checked = true;
		break;
	default:
		sBehavior = "alternate";
		document.all("d_behavior")[2].checked = true;
		break;
	}
//方向
	switch (sdirection) {
	case "right":
		document.all("d_direction")[1].checked = true;
		break;
	case "up":
		document.all("d_direction")[2].checked = true;
		break;
	case "down":
		document.all("d_direction")[3].checked = true;
		break;
	default:
		sdirection = "left";
		document.all("d_direction")[0].checked = true;
		break;
	}
//速度
	switch (sscrolldelay) {
	case "140":
		document.all("d_scrolldelay")[0].checked = true;
		break;
	case "40":
		document.all("d_scrolldelay")[2].checked = true;
		break;
	default:
		sscrolldelay = "90";
		document.all("d_scrolldelay")[1].checked = true;
		break;
	}

}
</script>


<SCRIPT event=onclick for=Ok language=JavaScript>
	sText = d_text.value;
	if (sAction == "MODI") {
		el.behavior = sBehavior;
		el.direction = sdirection;
		el.scrolldelay = sscrolldelay;		
		el.innerHTML = sText;
	}else{
		dialogArguments.insertHTML("<marquee behavior="+sBehavior+" direction="+sdirection+" scrolldelay="+sscrolldelay+">"+sText+"</marquee>");
	}
	window.returnValue = null;
	window.close();
</script>
</HEAD>

<body bgcolor=menu onload="InitDocument()">

<table border=0 cellpadding=0 cellspacing=0 align=center>
<tr><td>
	<FIELDSET align=left>
	<LEGEND></LEGEND>
	<table border=0 cellspacing=5 cellpadding=0>
	<tr valign=middle><td>文本:&nbsp;</td><td><input type=text id="d_text" size=50 value=""></td></tr>
	<tr valign=middle><td>表现:&nbsp;</td><td><input onclick="check()" type="radio" name="d_behavior" value="scroll"> 滚动条 <input onclick="check()" type="radio" name="d_behavior" value="slide"> 幻灯片 <input onclick="check()" type="radio" name="d_behavior" value="alternate"> 交替</td></tr>
	<tr valign=middle><td>方向:&nbsp;</td><td><input onclick="check_direction()" type="radio" name="d_direction" value="left">向左 <input onclick="check_direction()" type="radio" name="d_direction" value="right">向右 <input onclick="check_direction()" type="radio" name="d_direction" value="up">向上 <input onclick="check_direction()" type="radio" name="d_direction" value="down">向下</td></tr>
	<tr valign=middle><td>速度:&nbsp;</td><td><input onclick="check_scrolldelay()" type="radio" name="d_scrolldelay" value="40">快 <input onclick="check_scrolldelay()" type="radio" name="d_scrolldelay" value="90">中 <input onclick="check_scrolldelay()" type="radio" name="d_scrolldelay" value="140">慢</td></tr>
	</table>
	</FIELDSET>

</td></tr>
<tr><td height=10></td></tr>
<tr><td align=right><input type=submit value='  确定  ' id=Ok>&nbsp;&nbsp;<input type=button value='  取消  ' onclick="window.close();"></td></tr>
</table>

</body>
</html>
