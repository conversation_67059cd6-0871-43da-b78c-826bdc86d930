<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
Dim pageid
Dim list_id,list_sort,list_p
pageid = request.querystring("pageid")
list_id = request.querystring("list_id")
list_sort = request.querystring("sort")
po = request.querystring("po")

Call page_sort(pageid,list_id,po,"page",list_sort)
sub page_sort(i,list_id,po,table,list_sort)
	'table的值要排序的表,list_id即page.subid
	'页面排序，i为页面编号,po值: up,down,top,bottom 上、下、顶、底
	'list_sort 为 排序，如果是0,则sort_id最大的先显示，反之，倒过来
	dim rs,sql,alert_str,sort_id,fid,name_str

	'首先得到编号列值、最大sort_id 最小 sort_id 上一个 下一个sort_id值
	set rs=server.createobject("adodb.recordset")
	name_str = "页面"
	sql="select max(page.sort_id) as maxs,min(page.sort_id) as mins from page where subid=" & list_id
	rs.open sql,MM_conn_STRING,1,1
	maxs = rs("maxs")
	mins = rs("mins")
	rs.close()

	sql="select sort_id,subid as fid from " & table & " where pageid=" &i
	rs.open sql,MM_conn_STRING,3,2

	If po = "top" Or po = "bottom" then'如果是到顶部或者底部
		If list_sort = 0 Then
			If po = "top" then
				rs("sort_id") = maxs+1
				rs.update()
			Else
				rs("sort_id") = mins-1
				rs.update()
			End if
		Else
			If po = "top" then
				rs("sort_id") = mins-1
				rs.update()
			Else
				rs("sort_id") = maxs+1
				rs.update()
			End if		
		End If
		rs.close()
		Set rs=nothing
		Exit sub
	End If
	
	If po = "up" Or po = "down" then'如果是上移或者下移
		sort_id = rs("sort_id")
		fid = rs("fid")
		rs.close()
		If list_sort = 0 then
			select case po
			case "up"
			sql = "select top 1 pageid as id, sort_id from " & table & " where sort_id>" & sort_id & " AND subid=" & fid & " order by sort_id asc"
			alert_str = "这个" & name_str & "已经在最上方"
			case "down"
			sql = "select top 1 pageid as id, sort_id from " & table & " where sort_id<" & sort_id & " AND subid=" & fid & " order by sort_id desc"
			alert_str = "这个" & name_str & "已经在最下方"
			end select
		Else
			select case po
			case "up"
			sql = "select top 1 pageid as id, sort_id from " & table & " where sort_id<" & sort_id & " AND subid=" & fid & " order by sort_id desc"
			alert_str = "这个" & name_str & "已经在最上方"
			case "down"
			sql = "select top 1 pageid as id, sort_id from " & table & " where sort_id>" & sort_id & " AND subid=" & fid & " order by sort_id asc"
			alert_str = "这个" & name_str & "已经在最下方"
			end select
		End If

		rs.open sql,MM_conn_STRING,1,1
		if rs.eof then
			rs.close()
			set rs=nothing
			Response.Write "<script language=javascript>alert('" & alert_str & "');</script>"
			Exit sub
		else
			dim new_sort_id, new_i
			dim rs2,id_i
			dim ids,ids_new	'二个id数组
			new_sort_id = rs("sort_id")
			new_i = rs("id")
			'执行sql将i,new_i这二行的sort_id互换
			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING
			sql = "update page set sort_id = " & new_sort_id & " where pageid=" &i
			sql2 = "update page set sort_id = " & sort_id & " where pageid=" &new_i
			conn.execute sql
			conn.execute sql2
			set conn=nothing
		end if
	End if
end sub
%>
