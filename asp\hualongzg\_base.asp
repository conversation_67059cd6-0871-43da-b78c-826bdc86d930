<%''ver 7.1 最早用于 hn-power.com,为UTF-8
'处理页面表关联(etorch _page.asp 中）未放入本库

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'														说 明

'function HTMLEncode			取代 server.htmlencode
'function page_num_list		为分页函数，可用于 ajax ，引用了 replace_page ，为 list_nav 引用
'function replace_page		替换字符串中 #page# 为相应的页码 为 page_num_list 引用
'Sub error_flow				错误处理流程，暂未扩展，只是简单的导向到 default.asp
'Function SafeRequest		处理 request 参数的安全性
'Function SafeRequest2		同上，对于数字类，如果参数不安全，赋值为缺省值
'function this_filename		得到当前执行的页面文件名（没有querystring) 为 sub_nav 引用
'function cutStr				得到指定长度，去掉 html 标记的字符串
'function replace_en_char	去掉英文字符串里的全角特殊字符
'function DoDateTime			日期与时间格式化函数
'Function mail_str email	编码，反垃圾邮件扫描 联系方式等输出email地址的页面要用到
'Function DateTimeToGMT		转换时间为GMT(RFC822)格式时间函数 RSS里要用
'Function GBTOUTF8			GB2UTF8
'Function chinesetoutf8		繁体中文2utf8
'Function sub_nav				子导航函数（其中type为"list" "area"，对应当前list.id 当前area.cate_lcode）
'Function nav_tree			主导航树 cate_lcode 为语言版本起点 "01"为中文 "02"..只作了二级，不管子区域多少级只输出二级
'Function navurl				区域与列表导航条目<a href=...></a> ，在sub_nav 和 nav_tree 里调用
'Function page_list			取page表里的一些条目 不处理不安全参数 在过程 list_out_sub 用到 输出 list_para（总页 当前页）
'Function first_id(tbtype) 系统中的第一个非特定列表或者页面记录
'sub list_out_sub				输出数组 page_out 在列表页执行
'sub page_out_sub				输出页面，得到数组 page_out 在详细页执行
'sub area_out sub				输出区域，得到数据 area_out
'function page_nav			详细页面导航，即“顶部、打印、返回”
'Function list_nav			列表页面导航 countpage 该列表分页数， curpage 当前页面的真实页码

'----以上为页面处理

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※

Function HTMLEncode(Str)
 If Isnull(Str) Then
     HTMLEncode = ""
     Exit Function 
 End If
	Str=Replace(Str,"ガ","&#12460;")
	Str=Replace(Str,"ギ","&#12462;")
	Str=Replace(Str,"ア","&#12450;")
	Str=Replace(Str,"ゲ","&#12466;")
	Str=Replace(Str,"ゴ","&#12468;")
	Str=Replace(Str,"ザ","&#12470;")
	Str=Replace(Str,"ジ","&#12472;")
	Str=Replace(Str,"ズ","&#12474;")
	Str=Replace(Str,"ゼ","&#12476;")
	Str=Replace(Str,"ゾ","&#12478;")
	Str=Replace(Str,"ダ","&#12480;")
	Str=Replace(Str,"ヂ","&#12482;")
	Str=Replace(Str,"ヅ","&#12485;")
	Str=Replace(Str,"デ","&#12487;")
	Str=Replace(Str,"ド","&#12489;")
	Str=Replace(Str,"バ","&#12496;")
	Str=Replace(Str,"パ","&#12497;")
	Str=Replace(Str,"ビ","&#12499;")
	Str=Replace(Str,"ピ","&#12500;")
	Str=Replace(Str,"ブ","&#12502;")
	Str=Replace(Str,"ブ","&#12502;")
	Str=Replace(Str,"プ","&#12503;")
	Str=Replace(Str,"ベ","&#12505;")
	Str=Replace(Str,"ペ","&#12506;")
	Str=Replace(Str,"ボ","&#12508;")
	Str=Replace(Str,"ポ","&#12509;")
	Str=Replace(Str,"ヴ","&#12532;")
 Str = Replace(Str,Chr(0),"", 1, -1, 1)
 Str = Replace(Str, """", "&quot;", 1, -1, 1)
 Str = Replace(Str,"<","&lt;", 1, -1, 1)
 Str = Replace(Str,">","&gt;", 1, -1, 1) 
 Str = Replace(Str, "script", "&#115;cript", 1, -1, 0)
 Str = Replace(Str, "SCRIPT", "&#083;CRIPT", 1, -1, 0)
 Str = Replace(Str, "Script", "&#083;cript", 1, -1, 0)
 Str = Replace(Str, "script", "&#083;cript", 1, -1, 1)
 Str = Replace(Str, "object", "&#111;bject", 1, -1, 0)
 Str = Replace(Str, "OBJECT", "&#079;BJECT", 1, -1, 0)
 Str = Replace(Str, "Object", "&#079;bject", 1, -1, 0)
 Str = Replace(Str, "object", "&#079;bject", 1, -1, 1)
 Str = Replace(Str, "applet", "&#097;pplet", 1, -1, 0)
 Str = Replace(Str, "APPLET", "&#065;PPLET", 1, -1, 0)
 Str = Replace(Str, "Applet", "&#065;pplet", 1, -1, 0)
 Str = Replace(Str, "applet", "&#065;pplet", 1, -1, 1)
 Str = Replace(Str, "[", "&#091;")
 Str = Replace(Str, "]", "&#093;")
 Str = Replace(Str, """", "", 1, -1, 1)
 Str = Replace(Str, "=", "&#061;", 1, -1, 1)
 Str = Replace(Str, "'", "''", 1, -1, 1)
 Str = Replace(Str, "select", "sel&#101;ct", 1, -1, 1)
 Str = Replace(Str, "execute", "&#101xecute", 1, -1, 1)
 Str = Replace(Str, "exec", "&#101xec", 1, -1, 1)
 Str = Replace(Str, "join", "jo&#105;n", 1, -1, 1)
 Str = Replace(Str, "union", "un&#105;on", 1, -1, 1)
 Str = Replace(Str, "where", "wh&#101;re", 1, -1, 1)
 Str = Replace(Str, "insert", "ins&#101;rt", 1, -1, 1)
 Str = Replace(Str, "delete", "del&#101;te", 1, -1, 1)
 Str = Replace(Str, "update", "up&#100;ate", 1, -1, 1)
 Str = Replace(Str, "like", "lik&#101;", 1, -1, 1)
 Str = Replace(Str, "drop", "dro&#112;", 1, -1, 1)
 Str = Replace(Str, "create", "cr&#101;ate", 1, -1, 1)
 Str = Replace(Str, "rename", "ren&#097;me", 1, -1, 1)
 Str = Replace(Str, "count", "co&#117;nt", 1, -1, 1)
 Str = Replace(Str, "chr", "c&#104;r", 1, -1, 1)
 Str = Replace(Str, "mid", "m&#105;d", 1, -1, 1)
 Str = Replace(Str, "truncate", "trunc&#097;te", 1, -1, 1)
 Str = Replace(Str, "nchar", "nch&#097;r", 1, -1, 1)
 Str = Replace(Str, "char", "ch&#097;r", 1, -1, 1)
 Str = Replace(Str, "alter", "alt&#101;r", 1, -1, 1)
 Str = Replace(Str, "cast", "ca&#115;t", 1, -1, 1)
 Str = Replace(Str, "exists", "e&#120;ists", 1, -1, 1)
 Str = Replace(Str,Chr(13),"<br>", 1, -1, 1)
 HTMLEncode = Replace(Str,"'","''", 1, -1, 1)
End Function

'分页函数
'total_page为总页数,ac_page为当前页,display_page为显示的页数，应该为奇数，至少为5，language为语言
'"en"为英文 "cn"为中文
'onclick_str link_str 为链接串（必须有值），前面的实际上优先。格式为其中#page#为替换成页号
'要引用 replace_page
Function page_num_list(ByVal total_page,ByVal curpage,ByVal display_page,ByVal language,ByVal onclick_str, ByVal link_str)
	Dim p_c_id,page_altn(5)
	p_c_id = request.querystring("pid")
	If language = "en" Then
	page_altn(0) = "First"
	page_altn(1) = "Previous"
	page_altn(2) = "Next"
	page_altn(3) = "Last"
	page_altn(4) = "Total: "
	page_altn(5) = ""
	Else
	page_altn(0) = "第1页"
	page_altn(1) = "上一页"
	page_altn(2) = "下一页"
	page_altn(3) = "最后页"
	page_altn(4) = "共"
	page_altn(5) = "页"
	End if
	If curpage = "" Then
		curpage = 1
	Else
		curpage = CInt(curpage)
	End if
'	page_num_list = "<ul>"

	'显示前页开始，当前页大于1
	if curpage > 1 Then
		If onclick_str = "" then
		page_num_list = page_num_list & "<li><a title=""" & page_altn(1) & """" & " href=""" & replace_page(link_str,curpage-1,1) & """>&lt;</a></li>"
		Else
		page_num_list = page_num_list & "<li><a title=""" & page_altn(1) & """" & " href=""" & replace_page(link_str,curpage-1,1) & """ onclick=""" & replace_page(onclick_str,curpage-1,0) & """>&lt;</a></li>"
		End if
	End if

	'显示第1页开始，如果没有显示第1页（即当前页为1＋半数）
	if curpage > (display_page + 1)/2 Then
		If onclick_str = "" Then
		page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,"1",1) & """>1" & "</a></li>"
		else
		page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,"1",1) & """ onclick=""" & replace_page(onclick_str,"1",0) & """>1" & "</a></li>"
		End if
	End If

	'显示中间部分（当前页的前一半、当前页、后一半）
	for page_break_temp_i= (curpage - (display_page -1)/2) to (curpage + (display_page -1)/2)
		if (page_break_temp_i > 0) AND (page_break_temp_i <= total_page) then
			if page_break_temp_i = curpage Then
				If onclick_str = "" then
				page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,page_break_temp_i,0) & """ class=""current"">" & page_break_temp_i & "</a></li>"
				Else 
				page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,page_break_temp_i,0) & """ class=""current"" onclick=""" & replace_page(onclick_str,page_break_temp_i,1) & """>" & page_break_temp_i & "</a></li>"
				End if
			Else
				If onclick_str = "" then
				page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str, page_break_temp_i,0) & """>" & page_break_temp_i & "</a></li>"
				Else 
				page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str, page_break_temp_i,0) & """ onclick=""" & replace_page(onclick_str, page_break_temp_i,1) & """>" & page_break_temp_i & "</a></li>"
				End if
			end if
		end if
	Next

	'显示最后页开始
	if curpage < total_page - (display_page - 1)/2 then	'如果最后页没有出现，即当前页小于（总页数-半）
		If onclick_str = "" Then
		page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,total_page,0) & """>" & total_page & "</a></li>"
		else
		page_num_list = page_num_list & "<li><a href=""" & replace_page(link_str,total_page,0) & """ onclick=""" & replace_page(onclick_str,total_page,1) & """>" & total_page & "</a></li>"
		End if
	End If
	
	'显示下一页开始
	if curpage < total_page then'如果不是最后1页，则显示后页
		If onclick_str = "" Then
		page_num_list = page_num_list & "<li><a title=""" & page_altn(2) & """ href=""" & replace_page(link_str,curpage + 1,0) & """>&gt;</a></li>"
		else
		page_num_list = page_num_list & "<li><a title=""" & page_altn(2) & """ href=""" & replace_page(link_str,curpage + 1,0) & """ onclick=""" & replace_page(onclick_str, curpage + 1,1) & """>&gt;</a></li>"
		End if
	End If
'	page_num_list = page_num_list & "</ul>"
End Function

function replace_page(ByVal ll,ByVal str,Byval ltype)	'为 page_num_list 引用
	if ltype = 1 then
	replace_page = replace(ll,"#page#",str)
	else
	replace_page = replace(ll,"#page#",str)
	End if
end Function

Sub error_flow(str)
	'错误处理流程
	Response.redirect("default.asp")
	Response.End
End sub	

Function SafeRequest(ParaName,ParaType)
	'--- 传入参数 ---
	'ParaName:参数名称-字符型
	'ParaType:参数类型-数字型(1表示以上参数是数字，0表示以上参数为字符)

	Dim ParaValue
	ParaValue=Request(ParaName)
	If ParaType=1 then
		If not isNumeric(ParaValue) Then
			Call error_flow("参数错误")
		End if
		Else
			ParaValue=replace(ParaValue,"'","''")
	End if
	SafeRequest=ParaValue
End Function

Function SafeRequest2(ByVal ParaName,ByVal ParaType,ByVal defaultvalue)
	'--- 传入参数 --- 同SafeRequest
	Dim ParaValue
	ParaValue=Request(ParaName)
	If ParaType=1 then
		If (not isNumeric(ParaValue)) Or ( ParaValue = "") Then
			ParaValue = defaultvalue
		End if
	Else
		ParaValue=replace(ParaValue,"'","''")
	End if
	SafeRequest2=ParaValue
End Function

Function this_filename
	'得到当前文件名
	file_name = Request.ServerVariables("URL")
	file_name_length = len(file_name) - InStrRev(file_name,"/")
	file_name = right(file_name,file_name_length)
	this_filename = file_name
End Function

Function cutStr(str,strlen)
	'去掉所有HTML标记，留下strlen长度的字符（半角）
	if not isnull(str) then
	Dim re
	Set re=new RegExp
	re.IgnoreCase =True
	re.Global=True
	re.Pattern="<(.[^>]*)>"
	str=Replace(str,"&nbsp;"," ")
	str=re.Replace(str,"")	
	set re=Nothing
	Dim l,t,c,i
	l=Len(str)
	t=0
	For i=1 to l
		c=Abs(Asc(Mid(str,i,1)))
		If c>255 Then
			t=t+2
		Else
			t=t+1
		End If
		If t>=strlen Then
			cutStr=left(str,i)&"..."
			Exit For
		Else
			cutStr=str
		End If
	Next
	cutStr=Replace(cutStr,chr(10),"")
	cutStr=Replace(cutStr,chr(13),"")
	end if
End Function

function replace_en_char(str_en)
If Not IsNull(str_en) then
str_en = replace(str_en,"≤","&#8804;")
str_en = replace(str_en,"≥","&#8805;")
str_en = replace(str_en,"℃","&ordm;C")
str_en = replace(str_en,"，",", ")
str_en = replace(str_en,"’","' ")
str_en = replace(str_en,"。",". ")
str_en = replace(str_en,"、",". ")
str_en = replace(str_en,"：",": ")
str_en = replace(str_en,"（","( ")
str_en = replace(str_en,"）",") ")
str_en = replace(str_en,"…","... ")
str_en = replace(str_en,"～","~")
str_en = replace(str_en,"±","&#177;")
str_en = replace(str_en,"■","&#9830;")
str_en = replace(str_en,"△","&#9674;")
str_en = replace(str_en,"◆","&#9830;")
str_en = replace(str_en,"●","&#9830; ")
str_en = replace(str_en,"※","*")
str_en = replace(str_en,"×","&#215;")
str_en = replace(str_en,"－","-")
str_en = replace(str_en,"［","[")
str_en = replace(str_en,"］","]")
str_en = replace(str_en,"Φ","&#934;")
str_en = replace(str_en,"　"," ")
replace_en_char = str_en
End if
end function

function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function

Function mail_str(emailbox)'email编码，反广告
	mail_str=""
	If emailbox <> "" Then
		Dim mail_str_t
		mail_str_t = Split(emailbox,"@")
		mail_str = "<script type=""text/javascript"">" & vbcrlf & "//<![CDATA[" & vbcrlf
		mail_str = mail_str & "var m='<' + 'a class=""em' + 'ail"" href=""mailto:',m0 = '" & mail_str_t(0) & "', m1 = '" & mail_str_t(1) & "', m2='mailto:',m3='@',m4='"">',m5='<'+'/' + 'a' + '>';document.write(m + m0 + m3 + m1 + m4 + m0 + m3 + m1 + m5)"
		mail_str = mail_str & vbcrlf & "//]]>" & vbcrlf & "</script>"
	End if
End Function

Function DateTimeToGMT(sDate) 
'转换时间为GMT(RFC822)格式时间函数 
	Dim dWeek,dMonth 
	Dim strZero,strZone 
	strZero="00" 
	strZone="+0800" 
	dWeek=Array("Sun","Mon","Tue","Wes","Thu","Fri","Sat") 
	dMonth=Array("Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec") 
	DateTimeToGMT = dWeek(WeekDay(sDate)-1)&", "&Right(strZero&Day(sDate),2)&" "&dMonth(Month(sDate)-1)&" "&Year(sDate)&" "&Right(strZero&Hour(sDate),2)&":"&Right(strZero&Minute(sDate),2)&":"&Right(strZero&Second(sDate),2)&" "&strZone 
End Function 

Function GBTOUTF8(Byval a_iNum)
	iHexNum = Trim(a_iNum)
	If iHexNum = "" Then
	Exit Function
	End If
	sResult = ""
	If (iHexNum >127) Then
	sResult = sResult & "&#x" & Hex(iHexNum) & ";"
	Else
	sResult = sResult & chr(iHexNum)
	End If 
	GBTOUTF8=sResult
End Function

Function chinesetoutf8(Byval Chinese)
	sGB = Trim(Chinese)
	For i = 1 to Len (sGB)
	sTemp = Mid(sGB ,i,1)
	iTemp = Asc(sTemp)
	If (iTemp>127 OR iTemp<0) Then
	iUnicode = AscW(sTemp)
	If iUnicode<0 Then
	iUnicode = iUnicode + 65536
	End If
	Else
	iUnicode = iTemp
	End If
	sResult = sResult &GBTOUTF8(iUnicode)
	next
	chinesetoutf8=sResult
End Function

Function sub_nav(ByVal id,ByVal typestr)
	'子导航函数（其中type为"list" "area"，对应当前list.id 当前area.cate_lcode）
	Dim rs,sql,listr
	Set rs = Server.CreateObject("ADODB.Recordset")
	Select Case typestr
		Case "area"
		If Len(id)> 4 Then	'（有 class="current")
			sql = "SELECT * FROM V_Menusbasic where left(sort_id,4)='" & Left(id,4) & "' and from_table='area' and len(sort_id)>4 ORDER BY sort_id, from_tableid, sort_id2"
		Else
			sql = "SELECT * FROM V_Menusbasic where left(sort_id,4)='" & id & "' and ((len(sort_id)=len('" & id & "') and from_table='list') OR (len(sort_id)=6 AND from_table='area')) ORDER BY sort_id, from_tableid, sort_id2"	
		End if
		Case "list"				 '（有 class="current")
		If not isNumeric(id) Then Call error_flow("参数错误")
		sql = "SELECT * FROM V_Menusbasic where left(sort_id,4) in (select sort_id from V_Menusbasic where menuId=" & id & ") and from_table='list' ORDER BY sort_id, from_tableid, sort_id2"
	End Select
	rs.open sql,MM_conn_STRING,1,1
	While Not rs.eof
		listr = "<li"
		If (Len(id)> 4 And id= rs("sort_id")) Or (typestr = "list" And id=CStr(rs("menuID"))) Or (rs("from_table")="list" And this_filename = rs("url")) Then 
			listr = listr & " class=""current"""
		End if
		listr = listr & ">"
		listr = listr & navurl(rs("menuID"),rs("menu"),rs("type_id"),rs("sort_id"),rs("from_table"),rs("url"),rs("minpage"),rs("countpage","")) & "</li>"
		sub_nav = sub_nav & listr
	rs.movenext()
	wend
	rs.close()
	Set rs=nothing
End Function

Function nav_tree(ByVal cate_lcode,ByVal level)	
	'主导航树 cate_lcode 为语言版本起点 "01"为中文 "02"..
	'暂只作了二级子区域导航，不管子区域有多少级，只输出二级
	'level为级数，如果为1则是1级，如果为0为所有
	Dim rs,sql,cate_name,j,cate_url,li_str
	j = len(cate_lcode)/2-1
	nav_tree = ""
	Set rs = Server.CreateObject("ADODB.Recordset")
	If level = 0 Then
	sql = "SELECT * FROM V_Menusbasic where left(sort_id,len('" & cate_lcode & "'))='" & cate_lcode & "' and ( (from_table='area' and type_id<>89 and len(sort_id) >len('" & cate_lcode & "') ) or ( from_table='list' and type_id<>89 ) ) ORDER BY sort_id, from_tableid asc, sort_id2 asc"
	else
	sql = "SELECT * FROM V_Menusbasic where left(sort_id,len('" & cate_lcode & "'))='" & cate_lcode & "' and ( ((len(sort_id)-len('" & cate_lcode & "'))/2 <= " & level & " and from_table='area' and type_id<>89 and len(sort_id) >len('" & cate_lcode & "') ) or ((len(sort_id)-len('" & cate_lcode & "'))/2 < " & level & " and from_table='list' and type_id<>89 ) ) ORDER BY sort_id, from_tableid asc, sort_id2 asc"
	End If
	rs.open sql,MM_conn_STRING,1,1

	Dim i,i1,i2,li_1_t
	i = 1
	i2 = 0
	li_1_t=0
	While Not rs.eof
		cate_url = navurl(rs("menuID"),rs("menu"),rs("style_id"),rs("type_id"),rs("sort_id"),rs("from_table"),rs("url"),rs("minpage"),rs("countpage"),rs("other"))
		cate_name = rs("menu")
		If rs("from_table") = "area" Then
			cate_level = Len(rs("sort_id"))/2 - 1
		Else
			cate_level = Len(rs("sort_id"))/2
		End If
		
		If  rs("from_table") = "area" And cate_level = 1 Then
			li_1_t = li_1_t + 1
			li_str = "<li id=""menu0" & li_1_t & """>"
		Else
			li_str = "<li>"
		End if
		
		If i= 1 Then 
			pre_cate_level = cate_level - Len(cate_lcode)/2
		Else
			rs.MovePrevious()
			If rs("from_table") = "area" Then
				pre_cate_level = Len(rs("sort_id"))/2 - 1
			Else
				pre_cate_level = Len(rs("sort_id"))/2
			End if
			rs.movenext()
		End if

		If pre_cate_level < cate_level Then
			If pre_cate_level <> 0 Then nav_tree = nav_tree & "<ol>" '对于起点不加 "<ul>"
			nav_tree = nav_tree & li_str
			nav_tree = nav_tree & cate_url
		End if
		If pre_cate_level = cate_level Then
			nav_tree = nav_tree & "</li>" & li_str & cate_url
		End If
		If pre_cate_level > cate_level Then
			For i1=pre_cate_level - cate_level To 1 Step -1
				nav_tree = nav_tree & "</li></ol>"
			Next
			nav_tree = nav_tree & "</li>" & li_str
			nav_tree = nav_tree & cate_url
		End If
		If i = rs.recordcount Then
			nav_tree = nav_tree & "</li>"
			For i1=cate_level -1 To j+1 Step -1
				nav_tree = nav_tree & "</ol></li>"
			next
		End If
		i=i+1
		rs.movenext()
	Wend
	rs.close()
	Set rs=nothing
End Function

'区域与列表导航条目<a href=...></a> ，在sub_nav 和 nav_tree 里调用
Function navurl(ByVal menuID,ByVal menu,ByVal style_id,ByVal type_id,ByVal sort_id,ByVal from_table,ByVal url,ByVal minpage,ByVal countpage,ByVal other)
	Dim other1
	If IsNull(other) Then
		other1 = "||"
	Else
		other1 = other
	End if
	If from_table = "area" Then
		If Trim(url) = "" Or IsNull(url) Then
			navurl = "javascript:void(null)"
		else
			If Len(sort_id) = 4 Then
				navurl = url
			Else
				navurl = url & "?" & area_querystingid & "=" & menuID
			End if
		End if
	Else
		If style_id = 99 Then	'特定列表
			navurl = url
		ElseIf type_id =1 Then
			navurl = plist_name & "?" & pro_querystingid & "=" & menuID
		Else
			If countpage <= 1 then
				navurl = page_name & "?" & page_querystingid & "=" & minpage
			Else
				navurl = list_name & "?" & list_querystingid & "=" & menuID
			End if
		End if
	End If
'	If from_table = "area" And Len(sort_id) = 4 Then
'		navurl = "<a class=""P" & sort_id & """ href=""" & navurl & """"  & ">" & menu & "</a>"
'	Else
		navurl = "<a" & a_acstr(page_position_str,from_table,menuID) & " href=""" & navurl & """>" & menu & "</a>"
'	End if
End Function

Function a_acstr(page_position_str,ByVal from_table,ByVal menuID)
	a_acstr = ""
	If page_position_str <> "" Then
		If Split(page_position_str,",")(0) = from_table  And CInt(Split(page_position_str,",")(1)) = menuID Then a_acstr = " class=""ac"""
	End if
End function

Function page_list(ByVal sqltop,ByVal sqlwhere,ByVal pageevery,ByVal pagenum,ByVal newsign,ByVal newsigndate,ByVal dateon,ByVal nostr,ByVal lettercount)
	'取page表里的一些条目列表 此函数不要修改，用于列表页
	'此过程不处理不安全参数
	'在过程 list_out_sub 里用到
	'输出 list_para
	Dim rs,sql,li
	Dim v_new,v_date	'是否显示new标志和日期

	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT " & sqltop & " page.pageid,page.title,page.data_mod,page.img,page.imgwidth,page.imgheight,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,list.date_display_on,list.sign_on,list.style_id,trim(page.content)<>'' as iscon from (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id where page.isscreen=false and " & sqlwhere & " order by page.sort_id desc"
	rs.open sql,MM_conn_STRING,1,1
	if rs.recordcount > 0 Then
		If newsign=true Or rs("sign_on") = true Then v_new = true
		If dateon = True Or rs("date_display_on") = true Then v_date = True
		rs.pagesize = pageevery
		if rs.pagecount < pagenum then
			rs.absolutepage = rs.pagecount
			Else
			rs.absolutepage = pagenum
		end if
		list_para = Array(rs.pagecount,rs.absolutepage)	'如果是list页，则会输出 list_para

		for i = 1 to rs.pagesize
			li = "<li>"
			'图片栏目
			If (rs("style_id") = 1 Or rs("style_id") = 2) And Trim(rs("img")) <> "" Then
				li = li & "<a class=""thumb"" href=""upload/" & rs("img") & """ rel=""lightbox[plants]"" onclick=""javascript:return false""><img class=""imglbox"" src=""upload/" & rs("imgsmall") & """ width=""" & rs("imgsmallwidth") & """ height=""" &  rs("imgsmallheight") & """ alt=""" & HTMLEncode(rs("title")) & """ /> </a>"
			End If
			If (rs("style_id") = 1 Or rs("style_id") = 2) And rs("iscon") = true Then li = li & "<a class=""more"" href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & "</a>"
			If (rs("style_id") = 1 Or rs("style_id") = 2) Then
				li = li & "<span class=""name"">" & HTMLEncode(rs("title")) & "</span>"
			Else
				If lettercount = 0 Or Len(rs("title"))<=lettercount Then
					li_str = HTMLEncode(rs("title"))
					li = li & "<a href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & HTMLEncode(rs("title")) & "</a>"
				Else
					li_str = Left(HTMLEncode(rs("title")),lettercount-1) & "..."
					li = li & "<a title=""" & HTMLEncode(rs("title")) & """ href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & li_str & "</a>"
				End if
	
			End if
			If v_new = True Then
				if DateDiff("d",rs("data_mod").Value,Now) < newsigndate then
					li = li & "<span class=""new""></span>"
				End if
			End If
			If v_date = True Then li = li & "<span class=""date"">" & DoDateTime((rs("data_mod")), 2, 2052) & "</span>"
			li = li & "</li>"
			page_list = page_list & li
			rs.movenext()
			If rs.eof Then Exit for
		next
	Else
		page_list = "<li class=""none""><p>" & nostr & "</p></li>"
	End If
	rs.close()
	Set rs=Nothing
End Function

Function page_list2(ByVal sqltop,ByVal sqlwhere,ByVal pageevery,ByVal pagenum,ByVal newsign,ByVal newsigndate,ByVal dateon,ByVal nostr,ByVal lettercount)
	'lettercount 每行字数，如果为0则不限制，不仅取标题，还会取部分内容
	'取page表里的一些条目列表
	'此过程不处理不安全参数
	'在过程 list_out_sub 里用到
	'输出 list_para
	Dim rs,sql,li,li_str
	Dim v_new,v_date	'是否显示new标志和日期

	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT " & sqltop & " page.pageid,page.title,page.data_mod,page.img,page.imgwidth,page.imgheight,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,list.date_display_on,list.sign_on,list.style_id,trim(page.content)<>'' as iscon,page.content from (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id where page.isscreen=false and " & sqlwhere & " order by top_submit asc,page.sort_id desc"
	rs.open sql,MM_conn_STRING,1,1
	if rs.recordcount > 0 Then
		If newsign=true Or rs("sign_on") = true Then v_new = true
		If dateon = True Or rs("date_display_on") = true Then v_date = True
		rs.pagesize = pageevery
		if rs.pagecount < pagenum then
			rs.absolutepage = rs.pagecount
			Else
			rs.absolutepage = pagenum
		end if
		list_para = Array(rs.pagecount,rs.absolutepage)	'如果是list页，则会输出 list_para

		for i = 1 to rs.pagesize
			li = "<li class=""n" & i & """>"
			'图片栏目
			If (rs("style_id") = 1 or rs("style_id") = 2) And Trim(rs("img")) <> "" Then
				li = li & "<a class=""thumb"" href=""upload/" & rs("img") & """ rel=""lightbox[plants]"" onclick=""javascript:return false""><img class=""imglbox"" src=""upload/" & rs("imgsmall") & """ width=""" & rs("imgsmallwidth") & """ height=""" &  rs("imgsmallheight") & """ alt=""" & HTMLEncode(rs("title")) & """ /> </a>"
			End If
			If (rs("style_id") = 1 or rs("style_id") = 2) And rs("iscon") = true Then li = li & "<a class=""more"" href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & "</a>"
			If (rs("style_id") = 1 or rs("style_id") = 2) Then
				li = li & "<span class=""name"">" & HTMLEncode(rs("title")) & "</span>"
			Else
				If lettercount = 0 Or Len(rs("title"))<=lettercount Then
					li_str = HTMLEncode(rs("title"))
				Else
					li_str = Left(HTMLEncode(rs("title")),lettercount-1) & "..."
				End if
				li = li & "<h2>" & li_str & "</h2>"
			End if
			If v_new = True Then
				if DateDiff("d",rs("data_mod").Value,Now) < newsigndate then
					li = li & "<span class=""new""></span>"
				End if
			End If
			If v_date = True Then li = li & "<span class=""date"">" & DoDateTime((rs("data_mod")), 2, 2052) & "</span>"
			li = li & "<p>" & cutStr(rs("content"),240) & "<a class=""more"" href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """><span>more</span></a></p></li>"
			page_list2 = page_list2 & li
			rs.movenext()
			If rs.eof Then Exit for
		next
	Else
		page_list2 = "<li class=""none""><p>" & nostr & "</p></li>"
	End If
	rs.close()
	Set rs=Nothing
End function

Function first_id(tbtype)
	'系统中的第一个非特定列表或者页面记录
	Dim rs,sql
	Set rs = Server.CreateObject("ADODB.Recordset")
	Select Case tbtype
	Case "list"
		If this_lan = "en" then
			sql = "SELECT top 1 menuID from V_Menusbasic where from_table='list' and type_id<>99 and left(sort_id,2)='02'"
		Else
			sql = "SELECT top 1 menuID from V_Menusbasic where from_table='list' and type_id<>99 and left(sort_id,2)='01'"
		End if
	Case "page"
		If this_lan = "en" then	
			sql = "SELECT top 1 pageid from (page inner join list on page.subid=list.id) inner join area on area.cate_id=list.cate_id where list.style_id<>99 and list.style_id<>1 and page.isscreen=false and left(area.cate_lcode,2) = '02' order by page.pageid asc"
		Else
			sql = "SELECT top 1 pageid from (page inner join list on page.subid=list.id) inner join area on area.cate_id=list.cate_id where list.style_id<>99 and list.style_id<>1 and page.isscreen=false and left(area.cate_lcode,2) = '01' order by page.pageid asc"
		End If
	Case "product"
		If this_lan = "en" then	
			sql = "SELECT top 1 list.id from (list inner join area on area.cate_id=list.cate_id) where area.cate_type_id=1 and left(area.cate_lcode,2)='02' order by area.cate_lcode asc,list.sort_id asc"
		Else
			sql = "SELECT top 1 list.id from (list inner join area on area.cate_id=list.cate_id) where area.cate_type_id=1 and left(area.cate_lcode,2)='01' order by area.cate_lcode asc,list.sort_id asc"
		End If
	End Select
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof Then
		first_id = rs(0)
	Else
		Call error_flow("列表或者页面未添加")
	End If
	rs.close()
	Set rs=nothing
End Function

sub list_out_sub(byval listid,byval pages,ByVal force_newsign,ByVal force_newsigndate,ByVal force_datesort,ByVal force_dateon,byval force_style)
	'输出list页列表，得到数组 page_out force_style = -1 则为非 force
	If (not isNumeric(listid)) Or IsNull(lisid) Or (listid= "") then
		listid = first_id("list")
	End If
	Dim rs,sql,oitems(12)
	Dim v_new,v_dateon
	Dim page_num_tmp
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT V_Menusbasic.*,area.cate_name,area.cate_type_id from V_Menusbasic inner join area on area.cate_lcode = V_Menusbasic.sort_id where menuID=" & listid & " and from_table='list'"
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof then
		oitems(0) = rs("sort_id")			'area_lcode
		oitems(1) = rs("cate_type_id")	'area.cate_type_id
		oitems(2) = rs("cate_name")		'area.cate_name
		oitems(3) = rs("style_id")			'list.style_id
		oitems(4) = rs("menu")				'list.name
		oitems(5) = rs("countpage")		'countpage
		oitems(6) = rs("minpage")			'minpage
		oitems(7) = rs("listdate")			'list.date_display_on
		oitems(8) = rs("listnewsign")		'list.sign_on
		oitems(9) = rs("date_sort")		'list.date_sort
		oitems(10) = rs("url")				'list.otherurl
		oitems(12) = rs("menuID")			'list.id
	Else
		Call error_flow("none list id")
	End if
	rs.close()
	Set rs=Nothing
	If oitems(3) = "99" Then
		response.redirect(oitems(10))
	Else
		If oitems(7) = True Or force_dateon = True Then v_date = True
		If oitems(8) = True Or force_newsign = True Then v_new = True
		If oitems(3) = 1 Or oitems(3)=2 Then
			page_num_tmp = img_num
		Else 
			page_num_tmp = page_num
		End If
		If this_lan = "en" Then
			oitems(11) = page_list(""," list.id="&listid,page_num_tmp,pages,v_new,force_newsigndate,v_date,"comming soon ...",0)						'列表内容，引用page_list函数
		else
			oitems(11) = page_list(""," list.id="&listid,page_num_tmp,pages,v_new,force_newsigndate,v_date,"未输入列表内容",0)						'列表内容，引用page_list函数
		End if
	End if
	page_out = oitems
End sub

sub page_out_sub(ByVal pageid,ByVal force_newsign,ByVal force_newsigndate,ByVal force_datesort)
	'输出页面，得到数组 page_out（上一条id，主题，下一条id，主题 未处理，另用一个过程好些）
	If (not isNumeric(pageid)) Or IsNull(pageid) Or (pageid= "") then
		pageid = first_id("page")
	End If
	Dim rs,sql,oitems(23)
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT page.pageid,page.title,page.keywords,page.content,page.img,page.imgwidth,page.imgheight,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,page.data_creat,page.data_mod,V_Menusbasic.*,area.cate_name,area.cate_type_id from (page inner join V_Menusbasic on page.subid=V_Menusbasic.menuID) inner join area on area.cate_lcode = V_Menusbasic.sort_id where page.pageid=" & pageid & " and V_Menusbasic.from_table='list' and page.isscreen=false"
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof then
		oitems(0) = rs("sort_id")			'area_lcode
		oitems(1) = rs("cate_type_id")	'area.cate_type_id
		oitems(2) = rs("cate_name")		'area.cate_name
		oitems(3) = rs("type_id")			'list.style_id
		oitems(4) = rs("menu")				'list.name
		oitems(5) = rs("countpage")		'countpage
		oitems(6) = rs("minpage")			'minpage
		oitems(7) = rs("listdate")			'list.date_display_on
		oitems(8) = rs("listnewsign")		'list.sign_on
		oitems(9) = rs("date_sort")		'list.date_sort
		oitems(10) = rs("url")				'list.otherurl
		oitems(11) = rs("content")			'page.content		
		oitems(12) = rs("menuID")			'list.id
		'以上与 list_out_sub输出的相同，除了 (11）的内容前者为list列表内容，后者为page.content
		oitems(13) = HTMLEncode(rs("title"))	'page.title
		If rs("keywords") <> "" Then
			oitems(14) = Replace(rs("keywords"),"，",",")		'page.keywords
		Else
			oitems(14) = ""
		End if
		oitems(15) = rs("img")				'page.img
		oitems(16) = rs("imgwidth")		'page.imgwidth
		oitems(17) = rs("imgheight")		'page.imgheight
		oitems(18) = rs("imgsmall")		'page.imgsmall
		oitems(19) = rs("imgsmallwidth")	'page.imgsmallwidth
		oitems(20) = rs("imgsmallheight")'page.imgsmallheight
		oitems(21) = rs("data_creat")		'page.data_creat
		oitems(21) = rs("data_mod")		'page.data_mod
		oitems(22) = rs("pageid")			'page.data_mod
		oitems(23) = "<h1>" & oitems(13) & "</h1>"	'oitems(23)为输出的标题（当与列表名一样，且只一条时不输出）
		If oitems(5) = 1 And oitems(13) = oitems(4) Then oitems(23) = ""
		If this_lan = "en" then
			oitems(11) = replace_en_char(oitems(11))
			oitems(13) = replace_en_char(oitems(13))
			oitems(23) = replace_en_char(oitems(23))
		End if
	Else
		Call error_flow("没有这个页面")
	End if
	rs.close()
	Set rs=Nothing
	page_out = oitems
End Sub

Sub area_out_sub(ByVal cate_id)
	'输出区域 cate_id,cate_lcode,cate_descript1,cate_pro_count
	If (not isNumeric(cate_id)) Or IsNull(cate_id) Or (cate_id= "") then
		Call error_flow("error")
	End If
	Dim rs,sql,oitems(5)
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT cate_id,cate_name,cate_url,cate_lcode,cate_pro_count,cate_descript1 from area where cate_id=" & cate_id
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof Then
		oitems(0) = rs("cate_id")		
		oitems(1) = rs("cate_name")	
		oitems(2) = rs("cate_url")	
		oitems(3) = rs("cate_lcode")		
		oitems(4) = rs("cate_pro_count")			
		oitems(5) = rs("cate_descript1")	
	Else
		Call error_flow("没有这个区域")
	End if
	rs.close()
	Set rs=nothing
	area_out = oitems
End sub

Function area_list_out(ByVal cate_lcode)
	Dim rs_area,sql_area
	Set rs_area = Server.CreateObject("ADODB.Recordset")
	sql_area = "SELECT list.id,list.name from (list inner join area on list.cate_id = area.cate_id) where  left(area.cate_lcode," & Len(cate_lcode) & ")='" & cate_lcode & "' order by area.cate_lcode asc,list.sort_id asc"
	rs_area.open sql_area,MM_conn_STRING,1,1
	If Not rs_area.eof then
		While Not rs_area.eof
			area_list_out = area_list_out & "<div><h4>" & rs_area("name") & "</h4><a class=""more"" href=""" & list_name & "?" & list_querystingid & "=" & rs_area("id") & """><span>more</span></a><ul>" & page_list(""," list.id=" & rs_area("id"),5,1,false,false,false,"未输入列表内容",0) & "</ul></div>"
			rs_area.movenext()
		wend
	End if
	rs_area.close()
	Set rs_area = nothing
End Function

function page_nav(byval force_back,ByVal topstr,ByVal printstr,ByVal backstr)
	'详细页面导航，force_back 列表只有一页是，在该详细页是否显示返回
	page_nav = "<a href=""#top"" class=""pn_top"" title=""" & topstr & """><span>" & topstr & "</span></a><a href=""javascript:window.print()"" class=""pn_print"" title=""" & printstr & """><span>" & printstr & "</span></a>"
	If (force_back = true) Then page_nav = page_nav & "<a href=""javascript:history.go(-1)"" class=""pn_back"" title=""" & backstr & """><span>" & backstr & "</span></a>"
End Function

Function list_nav(byval countpage,byval curpage,ByVal display_page)
	'列表页面导航 countpage 该列表分页数， curpage 当前页面的真实页码
	'display_page 必须为奇数，至少大于5
	Dim language
	If Left(page_out(12),2) = "01" Then
		language = "cn"
	ElseIf Left(page_out(12),2) = "02" Then
		language = "en"
	End If
	list_nav = page_num_list(countpage,curpage,display_page,language,"", list_name & "?" & list_querystingid & "=" & page_out(12) & "&amp;" & list_p_querystingid & "=#page#")
End function

Function page_list_x(ByVal sqltop,ByVal sqlwhere,ByVal pageevery,ByVal pagenum,ByVal newsign,ByVal newsigndate,ByVal dateon,ByVal nostr,ByVal lettercount)
	'lettercount 每行字数，如果为0则不限制
	'取page表里的一些条目列表
	'此过程不处理不安全参数
	'在过程 list_out_sub 里用到
	'输出 list_para
	Dim rs,sql,li,li_str
	Dim v_new,v_date	'是否显示new标志和日期

	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "SELECT " & sqltop & " page.pageid,page.title,page.data_mod,page.img,page.imgwidth,page.imgheight,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,page.content,list.date_display_on,list.sign_on,list.style_id,trim(page.content)<>'' as iscon from (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id where page.isscreen=false and " & sqlwhere & " order by page.sort_id desc"
	rs.open sql,MM_conn_STRING,1,1
	if rs.recordcount > 0 Then
		If newsign=true Or rs("sign_on") = true Then v_new = true
		If dateon = True Or rs("date_display_on") = true Then v_date = True
		rs.pagesize = pageevery
		if rs.pagecount < pagenum then
			rs.absolutepage = rs.pagecount
			Else
			rs.absolutepage = pagenum
		end if
		list_para = Array(rs.pagecount,rs.absolutepage)	'如果是list页，则会输出 list_para

		for i = 1 to rs.pagesize
			li = "<li>"
			'图片栏目
			If (rs("style_id") = 1 or rs("style_id") = 2) And Trim(rs("img")) <> "" Then
				li = li & "<a class=""thumb"" href=""upload/" & rs("img") & """ rel=""lightbox[plants]"" onclick=""javascript:return false""><img class=""imglbox"" src=""upload/" & rs("imgsmall") & """ width=""" & rs("imgsmallwidth") & """ height=""" &  rs("imgsmallheight") & """ alt=""" & HTMLEncode(rs("title")) & """ /> </a>"
			End If
			If (rs("style_id") = 1 or rs("style_id") = 2) And rs("iscon") = true Then li = li & "<a class=""more"" href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & "</a>"
			If (rs("style_id") = 1 or rs("style_id") = 2) Then
				li = li & "<span class=""name"">" & HTMLEncode(rs("title")) & "</span>"
			Else
				If lettercount = 0  Then
					li = li & "<a href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & HTMLEncode(rs("title")) & "</a>"
				Else
					If Len(HTMLEncode(rs("title"))) > lettercount then
						li_str = Left(HTMLEncode(rs("title")),lettercount) & "..."
						li = li & "<a href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """ title=""" & HTMLEncode(rs("title")) & """>" & li_str & "</a>"
					Else
						li = li & "<a href=""" & page_name & "?" & page_querystingid & "=" & rs("pageid") & """>" & HTMLEncode(rs("title")) & "</a>"
					End if
				End if
			End if
			If v_new = True Then
				if DateDiff("d",rs("data_mod").Value,Now) < newsigndate then
					li = li & "<span class=""new""></span>"
				End if
			End If
			If v_date = True Then li = li & "<span class=""date"">" & DoDateTime((rs("data_mod")), 2, 2052) & "</span><div class=""div""><span></span><p>" & cutstr(rs("content"),106) & "</p></div>"
			li = li & "</li>"
			page_list_x = page_list_x & li
			rs.movenext()
			If rs.eof Then Exit for
		next
	Else
		page_list_x = "<li class=""none""><p>" & nostr & "</p></li>"
	End If
	rs.close()
	Set rs=Nothing
End function

Function page_rel_pro(ByVal id,ByVal table_type)
	'id关联id
	'table_type属于哪个表，其值为p 或者s
	page_rel_pro = ""
	Set rs = Server.CreateObject("ADODB.Recordset")
	If table_type="p" then
		sql = "SELECT rels.p_id_val,page.title,rels_descript from rels inner join page on rels.p_id_val = page.pageid where rels.rels_id=1 and rels.s_id_val =" & id
	Else
		sql = "SELECT rels.s_id_val,list.name,rels_descript from rels inner join list on rels.s_id_val = list.id where	rels.rels_id=1 and rels.p_id_val =" & id
	End If
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof Then
		While Not rs.eof
			If table_type="p" then
				page_rel_pro = page_rel_pro & "<li><a href=""" & page_name & "?" & page_querystingid & "=" & rs(0) & """>" & rs(1) & "</a>"
				If Trim(rs("rels_descript")) <> "" Then page_rel_pro = page_rel_pro & "<p>" & replace(Replace(HTMLEncode(rs("rels_descript")),vbCrLF,"<br>"),"  ","&nbsp;&nbsp;") & "</p>"
				page_rel_pro = page_rel_pro & "</li>"
			Else
				page_rel_pro = page_rel_pro & "<li><a href=""" & plist_name & "?" & pro_querystingid & "=" & rs(0) & """>" & rs(1) & "</a>"
				If Trim(rs("rels_descript")) <> "" Then page_rel_pro = page_rel_pro & "<p>" & replace(Replace(HTMLEncode(rs("rels_descript")),vbCrLF,"<br>"),"  ","&nbsp;&nbsp;") & "</p>"
				page_rel_pro = page_rel_pro & "</li>"
			End if
			rs.movenext()
		wend
	End If
End function		
%>
