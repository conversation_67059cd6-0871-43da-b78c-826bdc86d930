<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<%
Dim t_action
t_action =request.querystring("action")

Function j_type_str(ByVal jtype)
Select Case jtype
Case 0
j_type_str = "兼招"
Case -1
j_type_str  = "校园招聘"
Case 1
j_type_str = "社会招聘"
End select
End function

Select Case t_action
Case "del"
	set rs_t=server.createobject("adodb.recordset")
	sql="select dept_id from jobs where id=" & request.querystring("jobid")
	rs_t.open sql,MM_conn_STRING,1,1
	t_temp = rs_t("dept_id")
	rs_t.close()
	Set rs_t=nothing
	sql_command("delete from jobs where id=" & request.querystring("jobid"))
	Call update_dept(t_temp)
	response.redirect("jobs_position.asp")	
Case "stop"
	sql_command("update jobs set stop=not(stop) where id=" & request.querystring("jobid"))	
	response.redirect("jobs_position.asp")	
Case "mod2"
	set rs=server.createobject("adodb.recordset")
	sql="select stop,dept_id,dept_name,name,this_amount,complex_descript,j_type from jobs where id="& request.querystring("jobid")
	rs.open sql,MM_conn_STRING,3,3
	For Each x In Request.Form
		If Request.Form(x) <> "" Then rs(x) = Request.Form(x)
	Next
		rs("dept_name") = dept_name(Request.Form("dept_id"))
	rs.update
	rs.close()
	Set rs=Nothing
	Call update_dept(Request.Form("dept_id"))
	response.redirect("jobs_position.asp")	
Case "add2"
	set rs=server.createobject("adodb.recordset")
	sql="select * from jobs where dept_id=" & request.querystring("deptid")
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	For Each x In Request.Form
		If Request.Form(x) <> "" Then rs(x) = Request.Form(x)
	next
	rs.update
	rs.close()
	set rs=Nothing
	Call update_dept(request.querystring("deptid"))
	response.redirect("jobs_position.asp")
Case "add"
set rs=server.createobject("adodb.recordset")
sql="select id,name from jobs_dept where id="& request.querystring("deptid")
rs.open sql,MM_conn_STRING,1,1
%>
<TABLE border=1 cellPadding=4 cellSpacing=0 width="400" bordercolor="#cccccc" bgcolor="#ffffff" style="border-collapse: collapse;border:dotted;border-width:1px" align=center>
<form method=post name="frm_addjob" action="jobs_position_detail.asp?action=add2&deptid=<%=request.querystring("deptid")%>" onsubmit="return validateadd()">
<tr bgcolor="#cccccc" align=center>
	<td width=100>所属部门</td>
	<td align=left><input type=hidden name="dept_id" value="<%=rs("id")%>"><input type=hidden name="dept_name" value="<%=rs("name")%>"><%=rs("name")%></td>
	<td width=100>职位名称</td>
	<td align=left><input name="name" class=i100 style="overflow-x:visible"></td>
</tr>
<tr align=center>
	<td>招聘人数</td>
	<td align=left colspan=3><input name="this_amount" class=i50 style="ime-mode:disabled"></td>
</tr>
<tr align=center>
	<td>详细要求</td>
	<td colspan=3 align=left><textarea style="width:300px;height:100px;overflow-y:visible" name="complex_descript"></textarea></td>
</tr>
<%If jobs_system_school = True then%>
<tr align=center>
	<td>招聘类型</td>
	<td colspan=3 align=left><select name="j_type" size="1">
		<option value="0" selected="selected">兼招</option>
		<option value="1">社会招聘</option>
		<option value="-1">校园招聘</option>
	</select></td>
</tr>
<%End if%>
<tr align=center><td colspan=4><input type=submit value="增 加" class=i200></td></tr>
</form>
</table>
<%
rs.close()
Set rs=Nothing

Case "view" '//查看
set rs=server.createobject("adodb.recordset")
sql="select id,name,dept_name,this_amount,complex_descript,j_type from jobs where id="& request.querystring("jobid")
rs.open sql,MM_conn_STRING,1,1
%>
<TABLE bgcolor="#ffffff" border=1 cellPadding=4 cellSpacing=0 width="400" bordercolor="#cccccc" style="border-collapse: collapse;border:dotted;border-width:1px" align=center>
<tr bgcolor="#cccccc" align=center>
	<td width=100>所属部门</td>
	<td align=left><%=rs("dept_name")%></td>
	<td width=100>职位名称</td>
	<td align=left><%=rs("name")%></td>
</tr>
<tr align=center>
	<td>招聘人数</td>
	<td colspan=3 align=left><%If rs("this_amount") <> 0 Then response.write(rs("this_amount"))%></td>
</tr>
<tr align=center>
	<td>详细要求</td>
	<td colspan=3 align=left><textarea style="color:#333;width:300px;height:100px;overflow-y:visible;border-width:0px" name="complex_descript"><%=rs("complex_descript")%></textarea></td>
</tr>
<%If jobs_system_school = True then%>
<tr align=center>
	<td>招聘类型</td>
	<td colspan=3 align=left><%=j_type_str(rs("j_type"))%></td>
</tr>
<%End if%>
</table>
<%
rs.close()
Set rs=Nothing

Case "mod"
set rs=server.createobject("adodb.recordset")
sql="select dept_id,dept_name,name,this_amount,complex_descript,j_type from jobs where id="& request.querystring("jobid")
rs.open sql,MM_conn_STRING,1,1
%>
<TABLE bgcolor="#ffffff" border=1 cellPadding=4 cellSpacing=0 width="400" bordercolor="#cccccc" style="border-collapse: collapse;border:dotted;border-width:1px" align=center>
<form name="frm_addjob" method=post action="jobs_position_detail.asp?action=mod2&jobid=<%=request.querystring("jobid")%>" onsubmit="return validateadd()"><input name="stop" value="false" type=hidden>
<tr bgcolor="#cccccc" align=center>
	<td width=100>所属部门</td>
	<td align=left><select name="dept_id" style="width:72px"><%
	set rs1=server.createobject("adodb.recordset")
	sql1="select id,name from jobs_dept order by id asc"
	rs1.open sql1,MM_conn_STRING,1,1
	While Not rs1.eof
	%>
<option value=<%=rs1("id")%><%If rs1("id")= rs("dept_id") Then response.write(" selected")%>><%=rs1("name")%></option>
<%rs1.movenext()
wend%>
	</select></td>
	<td width=100>职位名称</td>
	<td align=left><input name="name" class=i100 style="overflow-x:visible" value="<%=rs("name")%>"></td>
</tr>
<tr align=center>
	<td>招聘人数</td>
	<td colspan=3 align=left><input name="this_amount" class=i50 value="<%=rs("this_amount")%>"> 0为不限制人数</td>
</tr>
<tr align=center>
	<td>详细要求</td>
	<td colspan=3 align=left><textarea style="width:300px;height:100px;overflow-y:visible" name="complex_descript"><%=rs("complex_descript")%></textarea></td>
</tr>
<%If jobs_system_school = True then%>
<tr align=center>
	<td>招聘类型</td>
	<td colspan=3 align=left><select name="j_type" size="1">
		<option value="0"<%If rs("j_type") = 0 Then  response.write(" selected=""selected""")%>>兼招</option>
		<option value="1"<%If rs("j_type") = 1 Then  response.write(" selected=""selected""")%>>社会招聘</option>
		<option value="-1"<%If rs("j_type") = -1 Then  response.write(" selected=""selected""")%>>校园招聘</option>
	</select></td>
</tr>
<%End if%>
<tr align=center><td colspan=4><input type=submit value="修 改" class=i200></td></tr>
</form>
</table>
<%
rs.close()
Set rs = Nothing
End Select

Sub update_dept(deptid)
	'更新jobs_dept表中have_jobs
	Dim have_jobs
	Dim rs,sql
	set rs=server.createobject("adodb.recordset")
	sql="select count(*) from jobs where dept_id=" & deptid
	rs.open sql,MM_conn_STRING,1,1
	have_jobs = rs(0)
	rs.close()
	Set rs=Nothing
	sql_command("update jobs_dept set have_jobs=" & have_jobs) & " where id=" & deptid
End Sub

Function dept_name(deptid)
	'从deptid得到部门名称
	Dim rs,sql,temp
	set rs=server.createobject("adodb.recordset")
	sql="select name from jobs_dept where id=" & deptid
	rs.open sql,MM_conn_STRING,1,1
	temp=rs("name")
	rs.close()
	Set rs=Nothing
	dept_name = temp
End function
%>