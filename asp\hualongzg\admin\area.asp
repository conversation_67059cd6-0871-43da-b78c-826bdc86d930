<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%'area 移到最上，最下的移动的函数要修改。父区域修改区域类别时，子区域的cate_type_id也要修改
dim MM_editAction,MM_editAction_0
MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))

If (Request.QueryString <> "") Then
  MM_editAction_0 = MM_editAction & "?" & Request.QueryString
  else
  MM_editAction_0 = MM_editAction
End If
dim sele_cate_id	'当前选定类别
if request.querystring("id") <> "" then sele_cate_id = request.querystring("id")

if sele_cate_id <> "" AND is_cate_addpro(sele_cate_id) = True Then
	pro_name_str = cate_type_list_name(sele_cate_id)
End if

If (Request.QueryString("proid") <> "") Then 
	Dim rsMODPROID
	Set rsMODPROID = Server.CreateObject("ADODB.Recordset")
	sqlMODPROID = "SELECT name,id,trademark_id,cate_id FROM list WHERE id = " & Request.QueryString("proid")
	rsMODPROID.open sqlMODPROID,MM_conn_STRING,1,1
end if

if request.form("cate") <> "" AND request.form("sel")<>"" then call form_sele
if request.form("cate") <> "" AND request.form("del")<>"" then call form_del
if request.form("cate") <> "" AND request.form("mod")<>"" then call form_mod
if request.form("add") <> "" AND request.form("newcatename")<>"" then call form_add

if (request.form("up") <> "" or request.form("down") <> "" or request.form("bottom") <> "" or request.form("top") <> "") then 
	dim del_type
	if request.form("up") <> "" then del_type = "up"
	if request.form("down") <> "" then del_type = "down"
	if request.form("bottom") <> "" then del_type = "bottom"
	if request.form("top") <> "" then del_type = "top"
	if request.form("frmname") = "cate" then
		call form_sort(ssplit(request.form("cate"),"-",1),del_type,"area")
	elseif request.form("frmname") = "pro" then
		call form_sort(request.form("proname"),del_type,"list")
	end if
end if

if request.form("selpro")<>"" then call form_selpro
if request.form("delpro")<>"" then call form_delpro
if request.form("modpro")<>"" then call form_modpro
if request.form("addpro")<>"" then call form_addpro

sub form_sort(i,del_type,table)
	'类或者产品排序，i为类或者产品编号,del_type值: up,down,top,bottom 上、下、顶、底
	'table 为 area 或者 list
	dim rs,sql,alert_str,sort_id,fid,name_str

	'首先得到编号列值
	set rs=server.createobject("adodb.recordset")
	if table = "area" then
		name_str = cate_name_str
		sql="select cate_lcode as sort_id,cate_fid as fid from " & table & " where cate_id="&i
	else
		name_str = pro_name_str
		sql="select sort_id,cate_id as fid from " & table & " where id=" &i
	end if
	rs.open sql,MM_conn_STRING
	sort_id = rs("sort_id")
	fid = rs("fid")
	rs.close()
'	set rs=nothing

	if table = "area" then
		select case del_type
		case "top"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode<'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode asc"
		alert_str = "这个" & name_str & "已经在同级最上方"
		case "up"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode<'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode desc"
		alert_str = "这个" & name_str & "已经在同级最上方"
		case "down"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode>'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode asc"
		alert_str = "这个" & name_str & "已经在同级最下方"
		case "bottom"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode>'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode desc"
		alert_str = "这个" & name_str & "已经在同级最下方"
		end select
	elseif table = "list" then
		select case del_type
		case "top"
		sql = "select top 1 id, sort_id from " & table & " where sort_id<" & sort_id & " AND cate_id=" & fid & " order by sort_id asc"
		alert_str = "这个" & name_str & "已经在最上方"
		case "up"
		sql = "select top 1 id, sort_id from " & table & " where sort_id<" & sort_id & " AND cate_id=" & fid & " order by sort_id desc"
		alert_str = "这个" & name_str & "已经在最上方"
		case "down"
		sql = "select top 1 id, sort_id from " & table & " where sort_id>" & sort_id & " AND cate_id=" & fid & " order by sort_id asc"
		alert_str = "这个" & name_str & "已经在最下方"
		case "bottom"
		sql = "select top 1 id, sort_id from " & table & " where sort_id>" & sort_id & " AND cate_id=" & fid & " order by sort_id desc"	
		alert_str = "这个" & name_str & "已经在最下方"
		end select
	end if

	rs.open sql,MM_conn_STRING
	if rs.eof then
		rs.close()
		set rs=nothing
		Response.Write "<script language=javascript>alert('" & alert_str & "');;history.back();</script>"
		response.end
	else
		dim new_sort_id, new_i
		dim rs2,id_i
		dim ids,ids_new	'二个id数组

		if table = "area" then
			new_sort_id = rs("cate_lcode")
			new_i = rs("cate_id")
			'执行sql将i,new_i这二行的sort_id互换
			set rs2=server.createobject("adodb.recordset")
			sql = "select cate_id as id from " & table & " where left(cate_lcode,len('" & new_sort_id & "'))='" & sort_id &"'"
			sql_new = "select cate_id as id from " & table & " where left(cate_lcode,len('" & sort_id & "'))='" & new_sort_id &"'"
			rs2.open sql,MM_conn_STRING
			id_i=0
			while not rs2.eof
				if id_i = 0 then
					ids = rs2("id")
					else
					ids = ids & "," & rs2("id")
				end if
			rs2.movenext()
			id_i= id_i + 1
			wend
			rs2.close()

			rs2.open sql_new,MM_conn_STRING
			id_i=0
			while not rs2.eof
				if id_i = 0 then
					ids_new = rs2("id")
					else
					ids_new = ids_new & "," & rs2("id")
				end if
			rs2.movenext()
			id_i= id_i + 1
			wend
			rs2.close()
			set rs2=nothing

			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING

			sql = "update " & table & " set cate_lcode = '" & new_sort_id & "'+right(cate_lcode,len(cate_lcode)-len('" & new_sort_id & "')) where cate_id in (" & ids & ")"

			sql2 = "update " & table & " set cate_lcode = '" & sort_id & "'+right(cate_lcode,len(cate_lcode)-len('" & sort_id & "')) where cate_id in (" & ids_new & ")"
			conn.execute sql
			conn.execute sql2
			set conn=nothing
			response.redirect("area.asp")
		else	'如果是产品排序
			new_sort_id = rs("sort_id")
			new_i = rs("id")
			'执行sql将i,new_i这二行的sort_id互换
			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING
			sql = "update " & table & " set sort_id = " & new_sort_id & " where id=" &i
			sql2 = "update " & table & " set sort_id = " & sort_id & " where id=" &new_i
			conn.execute sql
			conn.execute sql2
			set conn=nothing
			response.redirect("area.asp?id=" & fid & "&proid=" & i)
		end if

	end if
end sub

sub form_add
	'增加子类
	if request.form("cate") <> "" then
		if cint(ssplit(request.form("cate"),"-",3)) = cate_level_max then
		Response.Write "<script language=javascript>alert('最多" & cate_level_max-1 & "级" & cate_name_str & "，不能在这个" & cate_name_str & "下增加子" & cate_name_str & "！');;history.back();</script>"
		response.end
		end if

		if is_cate_pro(ssplit(request.form("cate"),"-",1)) = true then
		Response.Write "<script language=javascript>alert('这个" & cate_name_str & "下有" & pro_name_str & "，不能在这个" & cate_name_str & "下增加子" & cate_name_str & "！');;history.back();</script>"
		response.end
		end if

		dim rs,sql,lcodetmp
		set rs=server.createobject("adodb.recordset")
		sql="select * from area"
		rs.open sql,MM_conn_STRING,3,3
		rs.addnew
		lcodetmp= new_cate_lcode(ssplit(request.form("cate"),"-",2),ssplit(request.form("cate"),"-",1))
		rs("cate_lcode") = lcodetmp
		rs("cate_fid") = ssplit(request.form("cate"),"-",1)
		If ssplit(request.form("cate"),"-",3) = 1 Then
			rs("cate_type_id") = request.form("newcate_type_id")
		else
			rs("cate_type_id") = ssplit(request.form("cate"),"-",4)
		End if
		rs("cate_name") = request.form("newcatename")
		if pro_english_on = true then rs("cate_name_en") = request.form("newcatename_en")
		rs("cate_level") = cint(ssplit(request.form("cate"),"-",3)) +1
		rs.update
		rs.close()
		Set rs=nothing
		If ssplit(request.form("cate"),"-",3) <> 1 Then		'对于非1级区域更新其cate_url，认cate_lcode为参数
			Call update_cate_url(lcodetmp)
		End if
		response.redirect(MM_editAction_0)
	else		'增加顶级类
		If top_node <> "" Then
			if cate_top_add = true then
				set rs=server.createobject("adodb.recordset")
				sql="select * from area"
				rs.open sql,MM_conn_STRING,3,3
				rs.addnew
				rs("cate_lcode") = new_cate_lcode(top_node,top_node_id)
				rs("cate_fid") = top_node_id
				rs("cate_name") = request.form("newcatename")
				if pro_english_on = true then rs("cate_name_en") = request.form("newcatename_en")
				rs("cate_level") = top_node_level + 1
				rs.update
			end if
		else
			if cate_top_add = true then
			set rs=server.createobject("adodb.recordset")
			sql="select * from area"
			rs.open sql,MM_conn_STRING,3,3
			rs.addnew
			rs("cate_lcode") = new_cate_lcode("0",0)
			rs("cate_fid") = 0
			rs("cate_name") = request.form("newcatename")
			if pro_english_on = true then rs("cate_name_en") = request.form("newcatename_en")
			rs("cate_level") = 1
			rs.update
			end if
		End if
			response.redirect(MM_editAction_0)
	end if
end sub

sub form_sele
	'选定类
	dim str
	str = ssplit(request.form("cate"),"-",1)
	response.redirect("area.asp?id=" & str)
end sub

sub form_del
	'删除类
	dim str,rs,sql
	str = ssplit(request.form("cate"),"-",1)

		'如果有子类或者产品，则不允许删除
	set rs=server.createobject("adodb.recordset")
	sql = "select * from area where cate_fid=" & str & " or (cate_id=" & str & " AND cate_pro_sign = true)"
	rs.open sql,mm_conn_string,1,1
	if not rs.eof then
		Response.Write "<script language=javascript>alert('" & cate_name_str & "下有子" & cate_name_str & "或者" & pro_name_str & "，请先删除子" & cate_name_str & "和" & pro_name_str & "');;history.back();</script>"
		response.end
	else
		rs.close()
		set rs=nothing
	end if

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	sql="delete from area where cate_id="&str
	conn.execute sql
	set conn=nothing
	response.redirect("area.asp")
end sub

sub form_mod
	'修改区域名称与区域类别
	set rs=server.createobject("adodb.recordset")
	sql="select cate_name,cate_name_en,cate_level from area where cate_id=" & ssplit(request.form("cate"),"-",1)
	rs.open sql,MM_conn_STRING,3,3
	rs("cate_name")=request("catename")
	if pro_english_on = true then rs("cate_name_en")=request("catename_en")
	rs.update
	If rs("cate_level") = 2 Then
		Call up_cate_type_id(ssplit(request.form("cate"),"-",1),request("cate_type_id"))
		'更新所有子区域
	End If
	rs.close()
	Set rs=nothing
	response.redirect(MM_editAction_0)
end sub

sub form_delpro
	dim cate_id
	set rs=server.createobject("adodb.recordset")
	sql = "select cate_id,sub_img,sub_attachment from list where id="&request.form("proname")
	rs.open sql,MM_conn_STRING,1,1
	cate_id = rs("cate_id")

    Set File = CreateObject("Scripting.FileSystemObject")
	fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
	set fdir = File.getfile(fdir)
	set fdir = fdir.parentfolder
	set fdir = fdir.parentfolder								'获取父父目录
	if trim(rs("sub_img")) <> "" then
		ImagePath = fdir & "\img\" & rs("sub_img")
		if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
	end if
	if trim(rs("sub_attachment")) <> "" then
		ImagePath = fdir & "\img\" & rs("sub_attachment")
		if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
	end if
	set file=nothing

	rs.close()
	set rs=nothing

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	sql="delete from list where id="&request.form("proname")
	conn.execute sql
	set conn=nothing

	'更新类别的产品数量和cate_pro_sign值
	call update_procount(cate_id)
	response.redirect(MM_editAction_0)
end sub

sub form_selpro
	response.redirect("area.asp?id=" & request.form("cate_id") & "&proid=" & request.form("proname"))
end sub

sub form_modpro
	'修改产品（包括名称、品牌）
	set rs=server.createobject("adodb.recordset")
	sql="select name,trademark_id,style_id,date_display_on,sign_on,date_sort from list where id=" & request.form("proname")
	rs.open sql,MM_conn_STRING,3,3
	rs("name")=request("modproname")
	rs("style_id") = request.form("modstyle_id")
	rs("date_display_on") = request.form("moddate_display_on")
	rs("date_sort") = request.form("moddate_sort")
	rs("sign_on") = request.form("modsign_on")
	if pro_trademark_on = 1 then rs("trademark_id") = request.form("trademark_id")
	rs.update
	response.redirect("area.asp?id=" & request.form("cate_id") & "&proid=" & request.form("proname"))
end sub

sub form_addpro
	set rs=server.createobject("adodb.recordset")
	sql="select name,cate_id,trademark_id,sort_id,sort_id2,style_id,date_display_on,sign_on,date_sort from list where cate_id=" & request.form("cate_id") 
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	rs("cate_id") = request.form("cate_id")
	rs("style_id") = request.form("newstyle_id")
	if pro_trademark_on = 1 then	rs("trademark_id") = request.form("trademark_id")
	rs("name") = request.form("newproname")
	rs("date_display_on") = request.form("newdate_display_on")
	rs("date_sort") = request.form("newdate_sort")
	rs("sign_on") = request.form("newsign_on")
	rs.update
	rs("sort_id") = rs.bookmark
	rs("sort_id2") = maxmin("id","list","max") + 1	'此处要改，取最大ID号好些
	rs.update
	rs.close()
	set rs=nothing

	'更新类别的产品数量和cate_pro_sign值
	call update_procount(request.form("cate_id"))
	response.redirect(MM_editAction_0)
end sub

Function type_list_default(cate_id)
	set rs=server.createobject("adodb.recordset")
	sql= "select type_list_default from area_type inner join area on area.cate_type_id=area_type.type_id where area.cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof Then
		type_list_default = rs("type_list_default")
	Else
		type_list_default = 0
	End if
End function
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">区域与列表管理</h1>
<SCRIPT language=javascript>
function sid(str,obj){
	if(obj.value =='99'){
		obj.next('br',0).hide();
		obj.next('span',0).hide();
		obj.next('span',1).hide();
		obj.next('span',2).hide();
		obj.next('select',0).hide();
		obj.next('select',1).hide();
		obj.next('select',2).hide();
	}else{
		obj.next('br',0).show();
		obj.next('span',0).show();
		obj.next('span',1).show();
		obj.next('span',2).show();
		obj.next('select',0).show();
		obj.next('select',1).show();
		obj.next('select',2).show();
	}
}

function chkselect(form1)
{
    if (document.form1.cate.value == "")
		{
		alert("请选择<%=cate_name_str%>");
		document.form1.cate.focus();
		return false;
		}
return true;
}

function chkdel()
{
    if (document.form1.cate.value == "")
		{
		alert("请选择<%=cate_name_str%>");
		document.form1.cate.focus();
		return false;
		}
		return confirm('确定删除这个<%=cate_name_str%>？');
		return( true ) ;
}

function chkmodcate()
{
	 if (document.form1.cate.value == "")
		{
		alert("请选择<%=cate_name_str%>");
		document.form1.cate.focus();
		return false;
		}
	 if (document.form1.catename.value == "")
		{
		alert("请输入<%=cate_name_str%>名称");
		document.form1.catename.focus();
		return false;
		}
return true;
}

function chkaddcate()
{

	 if ((document.form1.cate.value == "") && (document.form1.newcatename.value != ""))
		{
<%if cate_top_add =false then%>
		return confirm('顶级<%=cate_name_str%>不允许添加');
<%else%>
		return confirm('您没有选择父<%=cate_name_str%>，是否加入顶级<%=cate_name_str%>？');
<%end if%>
		return( true ) ;
		}

    if (document.form1.newcatename.value == "")
		{
		alert("请输入<%=cate_name_str%>名称");
		document.form1.newcatename.focus();
		return false;
		}
}


function chkselpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择<%=pro_name_str%>");
		document.frmpro.proname.focus();
		return false;
		}
return true;
}

function chkdelpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择<%=pro_name_str%>");
		document.frmpro.proname.focus();
		return false;
		}
		return confirm('确定删除这个<%=pro_name_str%>？');
		return( true ) ;
}

function chkmodpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择<%=pro_name_str%>");
		document.frmpro.proname.focus();
		return false;
		}

    if (document.frmpro.modproname.value == "")
		{
		alert("请输入内容");
		document.frmpro.modproname.focus();
		return false;
		}
<%if pro_trademark_on = 1 then%>
    if (document.frmpro.trademark_id.value == "")
		{
		alert("请选择品牌");
		document.frmpro.trademark_id.focus();
		return false;
		}
<%end if%>
}
function chkaddpro()
{
<%if pro_trademark_on = 1 then%>
    if (document.frmpro.trademark_id.value == "")
		{
		alert("请选择品牌");
		document.frmpro.trademark_id.focus();
		return false;
		}
<%end if%>

   if (document.frmpro.newproname.value == "")
		{
		alert("请输入新<%=pro_name_str%>名称");
		document.frmpro.newproname.focus();
		return false;
		}

}
function viewtypeid(){
	if(document.form1.cate.value!='' && document.form1.cate.value.toString().split("-")[2] == 1){
		if($("newtypeid")){$("newtypeid").style.display="block";}
	}else{
		if($("newtypeid")){$("newtypeid").style.display="none";}
	}
}
//-->
</SCRIPT>
<table width=100% height=100% cellpadding=2 border=0 class=table0>
<tr valign=top>
	<td class=td0 width=50%><%=cate_name_str%>管理：<%if request.querystring("id")<>"" then response.write("<font color=#ff0000>" & catename(request.querystring("id")) & "</font>")%><br>

<table width=100% height=100% cellpadding=2 border=0 class=table0>
<form name="form1" method="POST" action="<%=(MM_editAction)%>?id=<%=(request.querystring("id"))%>">
<input type=hidden name=frmname value="cate">
<tr valign=top align=left>
<td class=td0 width=100><select name="cate" size=<%=cate_select_size%> onclick="viewtypeid()">
<option value="">== <%=cate_name_str%>列表 ==</option>
<%
dim rscate
set rscate = Server.CreateObject("ADODB.Recordset")
If top_node <> "" Then
rscate_s = "select cate_name,cate_id,cate_lcode,cate_level,cate_sort_id,cate_pro_sign,cate_type_id from area where left(cate_lcode,len('" & top_node & "')) = '" & top_node & "' AND len(cate_lcode) > len('" & top_node & "') order by cate_lcode asc"
else
rscate_s = "select cate_name,cate_id,cate_lcode,cate_level,cate_sort_id,cate_pro_sign,cate_type_id from area order by cate_lcode asc"
End if
rscate.open rscate_s,MM_conn_STRING,1,1
while not rscate.eof
dim cate_dot,cate_dot_i
	cate_dot = ""
	cate_dot_i = rscate("cate_level") -1
	cate_dot_i2 = 0
	while cate_dot_i > 0 
		if cate_dot_i2 = 0 then
		cate_dot = "　－"
		else
		cate_dot = "　" & cate_dot
		end if
		cate_dot_i = cate_dot_i - 1
		cate_dot_i2 = cate_dot_i2 + 1
	wend 
%>
<option<%if sele_cate_id = cstr(rscate("cate_id")) then response.write(" selected")%> value="<%=rscate("cate_id")%>-<%=rscate("cate_lcode")%>-<%=rscate("cate_level")%>-<%=rscate("cate_type_id")%>"><%=cate_dot%><%=rscate("cate_name")%></option>
<%
rscate.movenext()
wend
rscate.close()
set rscate = nothing
%>
</select>
</td>
	<td class=td0>
<input type="submit" value="选定<%=cate_name_str%>" name=sel onclick="return chkselect(form1)"><br>
<%if cate_mana_on = true then	'允许类管理%>
	<input type="submit" value="删除<%=cate_name_str%>" name=del onclick="return chkdel()">
	<br><br>
	名称 <input type="text" name="catename" size=10<%if request.querystring("id")<>"" then response.write(" value=""" & catename(request.querystring("id")) & """")%>><br>
	<%If request.querystring("id") <> "" Then
		If cate_level(request.querystring("id")) =2 then%>
	类型 <select name="cate_type_id">
	<%dim rscatetypelist,sql
	set rscatetypelist=server.createobject("adodb.recordset") 
	sql="select type_id,type_name from area_type where type_on=true order by type_id asc"
	rscatetypelist.open sql,MM_conn_STRING,1,1 
	While Not rscatetypelist.eof
		response.write("<option value=""" & rscatetypelist("type_id") & """")
		If rscatetypelist("type_id") = cate_type(request.querystring("id")) then response.write (" selected=""selected""")
		response.write(">" & rscatetypelist("type_name")  & "</option>")
	rscatetypelist.movenext()
	Wend 
	rscatetypelist.close()
	set rscatetypelist=Nothing
	%>
	</select><br />
	<%End If
	End IF%>
	<%if pro_english_on = true then%>
		<input type="text" name="catename_en" size=15<%if request.querystring("id")<>"" then response.write(" value=""" & catename_en(request.querystring("id")) & """")%>><font color=#ff0000 class=p12>(英文名)</font><br>
	<%end if%>
	　 　<input type="submit" value="修改<%=cate_name_str%>" name=mod onclick="return chkmodcate();">

	<br><br>
	名称 <input type="text" name="newcatename" size=10><br>
	<div id="newtypeid" style="display:none">类型 <select name="newcate_type_id">
	<%set rscatetypelist=server.createobject("adodb.recordset") 
	sql="select type_id,type_name from area_type where type_on=true order by type_id asc"
	rscatetypelist.open sql,MM_conn_STRING,1,1 
	While Not rscatetypelist.eof
		response.write("<option value=""" & rscatetypelist("type_id") & """")
		If rscatetypelist("type_id") = 0 then response.write (" selected=""selected""")
		response.write(">" & rscatetypelist("type_name")  & "</option>")
	rscatetypelist.movenext()
	Wend 
	rscatetypelist.close()
	set rscatetypelist=Nothing
	%>
	</select><br /></div>
	<%if pro_english_on = true then%>
		<input type="text" name="newcatename_en" size=15><font color=#ff0000 class=p12>(英文名)</font><br>	
	<%end if%>
	　 　<input type="submit" value="增加<%=cate_name_str%>" name=add onclick="return chkaddcate()">
	<%if cate_sort_on = true then%>
	<br><br>
	排序：<br>
	<input type="submit" value="顶部" title="把这个<%=cate_name_str%>移动到所在级别的最上方" name=top onclick="return chkselect(form1)">↑↑　<input type="submit" value="向上" name=up onclick="return chkselect(form1)">↑<br>
	<input type="submit" value="底部" title="把这个<%=cate_name_str%>移动到所在级别的最下方" name=bottom onclick="return chkselect(form1)">↓↓　<input type="submit" value="向下" name=down onclick="return chkselect(form1)">↓<br>
	<%end if%>
<%end if%>
	</td>
</tr>
</form>
</table>

	</td>
<!-- 如果选定了可加列表的区域 -->
<%if sele_cate_id <> "" AND is_cate_addpro(sele_cate_id) = True Then

dim rspro
set rspro = Server.CreateObject("ADODB.Recordset")
rspro_s = "select id,name from list where cate_id=" & sele_cate_id & " order by sort_id asc"
rspro.open rspro_s,MM_conn_STRING,1,1
	if not rspro.eof then
		is_pro_list = true	'当前类下有产品
		else
		is_pro_list = false
	end if
%>
	<td style="font-size:0;width:1px;boder-left:1px dashed #333;border-right:0;border-top:0;border-bottom:0"></td>
	<td class=td0 width=50%><%
	if is_pro_list = true then
		if request.querystring("proid") <> "" then '选择了产品
		response.write("本" & cate_name_str & pro_name_str & "管理：<font color=#ff0000>" & proname(request.querystring("proid"))(0) &  "</font><br>")
		else
		response.write("本" &  cate_name_str & pro_name_str & "管理:<br>")
		end if
	else
	response.write("本" & cate_name_str & "暂无" & pro_name_str & "，请添加<br>")
	end if%>
<table width=100% height=100% cellpadding=2 border=0 class=table0>
<form name="frmpro" method="POST" action="<%=(MM_editAction)%>?id=<%=(request.querystring("id"))%>">
<input type=hidden name=frmname value="pro">
<input type=hidden name="cate_id" value="<%=(request.querystring("id"))%>">
<tr valign=top align=left>
<td class=td0 width=30><%
	if is_pro_list = true then
	%>
	<select name="proname" size=<%=cate_select_size%>>
	<%
	while not rspro.eof%>
	<option value="<%=rspro("id")%>"><%=rspro("name")%></option>
	<%
	rspro.movenext()
	wend
	%>
	</select><%
	end if
rspro.close()
set rspro=nothing
%>
</td>
<td class=td0><%
	if is_pro_list = true then'如果当前类下有产品
	%>
	<input type="submit" value="选定<%=pro_name_str%>" name=selpro onclick="return chkselpro()"><br>
	<input type="submit" value="删除<%=pro_name_str%>" name=delpro onclick="return chkdelpro()">
	<br><br>
	名称 <input type="text" name="modproname" style="width:170px" size=28<%if request.querystring("proid")<>"" then response.write(" value=""" & proname(request.querystring("proid"))(0) & """" )%>><br>
	类型 <select name="modstyle_id" onclick="sid('modstyle_id',this)" style="width:48px">
		<%dim rslisttypelist
		set rslisttypelist=server.createobject("adodb.recordset") 
		sql="select style_id,left(style_name,len(style_name)-2) as style_name,style_descript from list_style where style_on=true order by style_id asc"
		rslisttypelist.open sql,MM_conn_STRING,1,1 
		While Not rslisttypelist.eof
			response.write("<option title=""" & rslisttypelist("style_descript") & """ value=""" & rslisttypelist("style_id") & """")
			response.write listslectstr(1,rslisttypelist("style_id"))
			response.write(">" & rslisttypelist("style_name")  & "</option>")
		rslisttypelist.movenext()
		Wend 
		rslisttypelist.close()
		set rslisttypelist=Nothing
		%>
	</select>　
	<span>日期</span> <select name="moddate_display_on" style="width:48px">
		<option value="1" title="栏目里显示信息输入的日期"<%=listslectstr(2,1)%>>显示</option>
		<option value="0"<%=listslectstr(2,0)%>>不显示</option>
	</select><br />
	<span>新标</span> <select name="modsign_on" style="width:48px">
		<option value="1"<%=listslectstr(3,1)%> title="对于新输入的信息显示醒目的新标志">显示</option>
		<option value="0"<%=listslectstr(3,0)%>>不显示</option>
	</select>　
	<span>排序</span> <select name="moddate_sort" style="width:48px">
		<option value="0"<%=listslectstr(4,0)%> title="缺省最新输入的显示在前面">倒序</option>
		<option value="1"<%=listslectstr(4,1)%> title="缺省最先输入的显示在前面">顺序</option>
	</select><br />
	<input type="submit" value="修改<%=pro_name_str%>" name=modpro onclick="return chkmodpro();">

	<br><br><%
	end if%>
	<%if pro_trademark_on = 1 then	'如果本系统支持品牌%>
	<select name="trademark_id">
	<option<%if request.querystring("proid")="" then response.write(" selected")%>>请选择品牌</option>
	<%
	set rs=server.createobject("adodb.recordset")
	sql="select id,name & ' ' & name_en as name from pro_trademark order by id asc"
	rs.open sql,MM_conn_string,1,1
	while not rs.eof
	if request.querystring("proid")<>""then
		if rs("id") = rsMODPROID("trademark_id") then
			response.write("<option value=" & rs("id") & " selected>" & rs("name") & "</option>")
			else
			response.write("<option value=" & rs("id") & ">" & rs("name") & "</option>")
		end if
		else
	response.write("<option value=" & rs("id") & ">" & rs("name") & "</option>")
	end if
	rs.movenext()
	wend
	rs.close
	set rs=nothing
	%>
	</select><br>
	<%end if									'如果本系统支持品牌%>
名称 <input type="text" name="newproname" size=15 style="width:170px"><br>	
	类型 <select name="newstyle_id" onclick="sid('newstyle_id',this)" style="width:48px">
	<%
	set rslisttypelist=server.createobject("adodb.recordset") 
	sql="select style_id,left(style_name,len(style_name)-2) as style_name,style_descript from list_style where style_on=true order by style_id asc"
	rslisttypelist.open sql,MM_conn_STRING,1,1 
	While Not rslisttypelist.eof
		response.write("<option title=""" & rslisttypelist("style_descript") & """ value=""" & rslisttypelist("style_id") & """")
		response.write(">" & rslisttypelist("style_name")  & "</option>")
	rslisttypelist.movenext()
	Wend 
	rslisttypelist.close()
	set rslisttypelist=Nothing
	%>
	</select>　
	<span>日期</span> <select name="newdate_display_on" style="width:48px">
		<option value="1" title="栏目里显示信息输入的日期">显示</option>
		<option value="0" selected="selected">不显示</option>
	</select><br />
	<span>新标</span> <select name="newsign_on" style="width:48px">
		<option value="1" title="对于新输入的信息显示醒目的新标志">显示</option>
		<option value="0" selected="selected">不显示</option>
	</select>　
	<span>排序</span> <select name="newdate_sort" style="width:48px">
		<option value="0" selected="selected" title="缺省最新输入的显示在前面">倒序</option>
		<option value="1" title="缺省最先输入的显示在前面">顺序</option>
	</select><br />
<input type="submit" value="增加<%=pro_name_str%>" name=addpro onclick="return chkaddpro()">

	<br><br>
	排序：<br>
	<input type="submit" value="顶部" title="把这个<%=pro_name_str%>移动到所在<%=cate_name_str%>最上方" name=top onclick="return chkselpro(frmpro)">↑↑　<input type="submit" value="向上" name=up onclick="return chkselpro(frmpro)">↑<br>
	<input type="submit" value="底部" title="把这个<%=pro_name_str%>移动到所在<%=cate_name_str%>最下方" name=bottom onclick="return chkselpro(frmpro)">↓↓　<input type="submit" value="向下" name=down onclick="return chkselpro(frmpro)">↓<br>

<br><br>
</td>
</tr>
</form>
</table>
	</td>
<%end if %>
<!-- 如果选定了可加产品的类别 -->
</tr>
</table>
<!--#include file ="_bottom.asp"-->
<%
function ssplit(str,div,i)
	'str是数组，div是分隔符，i是第几个
	dim sstr,j
	sstr = split(str,div)
	if i > ubound(sstr)+1 then
		ssplit = ""
	else
		ssplit = sstr(i-1)
	end if
end function

function catename(cate_id)
	'根据类ID得到类名称
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_name from area where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1 
	catename = rs("cate_name")
	rs.close()
	set rs=nothing
end function

function catename_en(cate_id)
	'根据类ID得到类英文名称
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_name_en from area where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1 
	catename_en = rs("cate_name_en")
	rs.close()
	set rs=nothing
end function

function is_cate_pro(cate_id)
	'根据类ID得到是否有产品,true为有,false为没有，但不一定不可改放产品，本函数只判断cate_pro_sign是否为真
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_pro_sign from area where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1
	if not rs.eof then
		is_cate_pro = rs("cate_pro_sign")
	else
		is_cate_pro = false
	end if
	rs.close()
	set rs=nothing
end function

function proname(pro_id)
	'根据产品ID得到产品名称
	dim rs,sql,proInfo(4)
	set rs=server.createobject("adodb.recordset") 
	sql="select name,style_id,date_display_on,sign_on,date_sort from list where id="&pro_id
	rs.open sql,MM_conn_STRING,1,1 
	proInfo(0) = rs(0)
	proInfo(1) = rs(1)
	proInfo(2) = rs(2)
	proInfo(3) = rs(3)
	proInfo(4) = rs(4)
	rs.close()
	set rs=Nothing
	proname = proInfo
end function

function new_cate_lcode(fcode,fid)
	'得到一个父类的新子类lcode,fcode为父类的lcode,fid为父类的cate_id
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select top 1 cate_lcode from area where cate_fid=" &fid & " order by cate_lcode desc"
	rs.open sql,MM_conn_STRING,1,1 
	if rs.eof then
		if fcode = "0" then
			new_cate_lcode = "01"
		else
			new_cate_lcode = fcode & "01"
		end if
	else
		dim bottom_code,new_cate_lcode_r
		bottom_code = rs("cate_lcode")
		new_cate_lcode = left(bottom_code,len(bottom_code)-2)	'除了右端2位的左边所有位
		new_cate_lcode_r = right(bottom_code,2)
		if int(new_cate_lcode_r) >= 9 then
			new_cate_lcode_r = cstr(int(new_cate_lcode_r)+1)
		else
			new_cate_lcode_r = "0" & cstr(int(new_cate_lcode_r)+1)
		end if
		new_cate_lcode = new_cate_lcode & new_cate_lcode_r
	end if

	rs.close()
	set rs=nothing
end function

function is_cate_addpro(cateid)
	'判断是这个类是否可以加入列表（也就是这个区域没有子区域），结果为true false
	if cateid="" or isnull(cateid) then
		is_cate_addpro = false
		exit function
	end if
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_id from area where cate_fid=" &cateid
	rs.open sql,MM_conn_STRING,1,1 
		if rs.eof then 
		is_cate_addpro = true
		else
		is_cate_addpro = false
		end if
	rs.close()
	set rs=nothing
end Function

function cate_lcode(cateid)
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_lcode from area where cate_id=" &cateid
	rs.open sql,MM_conn_STRING,1,1 
	cate_lcode = rs("cate_lcode")
	rs.close()
	set rs=nothing
End function

function cate_type(cateid)
	If IsNull(cateid) Or cateid="" Then
		cate_type = 0
	else
		set rs=server.createobject("adodb.recordset") 
		sql="select cate_type_id from area where cate_id=" &cateid
		rs.open sql,MM_conn_STRING,1,1 
		cate_type = rs("cate_type_id")
		rs.close()
		set rs=Nothing
	End if
End Function

function cate_level(cateid)
	set rs_cate_level=server.createobject("adodb.recordset") 
	sql="select cate_level from area where cate_id=" &cateid
	rs_cate_level.open sql,MM_conn_STRING,1,1 
	cate_level = rs_cate_level("cate_level")
	rs_cate_level.close()
	set rs_cate_level=Nothing
End Function

If (Request.QueryString("proid") <> "") Then 
rsMODPROID.close()
set rsMODPROID = nothing
end If

Function maxmin(row,tb,m)
	set rs=server.createobject("adodb.recordset") 
	sql="select " & m & "(" & row & ") from " & tb
	rs.open sql,MM_conn_STRING,1,1 
	If Not rs.eof Then maxmin = rs(0)
	rs.close()
	set rs=nothing
End Function

Sub up_cate_type_id(ByVal cate_id,ByVal type_id)'更新area表里 cate_id 及其子区域所有的cate_type_id值为 type_id
	Dim rs1,sql1,tmplcode
	set rs1=server.createobject("adodb.recordset") 
	sql1="select cate_lcode from area where cate_id=" & cate_id
	rs1.open sql1,MM_conn_STRING,1,1 
	tmplcode = rs1("cate_lcode")
	rs1.close()
	Set rs1=nothing
	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	conn.execute "update area set cate_type_id = " & type_id & " where left(cate_lcode,len('" & tmplcode &"'))='" & tmplcode & "'"
	set conn=nothing	
End Sub

Function listslectstr(ByVal i,ByVal currentval)	'选定列表后输出
	if request.querystring("proid")<>"" then 
		If currentval = proname(request.querystring("proid"))(i) Then
			listslectstr = " selected=""selected"""
		Else
			listslectstr = ""
		End if
	End If
End Function

Sub update_cate_url(ByVal cate_lcode)
	dim rs,sql,cateurl
	set rs=server.createobject("adodb.recordset")
	sql="select cate_url from area where cate_lcode = left('" & cate_lcode & "',4)"
	rs.open sql,MM_conn_STRING,1,1
	cateurl = rs("cate_url")
	rs.close()
	sql="select cate_url from area where cate_lcode = '" & cate_lcode & "'"
	rs.open sql,MM_conn_STRING,3,3	
	rs("cate_url") = cateurl
	rs.update
	rs.close()
	Set rs=nothing
End sub
'listslectstr(1,rslisttypelist("style_id"))

'	if request.querystring("proid")<>"" then 
'		If rslisttypelist("style_id") = proname(request.querystring("proid"))(1) Then
'			response.write (" selected=""selected""")
'		End if
'	End if
%>
