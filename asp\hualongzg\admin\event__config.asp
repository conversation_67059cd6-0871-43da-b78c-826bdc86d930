<%
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'流程设置，字符串，第1个为名称，第二个为 category_id ，对应 event 和 event_type表的 category_id 字段值
category_id = "服务,0,招聘,1"

'--------------------------------------------------------------------
'招聘系统设置
dim jobs_config_home					'是否显示导航首页
dim jobs_email_sign						'处理时的email签名内容
dim jobs_list_num						'每页显示的条数

jobs_config_home = 0					'缺省为不显示 1为显示
jobs_list_num = 30

jobs_email_sign = vbcrlf & vbcrlf & "--" & "http://www." & back_site

sub form_sort(i,sort_type,table)
	'类或者产品排序，i为类或者产品编号,sort_type值: up,down,top,bottom 上、下、顶、底
	'table 为 pro_category 或者 products
	dim rs,sql,alert_str,sort_id,name_str

	'首先得到编号列值
	set rs=server.createobject("adodb.recordset")
	if table = "event_type" then
		name_str = "流程"
		sql="select sort_id from " & table & " where event_type_id=" &i
	end if
	rs.open sql,MM_conn_STRING
	sort_id = rs("sort_id")
	rs.close()
'	set rs=nothing

	if table = "event_type" then
		select case sort_type
		case "up"
		sql = "select top 1 event_type_id, sort_id from " & table & " where sort_id<" & sort_id & " order by sort_id desc"
		alert_str = "这个" & name_str & "已经在最上方"
		case "down"
		sql = "select top 1 event_type_id, sort_id from " & table & " where sort_id>" & sort_id & " order by sort_id asc"
		alert_str = "这个" & name_str & "已经在最下方"
		end select
	end if

	rs.open sql,MM_conn_STRING
	if rs.eof then
		rs.close()
		set rs=nothing
		Response.Write "<script language=javascript>alert('" & alert_str & "');;history.back();</script>"
		response.end
	else
		dim new_sort_id, new_i
		dim rs2,id_i
		dim ids,ids_new	'二个id数组

		if table = "event_type" then
			new_sort_id = rs("sort_id")
			new_i = rs("event_type_id")
			'执行sql将i,new_i这二行的sort_id互换
			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING
			sql = "update " & table & " set sort_id = " & new_sort_id & " where event_type_id=" &i
			sql2 = "update " & table & " set sort_id = " & sort_id & " where event_type_id=" &new_i
			conn.execute sql
			conn.execute sql2
			set conn=nothing
			response.redirect("jobs_event_config.asp")
		end if

	end if
end sub
%>