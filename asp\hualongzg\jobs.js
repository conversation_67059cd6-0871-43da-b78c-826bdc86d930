function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} }

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('This file type is not allowed for uploading.\nOnly the following file extensions are allowed: ' + extensions + '.\nPlease select another file and try again.');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(gif|jpg|png|bmp|jpeg)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('Uploaded Image is too small!\nShould be at least ' + img.minWidth + ' x ' + img.minHeight); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('Uploaded Image is too big!\nShould be max ' + img.maxWidth + ' x ' + img.maxHeight); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('Uploaded Image File Size is too big!\nShould be max ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}

function IsDigit(cCheck)
    {
    return (('0'<=cCheck) && (cCheck<='9'));
    }

function IsAlpha(cCheck)
    {
    return ((('a'<=cCheck) && (cCheck<='z')) || (('A'<=cCheck) && (cCheck<='Z')))
    }

function year2k(d) {
   return (d < 1000) ? d + 1900 : d;
   }

function isDate (year, month, day)
{
   // month argument must be in the range 1 - 12
   month = month - 1;  // javascript month range : 0- 11
   var tempDate = new Date(year,month,day);

   if ( (year2k(tempDate.getYear()) == year) &&
      (month == tempDate.getMonth()) &&
      (day == tempDate.getDate()) )
	{

      return true;
	}
   else
	{
      return false;
     }
}

function chklogin()
{
var newDateObj = new Date()

    if (document.job_form.surname.value == "")
		{
		alert("尊姓？");
		document.job_form.surname.focus();
		return false;
		}
    if (document.job_form.f_name.value == "")
		{
		alert("大名？");
		document.job_form.f_name.focus();
		return false;
		}

    if (isNaN(document.job_form.year.value) || document.job_form.year.value > newDateObj.getFullYear()  || document.job_form.year.value < 1910)
		{
		alert("请输入正确的出生年份");
		document.job_form.year.focus();
		return false;
		}

	if (isNaN(document.job_form.month.value) || document.job_form.month.value > 12  || document.job_form.month.value < 1)
		{
		alert("请输入正确的出生月份");
		document.job_form.month.focus();
		return false;
		}

	if (isNaN(document.job_form.day.value) || document.job_form.day.value > 31  || document.job_form.day.value < 1)
		{
		alert("请输入正确的出生日期");
		document.job_form.day.focus();
		return false;
		}

	if (!isDate(document.job_form.year.value, document.job_form.month.value, document.job_form.day.value)){
		  alert("日期组合错误，请检查生日组合。\r\n\r\n[ 提示：某些月份没有31日，二月没有30日，有的没有29日]");
		  document.job_form.day.focus();
		  return false;
		}

	strage = document.job_form.year.value + "-" + document.job_form.month.value + "-" + document.job_form.day.value;
	document.job_form.birth.value = strage;

	if (document.job_form.tel.value == "" && document.job_form.mobile.value =="")
    {
        alert("请输入您的联系电话或者手机号码");
        document.job_form.tel.focus();
        return false;
    }
	if (document.job_form.email.value==""){
		alert("请输入您的电子信箱，请留下正确的电子信箱。\n因为我们要通过EMAIL与您联系，如果您收不到我们的邮件，您的应聘不会成功。\n不用担心垃圾邮件，因为您的Email不会出现在公开的网页上。");
		document.job_form.email.focus();
		return false;
	}
	if (document.job_form.xuexiao1.value==""){
		alert("请输入毕业学校");
		document.job_form.xuexiao1.focus();
		return false;
	}
	if (document.job_form.zhuanye1.value==""){
		alert("请输入您所学专业");
		document.job_form.zhuanye1.focus();
		return false;
	}
	if (document.job_form.jingli.value==""){
		alert("请输入您的工作经历");
		document.job_form.jingli.focus();
		return false;
	}
	if (document.job_form.dongji.value==""){
		alert("胜任该工作的理由？");
		document.job_form.dongji.focus();
		return false;
	}
	if (document.job_form.nextyear.value==""){
		alert("您在未来一年的工作规划？");
		document.job_form.nextyear.focus();
		return false;
	}


	return true;
}


function chkmail(aa, graf)
{
   var pos = 0;
   var num = -1;
   var i = -1;
   var email = new Array()
   
   while (pos != -1)
   {
      pos = graf.indexOf(";",i+1);
      num += 1;
      if (pos == -1) { email[num] = graf.substring(i+1,graf.length); }
      else { email[num] = graf.substring(i+1,pos); }
      i = pos;
   }
   for ( i = 0 ; i <= num ; i++ )
   {
     if (email[i].length > 0)
     {
       l=email[i].indexOf("@");
       j=email[i].indexOf(".",l);
       k=email[i].indexOf(",");
       kk=email[i].indexOf(" ");
       jj=email[i].lastIndexOf(".") + 1;
       ll=email[i].indexOf(":");
       mm=email[i].indexOf("(");
       nn=email[i].indexOf(")");
       oo=email[i].indexOf("");
       len=email[i].length;

       if ((l <= 0) || (j <= (1+1)) || (k != -1) || (kk != -1) || (len-jj < 2) || (len-jj > 3) || (ll != -1) || (mm != -1 ) || (nn != -1) || (oo != -1))
       {
       	   if ( aa == "" ) { alert("您输入的 email 地址不正确，不用担心垃圾邮件，因为您的Email不会出现在公开的网页上。");
		   		document.job_form.email.focus();}
       	   else { alert("您输入的 email 地址不正确，不用担心垃圾邮件，因为您的Email不会出现在公开的网页上。");
		   		document.job_form.email.focus();}
           return false;
       }
     }
   }
   return true;
}

function noerror()
{
    if ( !chkmail('',job_form.email.value)){ return( false ) ; }
    if ( !chklogin()){ return( false ) ; }
	return confirm('简历填写无误？\n注意：请留下正确的电子信箱，否则您的应聘不会成功。\n因为我们要通过EMAIL与您联系，确定提交')
    return( true ) ;
}
