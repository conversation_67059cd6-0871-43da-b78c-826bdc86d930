<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<% If request.querystring("pw") = "malong" Then
Session("superadmin") = True
End If
If (Session("superadmin") <> true) Then Response.Redirect("default.asp")
%>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="lesson_config.asp" -->
<%
'If cate_hidden = True And request.querystring("id") = "" Then response.redirect("lesson_man.asp?id=1")
dim MM_editAction,MM_editAction_0
MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))

If (Request.QueryString <> "") Then
  MM_editAction_0 = MM_editAction & "?" & Request.QueryString
  else
  MM_editAction_0 = MM_editAction
End If
dim sele_cate_id	'当前选定类别
if request.querystring("id") <> "" then sele_cate_id = request.querystring("id")

If (Request.QueryString("proid") <> "") Then 
	Dim rsMODPROID
	Set rsMODPROID = Server.CreateObject("ADODB.Recordset")
	sqlMODPROID = "SELECT name,id,cate_id FROM lessons WHERE id = " & Request.QueryString("proid")
	rsMODPROID.open sqlMODPROID,MM_conn_STRING,1,1
end if

if request.form("cate") <> "" AND request.form("sel")<>"" then call form_sele
if request.form("cate") <> "" AND request.form("del")<>"" then call form_del
if request.form("cate") <> "" AND request.form("mod")<>"" then call form_mod
if request.form("add") <> "" AND request.form("newcatename")<>"" then call form_add

if (request.form("up") <> "" or request.form("down") <> "" or request.form("bottom") <> "" or request.form("top") <> "") then 
	dim del_type
	if request.form("up") <> "" then del_type = "up"
	if request.form("down") <> "" then del_type = "down"
	if request.form("bottom") <> "" then del_type = "bottom"
	if request.form("top") <> "" then del_type = "top"
	if request.form("frmname") = "cate" then
		call form_sort(ssplit(request.form("cate"),"-",1),del_type,"lesson_category")
	elseif request.form("frmname") = "pro" then
		call form_sort(request.form("proname"),del_type,"lessons")
	end if
end if

if request.form("selpro")<>"" then call form_selpro
if request.form("delpro")<>"" then call form_delpro
if request.form("modpro")<>"" then call form_modpro
if request.form("addpro")<>"" then call form_addpro

sub form_sort(i,del_type,table)
	'类或者课件排序，i为类或者课件编号,del_type值: up,down,top,bottom 上、下、顶、底
	'table 为 lesson_category 或者 lessons
	dim rs,sql,alert_str,sort_id,fid,name_str

	'首先得到编号列值
	set rs=server.createobject("adodb.recordset")
	if table = "lesson_category" then
		name_str = "类别"
		sql="select cate_lcode as sort_id,cate_fid as fid from " & table & " where cate_id="&i
	else
		name_str = "课件"
		sql="select sort_id,cate_id as fid from " & table & " where id=" &i
	end if
	rs.open sql,MM_conn_STRING
	sort_id = rs("sort_id")
	fid = rs("fid")
	rs.close()
'	set rs=nothing

	if table = "lesson_category" then
		select case del_type
		case "top"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode<'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode asc"
		alert_str = "这个" & name_str & "已经在同级最上方"
		case "up"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode<'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode desc"
		alert_str = "这个" & name_str & "已经在同级最上方"
		case "down"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode>'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode asc"
		alert_str = "这个" & name_str & "已经在同级最下方"
		case "bottom"
		sql = "select top 1 cate_id, cate_lcode from " & table & " where cate_lcode>'" & sort_id & "' AND cate_fid=" & fid & " order by cate_lcode desc"
		alert_str = "这个" & name_str & "已经在同级最下方"
		end select
	elseif table = "lessons" then
		select case del_type
		case "top"
		sql = "select top 1 id, sort_id from " & table & " where sort_id<" & sort_id & " AND cate_id=" & fid & " order by sort_id asc"
		alert_str = "这个" & name_str & "已经在最上方"
		case "up"
		sql = "select top 1 id, sort_id from " & table & " where sort_id<" & sort_id & " AND cate_id=" & fid & " order by sort_id desc"
		alert_str = "这个" & name_str & "已经在最上方"
		case "down"
		sql = "select top 1 id, sort_id from " & table & " where sort_id>" & sort_id & " AND cate_id=" & fid & " order by sort_id asc"
		alert_str = "这个" & name_str & "已经在最下方"
		case "bottom"
		sql = "select top 1 id, sort_id from " & table & " where sort_id>" & sort_id & " AND cate_id=" & fid & " order by sort_id desc"	
		alert_str = "这个" & name_str & "已经在最下方"
		end select
	end if

	rs.open sql,MM_conn_STRING
	if rs.eof then
		rs.close()
		set rs=nothing
		Response.Write "<script language=javascript>alert('" & alert_str & "');;history.back();</script>"
		response.end
	else
		dim new_sort_id, new_i
		dim rs2,id_i
		dim ids,ids_new	'二个id数组

		if table = "lesson_category" then
			new_sort_id = rs("cate_lcode")
			new_i = rs("cate_id")
			'执行sql将i,new_i这二行的sort_id互换
			set rs2=server.createobject("adodb.recordset")
			sql = "select cate_id as id from " & table & " where left(cate_lcode,len('" & new_sort_id & "'))='" & sort_id &"'"
			sql_new = "select cate_id as id from " & table & " where left(cate_lcode,len('" & sort_id & "'))='" & new_sort_id &"'"
			rs2.open sql,MM_conn_STRING
			id_i=0
			while not rs2.eof
				if id_i = 0 then
					ids = rs2("id")
					else
					ids = ids & "," & rs2("id")
				end if
			rs2.movenext()
			id_i= id_i + 1
			wend
			rs2.close()

			rs2.open sql_new,MM_conn_STRING
			id_i=0
			while not rs2.eof
				if id_i = 0 then
					ids_new = rs2("id")
					else
					ids_new = ids_new & "," & rs2("id")
				end if
			rs2.movenext()
			id_i= id_i + 1
			wend
			rs2.close()
			set rs2=nothing

			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING

			sql = "update " & table & " set cate_lcode = '" & new_sort_id & "'+right(cate_lcode,len(cate_lcode)-len('" & new_sort_id & "')) where cate_id in (" & ids & ")"

			sql2 = "update " & table & " set cate_lcode = '" & sort_id & "'+right(cate_lcode,len(cate_lcode)-len('" & sort_id & "')) where cate_id in (" & ids_new & ")"
			conn.execute sql
			conn.execute sql2
			set conn=nothing
			response.redirect("lesson_man.asp")
		else	'如果是课件排序
			new_sort_id = rs("sort_id")
			new_i = rs("id")
			'执行sql将i,new_i这二行的sort_id互换
			set conn=server.createobject("ADODB.CONNECTION")
			conn.open MM_conn_STRING
			sql = "update " & table & " set sort_id = " & new_sort_id & " where id=" &i
			sql2 = "update " & table & " set sort_id = " & sort_id & " where id=" &new_i
			conn.execute sql
			conn.execute sql2
			set conn=nothing
			response.redirect("lesson_man.asp?id=" & fid & "&proid=" & i)
		end if

	end if
end sub

sub form_add
	'增加类
	if request.form("cate") <> "" then
		if cint(ssplit(request.form("cate"),"-",3)) = cate_level_max then
		Response.Write "<script language=javascript>alert('最多" & cate_level_max & "级类，不能在这个类下增加子类！');;history.back();</script>"
		response.end
		end if

		if is_cate_pro(ssplit(request.form("cate"),"-",1)) = true then
		Response.Write "<script language=javascript>alert('这个类别下有课件，不能在这个类下增加子类！');;history.back();</script>"
		response.end
		end if

		dim rs,sql
		set rs=server.createobject("adodb.recordset")
		sql="select * from lesson_category"
		rs.open sql,MM_conn_STRING,3,3
		rs.addnew
		rs("cate_lcode") = new_cate_lcode(ssplit(request.form("cate"),"-",2),ssplit(request.form("cate"),"-",1))
		rs("cate_fid") = ssplit(request.form("cate"),"-",1)
		rs("cate_name") = request.form("newcatename")
		if pro_english_on = true then rs("cate_name_en") = request.form("newcatename_en")
		rs("cate_level") = cint(ssplit(request.form("cate"),"-",3)) +1
		rs.update
		response.redirect(MM_editAction_0)
	else		'增加顶级类
		if cate_top_add = true then
		set rs=server.createobject("adodb.recordset")
		sql="select * from lesson_category"
		rs.open sql,MM_conn_STRING,3,3
		rs.addnew
		rs("cate_lcode") = new_cate_lcode("0",0)
		rs("cate_fid") = 0
		rs("cate_name") = request.form("newcatename")
		rs("cate_level") = 1
		rs.update
		end if
		response.redirect(MM_editAction_0)
	end if
end sub

sub form_sele
	'选定类
	dim str
	str = ssplit(request.form("cate"),"-",1)
	response.redirect("lesson_man.asp?id=" & str)
end sub

sub form_del
	'删除类
	dim str,rs,sql
	str = ssplit(request.form("cate"),"-",1)

		'如果有子类或者课件，则不允许删除
	set rs=server.createobject("adodb.recordset")
	sql = "select * from lesson_category where cate_fid=" & str & " or (cate_id=" & str & " AND cate_pro_sign = true)"
	rs.open sql,mm_conn_string,1,1
	if not rs.eof then
		Response.Write "<script language=javascript>alert('类下有子类或者课件，请先删除子类和课件');;history.back();</script>"
		response.end
	else
		rs.close()
		set rs=nothing
	end if

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	sql="delete from lesson_category where cate_id="&str
	conn.execute sql
	set conn=nothing
	response.redirect("lesson_man.asp")
end sub

sub form_mod
	'修改类名称
	set rs=server.createobject("adodb.recordset")
	sql="select cate_name,cate_name_en from lesson_category where cate_id=" & ssplit(request.form("cate"),"-",1)
	rs.open sql,MM_conn_STRING,3,3
	rs("cate_name")=request("catename")
	if pro_english_on = true then rs("cate_name_en")=request("catename_en")
	rs.update
	response.redirect(MM_editAction_0)
end sub

sub form_delpro
	dim cate_id
	set rs=server.createobject("adodb.recordset")
	sql = "select cate_id,img,thumb,img2 from lessons where id="&request.form("proname")
	rs.open sql,MM_conn_STRING,1,1
	cate_id = rs("cate_id")

    Set File = CreateObject("Scripting.FileSystemObject")
	fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
	set fdir = File.getfile(fdir)
	set fdir = fdir.parentfolder
	set fdir = fdir.parentfolder								'获取父父目录
	if trim(rs("img")) <> "" then
		ImagePath = fdir & "\img\" & rs("img")
		if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
	end if
	if trim(rs("img")) <> "" then
		ImagePath = fdir & "\img\" & rs("img2")
		if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
	end if
	if trim(rs("img")) <> "" then
		ImagePath = fdir & "\img\" & rs("thumb")
		if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
	end if
	set file=nothing

	rs.close()
	set rs=nothing

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	sql="delete from lessons where id="&request.form("proname")
	conn.execute sql
	set conn=nothing

	'更新类别的课件数量和cate_pro_sign值
	call update_procount(cate_id)
	response.redirect(MM_editAction_0)
end sub

sub form_selpro
	response.redirect("lesson_man.asp?id=" & request.form("cate_id") & "&proid=" & request.form("proname"))
end sub

sub form_modpro
	'修改课件（包括名称、品牌）
	set rs=server.createobject("adodb.recordset")
	sql="select name from lessons where id=" & request.form("proname")
	rs.open sql,MM_conn_STRING,3,3
	rs("name")=request("modproname")
	rs.update
	response.redirect("lesson_man.asp?id=" & request.form("cate_id") & "&proid=" & request.form("proname"))
end sub

sub form_addpro
	set rs=server.createobject("adodb.recordset")
	sql="select name,cate_id,sort_id from lessons where cate_id=" & request.form("cate_id") 
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	rs("cate_id") = request.form("cate_id")
	rs("name") = request.form("newproname")
	rs.update
	rs("sort_id") = rs.bookmark
	rs.update
	rs.close()
	set rs=nothing

	'更新类别的课件数量和cate_pro_sign值
	call update_procount(request.form("cate_id"))
	response.redirect(MM_editAction_0)
end sub

sub update_procount(cate_id) '更新类别的课件数量和cate_pro_sign值
	dim rs,sql
	set rs=server.createobject("adodb.recordset")
	sql="select count(id) as count_id from lessons where cate_id="& cate_id & " group by cate_id"
	rs.open sql,MM_conn_STRING,1,1

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	if  not rs.eof then
		sql = "update lesson_category set cate_pro_sign=true,cate_pro_count=(" & rs("count_id") & ") where lesson_category.cate_id=" & cate_id
		else
		sql = "update lesson_category set cate_pro_sign=false,cate_pro_count=0 where lesson_category.cate_id=" & cate_id
	end if
	conn.execute sql
	set conn=nothing
	set rs=nothing	
end sub
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">课件管理（超级管理员使用）</h1>
<SCRIPT language=javascript>

function chkselect(form1)
{
    if (document.form1.cate.value == "")
		{
		alert("请选择类别");
		document.form1.cate.focus();
		return false;
		}
return true;
}

function chkdel()
{
    if (document.form1.cate.value == "")
		{
		alert("请选择类别");
		document.form1.cate.focus();
		return false;
		}
		return confirm('确定删除这个类别？');
		return( true ) ;
}

function chkmodcate()
{
	 if (document.form1.cate.value == "")
		{
		alert("请选择类别");
		document.form1.cate.focus();
		return false;
		}
	 if (document.form1.catename.value == "")
		{
		alert("请输入类名称");
		document.form1.catename.focus();
		return false;
		}
return true;
}

function chkaddcate()
{

	 if ((document.form1.cate.value == "") && (document.form1.newcatename.value != ""))
		{
<%if cate_top_add =false then%>
		return confirm('顶级类别不允许添加');
<%else%>
		return confirm('您没有选择父类，是否加入顶级类别？');
<%end if%>
		return( true ) ;
		}

    if (document.form1.newcatename.value == "")
		{
		alert("请输入类名称");
		document.form1.newcatename.focus();
		return false;
		}
}


function chkselpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择课件");
		document.frmpro.proname.focus();
		return false;
		}
return true;
}

function chkdelpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择课件");
		document.frmpro.proname.focus();
		return false;
		}
		return confirm('确定删除这个课件？');
		return( true ) ;
}

function chkmodpro()
{
    if (document.frmpro.proname.value == "")
		{
		alert("请选择课件");
		document.frmpro.proname.focus();
		return false;
		}

    if (document.frmpro.modproname.value == "")
		{
		alert("请输入内容");
		document.frmpro.modproname.focus();
		return false;
		}
}
function chkaddpro()
{
   if (document.frmpro.newproname.value == "")
		{
		alert("请输入新课件名称");
		document.frmpro.newproname.focus();
		return false;
		}

}
//-->
</SCRIPT>
<table width=100% height=100% cellpadding=2 border=0 class=table0>
<tr valign=top>
<%If cate_hidden  = False then'如果不分类%>
	<td class=td0 width=50%>类管理：<%if request.querystring("id")<>"" then response.write("<font color=#ff0000>" & catename(request.querystring("id")) & "</font>")%><br>

<table width=100% height=100% cellpadding=2 border=0 class=table0>
<form name="form1" method="POST" action="<%=(MM_editAction)%>?id=<%=(request.querystring("id"))%>">
<input type=hidden name=frmname value="cate">
<tr valign=top align=left>
<td class=td0 width=100><select name="cate" size=<%=cate_select_size%>>
<option value="">== 类别列表 ==</option>
<%
dim rscate
set rscate = Server.CreateObject("ADODB.Recordset")
rscate_s = "select cate_name,cate_id,cate_lcode,cate_level,cate_sort_id,cate_pro_sign from lesson_category order by cate_lcode asc"
rscate.open rscate_s,MM_conn_STRING,1,1
while not rscate.eof
dim cate_dot,cate_dot_i
	cate_dot = ""
	cate_dot_i = rscate("cate_level") -1
	cate_dot_i2 = 0
	while cate_dot_i > 0 
		if cate_dot_i2 = 0 then
		cate_dot = "　－"
		else
		cate_dot = "　" & cate_dot
		end if
		cate_dot_i = cate_dot_i - 1
		cate_dot_i2 = cate_dot_i2 + 1
	wend 
%>
<option<%if sele_cate_id = cstr(rscate("cate_id")) then response.write(" selected")%> value="<%=rscate("cate_id")%>-<%=rscate("cate_lcode")%>-<%=rscate("cate_level")%>"><%=cate_dot%><%=rscate("cate_name")%></option>
<%
rscate.movenext()
wend
rscate.close()
set rscate = nothing
%>
</select>
</td>
	<td class=td0>
<input type="submit" value="选定类别" name=sel onclick="return chkselect(form1)"><br>
<%if cate_mana_on = true then	'允许类管理%>
	<input type="submit" value="删除类别" name=del onclick="return chkdel()">
	<br><br>
	<input type="text" name="catename" size=15<%if request.querystring("id")<>"" then response.write(" value=""" & catename(request.querystring("id")) & """")%>><br>
	<%if pro_english_on = true then%>
	<input type="text" name="catename_en" size=15<%if request.querystring("id")<>"" then response.write(" value=""" & catename_en(request.querystring("id")) & """")%>><font color=#ff0000 class=p12>(英文名)</font><br>
	<%end if%>
	<input type="submit" value="修改类名" name=mod onclick="return chkmodcate();">

	<br><br>
	<input type="text" name="newcatename" size=15><br>	
	<%if pro_english_on = true then%>
	<input type="text" name="newcatename_en" size=15><font color=#ff0000 class=p12>(英文名)</font><br>	
	<%end if%>
	<input type="submit" value="增加类别" name=add onclick="return chkaddcate()">
	<%if cate_sort_on = true then%>
	<br><br>
	排序：<br>
	<input type="submit" value="顶部" title="把这个类移动到所在级别的最上方" name=top onclick="return chkselect(form1)">↑↑<br>
	<input type="submit" value="向上" name=up onclick="return chkselect(form1)">↑<br>
	<input type="submit" value="向下" name=down onclick="return chkselect(form1)">↓<br>
	<input type="submit" value="底部" title="把这个类移动到所在级别的最下方" name=bottom onclick="return chkselect(form1)">↓↓<br>
	<%end if%>
<%end if%>
	</td>
</tr>
</form>
</table>


	</td><%End If '隐藏类操作，对于没有不分类的课件%>
<!-- 如果选定了可加课件的类别 -->
<%if sele_cate_id <> "" AND is_cate_addpro(sele_cate_id) = true then
dim rspro
set rspro = Server.CreateObject("ADODB.Recordset")
rspro_s = "select id,name from lessons where cate_id=" & sele_cate_id & " order by sort_id asc"
rspro.open rspro_s,MM_conn_STRING,1,1
	if not rspro.eof then
		is_pro_list = true	'当前类下有课件
		else
		is_pro_list = false
	end if
%>
<%If cate_hidden  = False then'如果不分类%>	<td width=1 bgcolor=<%=back_menubackcolor%>></td><%End if%>
	<td class=td0 width=50%><%
If cate_hidden  = False then'如果不分类
	if is_pro_list = true then
		if request.querystring("proid") <> "" then '选择了课件
		response.write("本类课件管理:<font color=#ff0000>" & proname(request.querystring("proid")) &  "</font><br>")
		else
		response.write("本类课件管理:<br>")
		end if
	else
	response.write("本类暂无课件，请添加<br>")
	end If
End if	%>
<table width=100% height=100% cellpadding=2 border=0 class=table0>
<form name="frmpro" method="POST" action="<%=(MM_editAction)%>?id=<%=(request.querystring("id"))%>">
<input type=hidden name=frmname value="pro">
<input type=hidden name="cate_id" value="<%=(request.querystring("id"))%>">
<tr valign=top align=left>
<td class=td0 width=30><%
	if is_pro_list = true then
	%>
	<select name="proname" size=<%=cate_select_size%>>
	<%
	while not rspro.eof%>
	<option value="<%=rspro("id")%>"><%=rspro("name")%></option>
	<%
	rspro.movenext()
	wend
	%>
	</select><%
	end if
rspro.close()
set rspro=nothing
%>
</td>
<td class=td0><%
	if is_pro_list = true then'如果当前类下有课件
	%>
	<input type="submit" value="选定课件" name=selpro onclick="return chkselpro()"><br>
	<input type="submit" value="删除课件" name=delpro onclick="return chkdelpro()">
	<br><br>
	<input type="text" name="modproname" size=15<%if request.querystring("proid")<>"" then response.write(" value=" & proname(request.querystring("proid")))%>><br>
	<input type="submit" value="修改课件" name=modpro onclick="return chkmodpro();">

	<br><br><%
	end if%>
<input type="text" name="newproname" size=15><br>	
<input type="submit" value="增加课件" name=addpro onclick="return chkaddpro()">

	<br><br>
	排序：<br>
	<input type="submit" value="顶部" title="把这个课件移动到所在类最上方" name=top onclick="return chkselpro(frmpro)">↑↑<br>
	<input type="submit" value="向上" name=up onclick="return chkselpro(frmpro)">↑<br>
	<input type="submit" value="向下" name=down onclick="return chkselpro(frmpro)">↓<br>
	<input type="submit" value="底部" title="把这个课件移动到所在类最下方" name=bottom onclick="return chkselpro(frmpro)">↓↓<br>

<br><br>
	<%If (Request.QueryString("proid") <> "") Then %>
		<div style="background-color:#dddddd;width:150px" class=p14 align=center><a href=lesson_man2.asp?proid=<%=Request.QueryString("proid")%>>→ 查看与编辑当前课件<br>[ <%=proname(Request.QueryString("proid"))%> ]</a></div>
	<%end if%>
</td>
</tr>
</form>
</table>
	</td>
<%end if %>
<!-- 如果选定了可加课件的类别 -->
</tr>
</table>
<!--#include file ="_bottom.asp"-->
<%
function ssplit(str,div,i)
	'str是数组，div是分隔符，i是第几个
	dim sstr,j
	sstr = split(str,div)
	if i > ubound(sstr)+1 then
		ssplit = ""
	else
		ssplit = sstr(i-1)
	end if
end function

function catename(cate_id)
	'根据类ID得到类名称
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_name from lesson_category where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1 
	catename = rs("cate_name")
	rs.close()
	set rs=nothing
end function

function catename_en(cate_id)
	'根据类ID得到类英文名称
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_name_en from lesson_category where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1 
	catename_en = rs("cate_name_en")
	rs.close()
	set rs=nothing
end function

function is_cate_pro(cate_id)
	'根据类ID得到是否有课件,true为有,false为没有，但不一定不可改放课件，本函数只判断cate_pro_sign是否为真
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_pro_sign from lesson_category where cate_id="&cate_id
	rs.open sql,MM_conn_STRING,1,1
	if not rs.eof then
		is_cate_pro = rs("cate_pro_sign")
	else
		is_cate_pro = false
	end if
	rs.close()
	set rs=nothing
end function

function proname(pro_id)
	'根据课件ID得到课件名称
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select name from lessons where id="&pro_id
	rs.open sql,MM_conn_STRING,1,1 
	proname = rs("name")
	rs.close()
	set rs=nothing
end function

function new_cate_lcode(fcode,fid)
	'得到一个父类的新子类lcode,fcode为父类的lcode,fid为父类的cate_id
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select top 1 cate_lcode from lesson_category where cate_fid=" &fid & " order by cate_lcode desc"
	rs.open sql,MM_conn_STRING,1,1 
	if rs.eof then
		if fcode = "0" then
			new_cate_lcode = "01"
		else
			new_cate_lcode = fcode & "01"
		end if
	else
		dim bottom_code,new_cate_lcode_r
		bottom_code = rs("cate_lcode")
		new_cate_lcode = left(bottom_code,len(bottom_code)-2)	'除了右端2位的左边所有位
		new_cate_lcode_r = right(bottom_code,2)
		if int(new_cate_lcode_r) >= 9 then
			new_cate_lcode_r = cstr(int(new_cate_lcode_r)+1)
		else
			new_cate_lcode_r = "0" & cstr(int(new_cate_lcode_r)+1)
		end if
		new_cate_lcode = new_cate_lcode & new_cate_lcode_r
	end if

	rs.close()
	set rs=nothing
end function

function is_cate_addpro(cateid)
	'判断是这个类是否可以加入课件，结果为true false
	if cateid="" or isnull(cateid) then
		is_cate_addpro = false
		exit function
	end if
	dim rs,sql
	set rs=server.createobject("adodb.recordset") 
	sql="select cate_id from lesson_category where cate_fid=" &cateid
	rs.open sql,MM_conn_STRING,1,1 
		if rs.eof then 
		is_cate_addpro = true
		else
		is_cate_addpro = false
		end if
	rs.close()
	set rs=nothing
end function

If (Request.QueryString("proid") <> "") Then 
rsMODPROID.close()
set rsMODPROID = nothing
end if
%>
