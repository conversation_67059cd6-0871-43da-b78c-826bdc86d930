<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"--><%
sql_str = "select cate_id,cate_name from area where (left(cate_lcode,4) = '0102' or left(cate_lcode,4) = '0202') and cate_level=3 order by cate_lcode asc"
con_row = "cate_descript1"
%>
<style type="text/css">
/* <![CDATA[ */
ul.procate {list-style-type:none;width:300px;padding-right:50px}
ul.procate li a.ac{color:#f00}
div.procate_con{width:500px;left:400px;padding-top:10px}
/* ]]> */ 
</style>
<h1 class="b_ch1">产品类说明</h1>
<%
Function ac_str(ByVal id1,ByVal id2)
	ac_str = ""
	If id1 = id2 Then ac_str = " class=""ac"""
End function

set rs=server.createobject("adodb.recordset")
rs.open sql_str,MM_conn_STRING,1,1
If request.querystring("id")<> "" Then 
	this_id = request.querystring("id")
	Else
	this_id=""
End if
If Not rs.eof Then
response.write "<h2>选择类</h2><ul class=""procate"">"
	While Not rs.eof
		response.write "<li>&gt;&gt; <a " & ac_str(this_id,CStr(rs("cate_id"))) & "href=""procate.asp?id=" & rs("cate_id")  & """>" & rs("cate_name") & "</a></li>"
	rs.movenext()
	Wend
response.write"</ul>"	
End If
rs.close()

If this_id <> "" then
sql = "select cate_id as id," & con_row & " from area where cate_id=" & this_id

rs.open sql,MM_conn_STRING,1,1

%>
<div class="procate_con">
		<form method="post" name="frmpdetail" id="frmpdetail" action="procate.asp">
			<p><textarea id="content" name="content" rows="15" cols="80" style="display:none"><%=rs(1)%></textarea>
			<iframe ID="htmlbuilder0" src="htmlbuilder/htmlbuilder.asp?id=content&style=<%=hb_css%>" frameborder="0" scrolling="no" width="550" height="300"></iframe>
			</p>
			<p><input type="submit" value="修改" class="isubmit" style="width:120px;height:20px" /><input type="hidden" id="actionok" name="actionok" value="mod" /><input type="hidden" id="id" name="id" value="<%=rs("id")%>" /></p>
		</form>
</div>
<%End if%>
<%Set rs=nothing%>
<%
'更新
If request.Form("actionok") = "mod" Then
	sql = "update area set " & con_row & " ='" & request.Form("content")  & "' where cate_id = " & request.Form("id")
	sql_command sql
	response.redirect("procate.asp?id=" & request.Form("id"))
End if
%>
<!--#include file ="_bottom.asp"-->