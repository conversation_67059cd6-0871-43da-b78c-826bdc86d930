<%@LANGUAGE="VBSCRIPT" codepage="65001"%>

<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel<3 then Response.Redirect "stat_help.asp?id=004&error=您没有查看详细记录的权限。"

wherestr=Request("wherestr")

pagesize=mPageSize
if request("page")="" then
  	curpage = 1
else
	curpage = clng(request("page"))
end if
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>详细日志</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指表中数据可查看详细信息</font>
  </td></tr>
</table>

<br>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="540" align=center>
  <tr align=center height="25" bgcolor="<%=(back_menubackcolor)%>">
    <td class=td0 width="85"><font class=p12>时间</font></td><td class=td0 width="75"><font class=p12>地区</font></td><td class=td0 width="40" nowrap><font class=p12>分辨率</font></td><td class=td0 width="55" nowrap><font class=p12> 操作系统 </font></td><td class=td0 width="55"><font class=p12>浏览器</font></td><td class=td0 width="170"><font class=p12>来源网页</font></td>
  </tr>
<%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")

sql = "SELECT * FROM view " & wherestr & " ORDER BY id DESC"
rs.open sql, conn, 1, 1
if rs.bof and rs.eof then
	rs.close
'	response.write "<tr><td class=td0 colspan='6' align=center><br><font class=p14>" & wherestr & "<br><br>没有符合条件的记录</font><br></td></tr></table></td></tr>"
	response.write "<tr><td class=td0 colspan='6' align=center><br><font class=p14><br><br>没有符合条件的记录</font><br></td></tr><tr><td class=td014 colspan='6' align=right><a href='javascript:history.back()'>继续</a></td></tr></table>"
else
	dim i
	rs.pagesize = pagesize
	if rs.pagecount < curpage then
		rs.absolutepage=rs.pagecount
		curpage=rs.pagecount
	else
		rs.absolutepage = curpage
	end if
for i = 1 to rs.pagesize
%>
  <tr height="1" bgcolor="<%=(stat_style_nav)%>"><td class=td0 colspan=6></td></tr>
  <tr height="18">
	<td class=td0 nowrap><a title="<%=rs("vpage")%>"><font class=p12><%=month(rs("vtime")) & "-" & day (rs("vtime")) & " " & formatdatetime(rs("vtime"),4)%></font></a></td>
	<td class=td0 nowrap><font class=p12><a title="<%=rs("vIP")%>"><%=rs("vwhere")%><%=rs("vwheref")%></a></font></td>
	<td class=td0 align=center><font class=p12><%=rs("vwidth")%></font></td>
	<td class=td0 align=center nowrap><font class=p12><%=rs("vOS")%></font></td>
	<td class=td0 align=center nowrap><font class=p12><%=rs("vsoft")%></font></td>
	<td class=td0><%
	vcome=rs("vcome")
	thelen=len(vcome)
	if thelen=0 then Response.Write ""
	if thelen <= 33 and thelen > 0 then
		svcome=right(vcome,thelen-6)
		Response.Write "<a href='" & vcome & "' target='_blank'><font class=p12> " & svcome & "</font></a>"
	end if
	if thelen >= 34 then
		svcome=left(right(vcome,thelen-6),24) & "..."
		Response.Write "<a title='" & vcome & "' href='" & vcome & "' target='_blank'><font class=p12> " & svcome & "</font></a>"
	end if
	%></td>
  </tr>
<%
  rs.movenext
    if rs.eof then
      i = i + 1
      exit for
    end if
  next
%>
</table>
<table class=table0 width=540 cellspacing=0 align=center cellpadding=0>
<form method=put action="stat_anall.asp" id=form2 name=form2>
  <tr height=65>
    <td class=td0 width=1></td>
    <td class=td012 width="100%" nowrap>共<%=cstr(rs.pagecount)%>页 第<%=cstr(curpage)%>页 | 共<%=cstr(rs.recordcount)%>条 当前页<%=cstr(i-1)%>条 | 
      <%wherestr1=wherestr
      wherestr=server.URLEncode(wherestr)
      if curpage <> 1 then%>
		<a href="stat_anall.asp?page=1&wherestr=<%=wherestr%>">&lt;&lt; 第一页 </a>
		<a href="stat_anall.asp?page=<%=cstr(curpage-1)%>&wherestr=<%=wherestr%>">&lt; 上页 </a>
      <%else%>
        <font color="#888888">&lt;&lt;第一页 &lt;上页
      <%end if
      if curpage <> rs.pagecount then%>
		<a href="stat_anall.asp?page=<%=cstr(curpage+1)%>&wherestr=<%=wherestr%>"> 下页 &gt;</a>
		<a href="stat_anall.asp?page=<%=cstr(rs.pagecount)%>&wherestr=<%=wherestr%>"> 最后页 &gt;&gt;</a>
      <%else%>
         <font color="#888888">下页&gt; 最后页&gt;&gt;
      <%end if%>
		 | <input type=hidden name="wherestr1" value="<%=wherestr%>"><input type=text name="page" style="width:60px"><input type=submit value='页号'>
	</td>
    <td class=td0 width=1></td>
  </tr>
</form>
</table>
<br>
<%
'如果是检索结果，则显示保存数据表单
if wherestr <> "" then
wherestr=Request("wherestr")
'保存数据表单
if session.Contents("master")=true or mlevel>=6 then

%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  
  <form action="stat_an_save.asp" method=post id=form1 name=form1>
  <tr height="30">
    <td class=td0 width="1"></td>
    <td class=td0 width="100%"><font class=p14>保存本次检索条件</font><br><br>
<% if ssql_view =1 then%>
<font class=p12><b>检索条件&nbsp;</b></font><%if wherestr="" then%><font class=p12>没有检索条件</font><%else%><font class=p12><%=wherestr%></p><%end if%><br><%end if'ssql_view=1%>
<font class=p12><b>查询项目</b></font><br>
<font class=p12><b>取个名字&nbsp;</b></font><input name="name" size="16" class="input">&nbsp;<INPUT type="radio" name="overwrite" value="0" checked><font class=p12>同名时提示&nbsp;</font><INPUT type="radio" name="overwrite" value="1"> <font class=p12>同名时覆盖&nbsp;</font>
	<br><font class=p12><b>加个介绍</b>&nbsp;</font><input name="content" size="50" class="input">
	<input type="hidden" name="wherestr" value="<%=wherestr%>"><input type="hidden" name="outtype" value="详细">
	<input type="submit" value="保存自定义查询" name="save" class="backc2">
	</td>
    <td class=td0 width="1"></td>
  </tr>
  </form>
</table>
<%
end if
end if
%>
<%
rs.close
end if

set rs=nothing
conn.Close 
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->