<%@LANGUAGE="VBSCRIPT" codepage="65001"%>

<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel<2 then Response.Redirect "stat_help.asp?id=004&error=您没有查看客户端软件统计的权限。"
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>客户浏览器与操作系统</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指图形柱或者图形柱下的数字可以看到对应的访问量</font>
  </td></tr>
</table>
<br>
<br>
<%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
%>
<%'浏览器使用情况

dim soft(7,2)

	soft(0,0)="NetCaptor"
	soft(1,0)="MSIE 6.x"
	soft(2,0)="MSIE 5.x"
	soft(3,0)="MSIE 4.x"
	soft(4,0)="Netscape"
	soft(5,0)="Opera"
	soft(6,0)="Other"

	'将浏览器情况写入数组，howsoft
	soft(0,1)=howsoft("NetCaptor")
	soft(1,1)=howsoft("MSIE 6.x")
	soft(2,1)=howsoft("MSIE 5.x")
	soft(3,1)=howsoft("MSIE 4.x")
	soft(4,1)=howsoft("Netscape")
	soft(5,1)=howsoft("Opera")
	soft(6,1)=howsoft("Other")

	'找到最大值和总值
	maxsoft=0
	sumsoft=0
	for i=0 to 6
		if soft(i,1)>maxsoft then maxsoft=soft(i,1)
		sumsoft=sumsoft+soft(i,1)
	next
	'防止除数为0而出错
	if maxsoft=0 then maxsoft=1
	if sumsoft=0 then sumsoft=1
%>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="385" align=center>
<tr height="101">
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxsoft*10+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxsoft*10/4+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxsoft*10/2+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxsoft*10/4+0.5)/10%><br></font></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 6
%>
<td class=td0 width=45 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid" src="<%=(stat_chart_gif)%>"
	height="<%=(soft(i,1)/maxsoft)*100%>" width="9"
	alt="<%=soft(i,0)%>，访问<%=soft(i,1)%>次，<%
	
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(soft(i,1)*1000/sumsoft+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>

<tr>
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></td>
<td class=td0 width=10></td>
<%
for i= 0 to 6
%>
<td class=td0 width=45 align=center>
<a title="<%=soft(i,0)%>，访问<%=soft(i,1)%>次，<%
	
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(soft(i,1)*1000/sumsoft+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	
	%>%"><font class=p12><%=left(soft(i,0),6)%></font></a></td>
<%next%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5"><td class=td0 align=center colspan=10>&nbsp;</td></tr>
<tr height="5"><td class=td0 align=center colspan=10><font class=p14><u>客户使用浏览器的情况</u></font></td></tr>
</table>
<br>
<br>
<%'操作系统使用情况

dim OS(7,2)

	OS(0,0)="Win 2000"
	OS(1,0)="Win XP"
	OS(2,0)="Win NT"
	OS(3,0)="Win 9x"
	OS(4,0)="类Unix"
	OS(5,0)="Mac"
	OS(6,0)="Other"

	'将操作系统情况写入数组，howOS
	OS(0,1)=howOS("Win 2000")
	OS(1,1)=howOS("Win XP")
	OS(2,1)=howOS("Win NT")
	OS(3,1)=howOS("Win 9x")
	OS(4,1)=howOS("类Unix")
	OS(5,1)=howOS("Mac")
	OS(6,1)=howOS("Other")

	'找到最大值和总值
	maxOS=0
	sumOS=0
	for i=0 to 6
		if OS(i,1)>maxOS then maxOS=OS(i,1)
		sumOS=sumOS+OS(i,1)
	next
	'防止除数为0而出错
	if maxOS=0 then maxOS=1
	if sumOS=0 then sumOS=1
%>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="385" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="101">
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxOS*10+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxOS*10/4+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxOS*10/2+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxOS*10/4+0.5)/10%><br></font></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 6
%>
<td class=td0 width=45 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid" src="<%=(stat_chart_gif)%>"
	height="<%=(OS(i,1)/maxOS)*100%>" width="9"
	alt="<%=OS(i,0)%>，访问<%=OS(i,1)%>次，<%
	
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(OS(i,1)*1000/sumOS+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>

<tr>
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></td>
<td class=td0 width=10></td>
<%
for i= 0 to 6
%>
<td class=td0 width=45 align=center><font class=p12>
<a title="<%=OS(i,0)%>，访问<%=OS(i,1)%>次，<%
	
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(OS(i,1)*1000/sumOS+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	
	%>%"><%if i=0 then%>Win 2k<%else%><%=OS(i,0)%><%end if%></font></a></td>
<%next%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5"><td class=td0 colspan=10>&nbsp;</td></tr>
<tr height="5"><td class=td0 colspan=10 align=center><font class=p14><u>操作系统使用情况</u></font></td></tr>
</table>
<br>
<br>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="270" align=center>
<tr height="10">
	<td class=td0 width="40"></td><td class=td0 width="230"><img src="stat_images/chart_head.gif"></td>
</tr>
<%


Set rs = Server.CreateObject("ADODB.Recordset")
sql="select vwidth,count(id) as allwidth from view where vwidth<>0 group by vwidth order by vwidth DESC"
rs.Open sql,conn,1,1

maxallwidth=0
sumallwidth=0
do while not rs.EOF
	if clng(rs("allwidth"))>maxallwidth then maxallwidth=clng(rs("allwidth"))
	sumallwidth=sumallwidth+clng(rs("allwidth"))
	rs.MoveNext
loop
	'防止除数为零而出错
	if maxallwidth=0 then maxallwidth=1
	if sumallwidth=0 then sumallwidth=1

rs.MoveFirst 
do while not rs.EOF
thewidth=rs("vwidth")
vallwidth=rs("allwidth")
%>
<tr>
<td class=td0 width="40" align=right><a title="<%=thewidth%>像素，<%=vallwidth%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallwidth*1000/sumallwidth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12><%=thewidth%></a></a>&nbsp;</td>
<td class=td0 width="230" background="stat_images/chart_b2.gif" align=left>
<img style="BORDER-left: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	width="<%=(vallwidth/maxallwidth)*183%>" height="9"
	alt="<%=thewidth%>像素，<%=vallwidth%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallwidth*1000/sumallwidth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12> <%=vallwidth%></font></td>
</tr>
<%
rs.MoveNext
loop
%>
<tr height="10">
	<td class=td0 width="40"></td><td class=td0 width="230"><img src="stat_images/chart_bottom.gif"></td>
</tr>
<tr height="5"><td class=td0 colspan=10>&nbsp;</td></tr>
<tr height="5"><td class=td0 colspan=10 align=center><font class=p14><u>客户端显示器分辨率</u></font></td></tr>
</table>
<%
rs.Close

set rs=nothing
conn.Close 
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->
<%
'计算使用指定浏览器的浏览量
function howsoft(vsoft)
    tmprs=conn.execute("Select count(id) as howsoft from view where" & _
		" vsoft='" & vsoft & "'")
    howsoft=tmprs("howsoft")
	if isnull(howsoft) then howsoft=0
end function

'计算使用指定操作系统的浏览量
function howOS(vOS)
    tmprs=conn.execute("Select count(id) as howOS from view where" & _
		" vOS='" & vOS & "'")
    howOS=tmprs("howOS")
	if isnull(howOS) then howOS=0
end function
%>