<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file = "_config.asp"-->
<% Option Explicit %>
<%response.Charset = "utf-8"%>

<%

' 描述：弹窗式编辑调用接口文件
' 传入参数
'	style	: 样式名
'	form	: 要返回或设置值的表单form名
'	field	: 要返回或设置值的表单项textarea名


Dim sStyleName, sFormName, sFieldName
sStyleName = Trim(Request("style"))
sFormName = Trim(Request("form"))
sFieldName = Trim(Request("field"))

If sStyleName = "" Then sStyleName = "s_popup"
%>

<html>
<head>
<title>htmlbuilder - html在线编辑器</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style>
body,td,input,textarea {font-size:9pt}
</style>
<script language=javascript>
var objField=opener.document.<%=sFormName%>.<%=sFieldName%>;

function doSave(){
	objField.value = htmlbuilder1.getHTML();
	self.close();
}

function setValue(){
	try{
		if (htmlbuilder1.bInitialized){
			htmlbuilder1.setHTML(objField.value);
		}else{
			setTimeout("setValue();",1000);
		}
	}
	catch(e){
		setTimeout("setValue();",1000);
	}
}
</script>

</head>
<body>

<FORM method="POST" name="myform">
<INPUT type="hidden" name="content1" value="">
<IFRAME ID="htmlbuilder1" src="htmlbuilder.asp?id=content1&style=<%=sStyleName%>" frameborder="0" scrolling="no" width="100%" height="100%"></IFRAME>
</FORM>

<script language=javascript>
setTimeout("setValue();",1000);
</script>
</body>
</html>
