<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%
If (Session("isadmin") <> true) Then Response.Redirect("../admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "link_exchang"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "links2.asp"
  MM_fieldsStr  = "agree|value"
  MM_columnsStr = "agree|none,1,0"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
set rsLINKS = Server.CreateObject("ADODB.Recordset")
rsLINKS.ActiveConnection = MM_conn_STRING
rsLINKS.Source = "SELECT agree, id, my_url, site_contact, site_data, site_decript, site_demo, site_email, site_name, site_url FROM link_exchang ORDER BY site_data DESC"
rsLINKS.CursorType = 0
rsLINKS.CursorLocation = 2
rsLINKS.LockType = 3
rsLINKS.Open()
rsLINKS_numRows = 0
%>
<%
Dim Repeat1__numRows
Repeat1__numRows = -1
Dim Repeat1__index
Repeat1__index = 0
rsLINKS_numRows = rsLINKS_numRows + Repeat1__numRows
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">友情链接列表</h1>
	<table width="500" align="center"><thead>
	<tr><td colspan=6 class="td0" nowrap><a href="links2+add.asp" title="主动增加友情链接" style="float:right"><b>→ 点击增加友情链接</b></a></td></tr></thead>
	</table>
	<table width="500" border="1" cellspacing="0" cellpadding="4" align=center>
	<thead>
	  <tr align=center> 
		<td nowrap>网站名称</td>
		<td nowrap>联系人</td>
		<td nowrap>验证网址</td>
		<td nowrap>查看与修改</td>
		<td nowrap>批准状态</td>
		<td>&nbsp;</td>
	  </tr>
  </thead>
		<% 
While ((Repeat1__numRows <> 0) AND (NOT rsLINKS.EOF)) 
%>
	  <form name="form2" method="POST" action="<%=MM_editAction%>">
		<tr height=20> 
		  <td nowrap class=p12>&nbsp;<a target="_blank" href="<%=(rsLINKS.Fields.Item("site_url").Value)%>" title="<%=(rsLINKS.Fields.Item("site_decript").Value)%>"><%=(rsLINKS.Fields.Item("site_name").Value)%></a></td>
		  <td nowrap class=p12>&nbsp;<a title="发信给该站点联系人" href="mailto:<%=(rsLINKS.Fields.Item("site_email").Value)%>?subject=关于贵站与我站 （<%=(back_siteurl)%>）友情链接的事宜"><%=(rsLINKS.Fields.Item("site_contact").Value)%></a></td>
		  <td nowrap class=p12>&nbsp;<a title="点击查看对方网站上的页面，验证链接" target="_blank" href="<%=(rsLINKS.Fields.Item("my_url").Value)%>"><%=(rsLINKS.Fields.Item("my_url").Value)%></a></td>
		  <td class=p12 align=center nowrap><a href="links_des.asp?id=<%=(rsLINKS.Fields.Item("id").Value)%>" title="点击查看或修改本条链接的详细信息">点击查看详细信息…</a></td>
		  <td nowrap align=center class=p12> 
			<input type="checkbox" name="agree" value="checkbox" <% if rsLINKS.Fields.Item("agree").Value =true then response.write "checked"%>>
		  </td>
		  <td class=p12 align=center width=20><input type="submit" name="Submit" value="确定"></td>
</tr>
		<input type="hidden" name="MM_update" value="true">
		<input type="hidden" name="id" value="<%=(rsLINKS.Fields.Item("id").Value)%>">
		<input type="hidden" name="MM_recordId" value="<%= rsLINKS.Fields.Item("id").Value %>">
	  </form>

			<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsLINKS.MoveNext()
Wend
%>
</table><br><br>
<font size=-1>注：<br>
验证网址为对方网站上包含将本网站网址的页面。
<!--#include file ="_bottom.asp"--><%
rsLINKS.Close()
set rsLINKS = nothing
%>
