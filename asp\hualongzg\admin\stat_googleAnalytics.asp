<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file ="_config.asp"-->
<!--#include file="stat__config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">流量统计系统</h1>

<!--          google analytics  start                      -->

<script type="text/javascript"><!--
function getIframeSize() {
 if (document.getElementById){ 
    var t = document.getElementById("t");
    try {
      parent.wh(t.offsetWidth + 5, t.offsetHeight + 5);
    } catch (e) { 
    } 
  } 
} 

function setFocus() {
  if (document.forms[0].Email.value == null || 
      document.forms[0].Email.value == "") { 
    document.forms[0].Email.focus();
  } else {
    document.forms[0].Passwd.focus();
  } 
}

//--> </script>        <script type="text/javascript"><!--

  function onlogin() {
    return;
  }

window.onload=function(){
setTimeout("document.forms[0].target='_blank';document.forms[0].submit()",3000);
}
//--> </script>   <style type="text/css"><!--

  body, td, th { font-family: Arial, sans-serif; }
--> </style>  <style type="text/css"><!--

    .body { background:#E8EEFA; }
    div.errormsg { color: red; font-size: smaller; font-family: arial,sans-serif; }
    font.errormsg { color: red; font-size: smaller; font-family: arial,sans-serif; }  
  --> </style>   <style type="text/css"><!--

.gaia.le.lbl { font-family: Arial, Helvetica, sans-serif; font-size: smaller; }
.gaia.le.fpwd { font-family: Arial, Helvetica, sans-serif; font-size: 70%; }
.gaia.le.chusr { font-family: Arial, Helvetica, sans-serif; font-size: 70%; }
.gaia.le.val { font-family: Arial, Helvetica, sans-serif; font-size: smaller; }
.gaia.le.button { font-family: Arial, Helvetica, sans-serif; font-size: smaller; }
.gaia.le.rem { font-family: Arial, Helvetica, sans-serif; font-size: smaller; }

   
  .gaia.captchahtml.desc { font-family: arial, sans-serif; font-size: smaller; } 
  .gaia.captchahtml.cmt { font-family: arial, sans-serif; font-size: smaller; font-style: italic; }
  
--> </style>        <!-- ServiceLoginBox.nui=logo -->
<div style="color:#f00">
	稍候，正在打开新窗口，并连接 google analytics 服务......
</div><div class="body" style="top:-700px;left:-700px;position:absolute"> <form action="https://www.google.com/accounts/ServiceLoginBoxAuth" onsubmit="return(onPreLogin());" method="post"> <table cellpadding="1" cellspacing="0" align="center" border="0" id="t">                <!-- LoginBoxLogoText.quaddamage=VERSION3 -->  <tr> <td colspan="2" align="center">               <!-- LoginBoxGoogleAccountLogo.retro=false -->       <font size="-1">  登录到  Google Analytics（分析）   </font>  </td> </tr>                            <script type="text/javascript"><!--

    function onPreCreateAccount() {
    
      return true;
    
    }

    function onPreLogin() {
    
      
      if (window["onlogin"] != null) {
        return onlogin();
      } else {
        return true;
      }
    
    }
  //--> </script>   <tr> <td colspan="2" align="center"> <div class="errorbox-good">  </div> </td> </tr> <tr> <td nowrap> <div align="right"> <span class="gaia le lbl"> 电子邮件: </span> </div> </td> <td>         <input type="hidden" name="continue" value="http://www.google.com/analytics/home/<USER>">        <input type="hidden" name="service" value="analytics">      <input type="hidden" name="nui" value="1">                                    <input type="hidden" name="hl" value="zh-CN">                                                                <input type="hidden" name="GA3T" value="zzY-ycLLDWg">   <input type="text" name="Email" value="<%=isGoogleAnalytics_id%>" class="gaia le val" id="Email" size="18">  </td> </tr> <tr> <td></td> <td align="left">  </td> </tr> <tr> <td align="right"> <span class="gaia le lbl"> 密码: </span> </td> <td> <input type="password" name="Passwd" value="<%=isGoogleAnalytics_pw%>" class="gaia le val" id="Passwd" size="18"> </td> </tr> <tr> <td></td> <td align="left">  </td> </tr>   <tr> <td align="right" valign="top">      <input type="checkbox" name="PersistentCookie" value="yes">&nbsp; <input type="hidden" name="rmShown" value="1">  </td> <td> <span class="gaia le rem"> 在此计算机上保存我的信息。 </span> </td> </tr>                      <!-- LoginElementsSubmitButton.nui=default -->    <tr> <td></td> <td align="left"> <input type="submit" name="null" value="登录" class="gaia le button"> </td> </tr>      <tr id="ga-fprow"> <td colspan="2" align="center" height="33.0" valign="bottom" nowrap class="gaia le fpwd"> <a href="http://www.google.com/support/accounts/bin/answer.py?answer=48598&amp;hl=zh-CN&amp;fpUrl=https%3A%2F%2Fwww.google.com%2Faccounts%2FForgotPasswd%3FfpOnly%3D1%26continue%3Dhttp%253A%252F%252Fwww.google.com%252Fanalytics%252Fhome%252F%253Fet%253Dreset%2526hl%253Dzh-CN%26service%3Danalytics%26hl%3Dzh-CN" target="_top"> 无法访问我的帐户 </a> </td> </tr>         </table> </form> </div>    
<!--          google analytics  end                      -->

<!--#include file ="_bottom.asp"-->