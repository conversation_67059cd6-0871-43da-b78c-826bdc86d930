# **华隆重工企业官网前端设计与开发文档**

### **任务：为“华隆重工”设计并构建新版企业官网前端界面**

**背景:**

- 你是一位顶尖的 Web 设计师与前端开发专家。你的任务是根据“华隆重工”（一家重型机械制造商）的旧版网站数据，为其设计和构建一个全新的、现代化的、国际化的企业网站前端。新网站需要专业、可靠、简洁，并具备出色的用户体验和响应式布局。
- 在元素上实现可访问性功能。例如，标签应该具有 tabindex=“0”、aria 标签、on:click 和 on:keydown 以及类似的属性。
- 要保证的良好 SEO 效果

**核心技术栈:**

- **CSS 框架:** TailwindCSS
- **HTML:** 语义化 HTML5
- **图标库:** FontAwesome
- **布局:** CSS Grid 和 Flexbox

---

### **一、核心设计理念**

1.  **品牌调性:** 专业、稳重、可靠、技术领先、国际化。设计风格需体现重工业的信赖感和实力，避免过于花哨或轻浮的设计。
2.  **用户中心:** 网站主要面向 B2B 客户、行业工程师、潜在合作伙伴和求职者。因此，信息架构必须清晰，导航直观，内容易于查找。
3.  **视觉目标:** 打造一个简洁、大气、有辨识度的界面。通过留白、结构化的布局和统一的视觉元素，提升品牌形象。

---

### **二、网站结构与信息架构 (Sitemap)**

根据提供的旧数据，规划新网站的导航结构如下：

- **主导航栏 (Header Navigation):**
  - **首页 (Home)**
  - **产品中心 (Products)** (鼠标悬停时以下拉菜单展示)
    - 斗轮堆取料机
    - 混匀堆取料机
    - 板式给矿机
    - 链斗卸车机
    - 定量圆盘给料机
    - 板材矫直机
    - 链篦机
  - **客户服务 (Services)** (鼠标悬停时以下拉菜单展示)
    - 技术改造
    - 产品备件
  - **工程案例 (Case Studies)**
  - **走进华隆 (About Us)** (鼠标悬停时以下拉菜单展示)
    - 华隆简介 (Company Profile)
    - 加工设备 (Our Equipment)
    - 华隆剪影 (Gallery)
    - 联系我们 (Contact Us)
  - **人才招聘 (Careers)** (鼠标悬停时以下拉菜单展示)
    - 人才理念 (Philosophy)
    - 招聘流程 (Process)
    - 招聘职位 (Open Positions)
- **页脚导航 (Footer Navigation):**
  - 包含上述所有主要页面的快速链接。
  - 联系信息（地址、电话、邮箱）。
  - 版权信息。

---

### **三、视觉设计规范 (Visual Style Guide)**

1.  **配色方案 (Color Palette):**

    - **主色调 (Primary):** 工业蓝，代表专业、科技和稳定。
      - `#1E3A8A` (深蓝，用于标题、关键按钮、导航高亮)
    - **辅助色 (Secondary):** 灰色系，用于背景、边框和次要文本，营造稳重感。
      - `#F3F4F6` (浅灰，页面背景)
      - `#6B7280` (中灰，次要文本)
    - **强调色 (Accent):** 活力橙，用于号召性用语（CTA）按钮、图标和需要突出的元素。
      - `#F97316` (橙色)
    - **文本颜色 (Text):**
      - `#111827` (深灰，用于正文)

2.  **字体搭配 (Typography):**

    - **全局:** 使用无衬线字体，体现现代感和清晰度。
    - **英文/数字:** `Montserrat` (用于标题), `Roboto` (用于正文)。
    - **中文:** `Noto Sans SC` 或系统默认的 `苹方 (PingFang SC)` / `思源黑体 (Source Han Sans)` / `微软雅黑 (Microsoft YaHei)`。
    - **字号:** 建立清晰的层级，如 H1 > H2 > H3 > Body Text。

3.  **图标库 (Icon Library):**
    - **统一使用 FontAwesome 6 (Free Version)。**
    - **应用场景:**
      - 服务优势、产品特点的旁边，作为视觉辅助。
      - 联系方式前（如电话、邮箱、地址图标）。
      - 多语言切换处使用地球图标 (`fa-globe`)。
      - 社交媒体链接。
    - **图片来源:**
      - 部分来源 D:\Solution\hualongzg\asp\hualongzg 其他的从 Unsplash 等网站获取高质量图片资源

---

### **四、页面布局与模块设计 (Layout & Components)**

**1. 全局布局 (Global Layout):**

- **响应式设计:** 移动端优先。使用 Grid 和 Flexbox 确保在桌面、平板和手机上都有完美的显示效果。
- **标准页面结构:**
  - `<header>`: 固定的导航栏。
  - `<main>`: 页面主体内容。
  - `<footer>`: 页脚信息。
- **最大宽度:** 主体内容区域设置最大宽度（如 `max-w-7xl`），并在屏幕中央显示，以提高大屏幕下的可读性。

**2. 核心组件/页面模块设计:**

- **Header (导航栏):**

  - 左侧：公司 Logo。
  - 中间：主导航链接（参考 Sitemap）。
  - 右侧：
    - **语言切换器:** 一个 `fa-globe` 图标，点击后以下拉菜单形式展示 6 种联合国官方语言选项（阿拉伯语, 中文, 英语, 法语, 俄语, 西班牙语）。
    - **联系我们按钮:** 一个使用强调色的 CTA 按钮。

- **Homepage (首页):**

  - **英雄区 (Hero Section):** 全屏宽的背景图或视频，展示震撼的重型机械或工厂场景。叠加公司 Slogan 和指向“产品中心”的 CTA 按钮。
  - **产品核心展示区:** 使用 Grid 布局，以卡片（Card）形式展示 3-4 个核心产品。每张卡片包含产品图、名称和简短描述。
  - **服务优势区:** 使用 Flexbox 布局，图标+标题+简短文字的形式，并列展示“技术改造”、“产品备件”等优势。
  - **工程案例速览:** 以轮播（Carousel）或卡片 Grid 形式展示最新的 2-3 个工程案例图片和标题。
  - **关于我们简介:** 公司简介的浓缩版本，配一张工厂或团队图片，并附有“了解更多”按钮。

- **Product List Page (产品列表页):**

  - **布局:** 采用 Grid 布局，每行 3-4 个产品卡片。
  - **卡片设计:** 包含高质量产品图片（使用 `next/image` 优化）、产品型号/名称、关键特性（1-2 点）和“查看详情”链接。
  - **侧边栏 (可选):** 可添加一个产品分类筛选器。

- **Product Detail Page (产品详情页):**

  - **布局:** 两栏布局。
  - **左栏:**
    - 产品图片廊 (Image Gallery)，支持多图切换和点击放大。
  - **右栏:**
    - 产品名称 (H1)。
    - 产品简介。
    - **技术参数表:** **关键部分！** 将旧数据中的`<table>`进行重新设计，使用 TailwindCSS 美化，确保清晰易读。
    - 产品特点/优势列表。
    - 资料下载或联系询价按钮。

- **Case Study / About Us Pages (标准内容页):**

  - 采用经典的文章布局，左侧为主要内容区，右侧为相关链接或子菜单导航。
  - 内容区支持富文本，包括段落、标题、图片和视频嵌入。

- **Footer (页脚):**
  - 多列布局。
  - 第一列：Logo 和版权信息。
  - 中间几列：Sitemap 快速链接。
  - 最后一列：详细联系方式（带 FontAwesome 图标）和社交媒体链接。

**最终交付:**
请提供基于上述设计规范和结构的 HTML 和 TailwindCSS 代码。代码需要是组件化的、语义化的，并为后续集成到 Next.js 框架中做好准备。
代码放到 src 文件夹
