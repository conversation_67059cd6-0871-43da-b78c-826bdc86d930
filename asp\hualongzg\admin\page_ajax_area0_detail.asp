<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
actionok = request.querystring("actionok")
If actionok <> "" Then
	list_id = request.querystring("list_id")
	If actionok = "add" Then
		set rsPAGE = Server.CreateObject("ADODB.Recordset")
		sql = "SELECT page.title,page.sort_id,page.subid,page.this_not_del,page.keywords,page.content,page.top_submit,page.top_style,page.imgsmall,page.img,page.imgsmallwidth,page.imgsmallheight,page.imgwidth,page.imgheight,page.page_style_id FROM page WHERE subid = " & list_id
		rsPAGE.open sql,MM_conn_String,3,3
		rsPAGE.addnew
		If request.querystring("imgsmall") <> "" Then 
			rsPAGE("imgsmall") = request.querystring("imgsmall")
			rsPAGE("imgsmallwidth") = imgDimension(request.querystring("imgsmall"),"w")
			rsPAGE("imgsmallheight") = imgDimension(request.querystring("imgsmall"),"h")
		End if
		If request.querystring("img") <> "" Then 
			rsPAGE("img") = request.querystring("img")
			rsPAGE("imgwidth") = imgDimension(request.querystring("img"),"w")
			rsPAGE("imgheight") = imgDimension(request.querystring("img"),"h")
		End If
		
		rsPAGE("subid") = list_id
		rsPAGE("title") = VBsUnEscape(request.querystring("title"))
		If  request.querystring("page_style_id") <> "" then
			rsPAGE("page_style_id") = request.querystring("page_style_id")
		Else
			rsPAGE("page_style_id") = 0
		End if
		rsPAGE("keywords") = Replace(VBsUnEscape(request.querystring("keywords")),"，",", ")
		rsPAGE("content") = VBsUnEscape(request.Form)
		rsPAGE.update
		rsPAGE("sort_id") = rsPAGE.bookmark
		rsPAGE.update
		rsPAGE.close()
		Set rsPAGE =Nothing
	ElseIf actionok="mod" then'修改数据
		set rsPAGE = Server.CreateObject("ADODB.Recordset")
		sql = "SELECT page.title,page.this_not_del,page.keywords,page.content,page.top_submit,page.top_style,list.style_id,page.data_mod FROM page inner join list on page.subid=list.id WHERE pageid = " & request.querystring("pageid")
		rsPAGE.open sql,MM_conn_String,3,2
		rsPAGE("title") = VBsUnEscape(request.querystring("title"))
		rsPAGE("keywords") = Replace(VBsUnEscape(request.querystring("keywords")),"，",", ")
		If request.querystring("data_mod") <> "" Then rsPAGE("data_mod") = VBsUnEscape(request.querystring("data_mod"))
		rsPAGE("content") = VBsUnEscape(request.Form)
		rsPAGE.update
		rsPAGE.close()
		Set rsPAGE =Nothing	
	End if
else	'如果不是提交增加或修改的数据，而是空白增加表单或者查看表单
	list_id = request.querystring("list_id")
	list_style_id = request.querystring("list_style_id")
	action = request.querystring("action")
	htmlbuilderstyle=request.querystring("style")
	pageid = request.querystring("pageid")
	page_style_id = request.querystring("page_style_id")
	If list_style_id = 1 Or list_style_id =2 Or page_style_id =1 Then this_list_style_id = 1

	If this_list_style_id = 1 Then iframestr = "<a href=""javascript:void(null)"" onclick=""sswitch(this)"" class=""smallswitch"" rel=""htmlbuilder0""></a>"

	If action <> "add" then
		set rsPAGE = Server.CreateObject("ADODB.Recordset")
		sql = "SELECT page.data_mod,page.pageid, page.title,page.subid,page.this_not_del,page.keywords,page.content,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,page.img,page.imgwidth,page.imgheight,page.top_submit,page.top_style,list.style_id,page.page_style_id,list.date_display_on FROM page inner join list on page.subid=list.id WHERE pageid = " & pageid
		rsPAGE.open sql,MM_conn_String,1,1
		list_style_id = rsPAGE("style_id")
		page_style_id = rsPAGE("page_style_id")
		If list_style_id = 1 Or list_style_id =2 Or page_style_id =1 Then this_list_style_id = 1
	%>
		<form method="post" name="frmpdetail" id="frmpdetail" action="#" class="b_confrm list<%=this_list_style_id%>">
		<%If Trim(rsPAGE("imgsmall")) <> "" or rsPage("style_id") = 1 or rsPage("style_id") = 2  then%>
			<p class="thumb" id="img_page<%=pageid%>"><span>特征图片</span><a href="../upload/<%=rsPAGE("img")%>" class="img" target="_blank"><img src="../upload/<%=rsPAGE("imgsmall")%>" alt="点击看大图" /></a><a href="javascript:void(null)" onclick="modlistimg('mod,img_page<%=pageid%>,page,<%=pageid%>,imgsmall,<%=img_bigwidth%>,<%=img_bigheight%>,img,<%=img_big2width%>,<%=img_big2height%>')">修改</a></p>
		<%End if%>
			<p><label>页面标题</label><input type="text" name="title" id="title" class="i300" value="<%=rsPAGE("title")%>" /></p>
			<p><label>关键词</label><input id="keywords" name="keywords" type="text" value="<%=rsPAGE("keywords")%>" class="i300" />（用逗号分开）</p>
			<p><label>内容</label><%=iframestr%><textarea id="content" name="content" rows="15" cols="80" style="display:none"><%=rsPAGE("content")%></textarea>
			<iframe ID="htmlbuilder0" src="htmlbuilder/htmlbuilder.asp?id=content&style=<%=htmlbuilderstyle%>" frameborder="0" scrolling="no" width="550" height="300"></iframe>
			</p>
			<%If rsPAGE("date_display_on") = True then%>
			<p><label>修改日期</label>
			<input style="ime-mode:disabled" name="data_mod" id="data_mod" value="<%=rsPAGE("data_mod")%>" /> 注意日期格式正确
			</p>
			<%End if%>
			<p><input type="submit" value="修改" class="isubmit" onclick="return Pageok();" /><input type="hidden" id="actionok" name="actionok" value="mod" /><input type="hidden" id="pageid" name="pageid" value="<%=rsPAGE("pageid")%>" /><input type="hidden" id="list_id" name="list_id" value="<%=rsPAGE("subid")%>" /></p>
		</form><%
	Else%>
		<form method="post" name="frmpdetail" id="frmpdetail" action="#" class="b_confrm list<%=this_list_style_id%>">
		<%If this_list_style_id = 1 then%>
			<p class="thumb" id="img_pageadd"><span>特征图片</span><a href="javascript:void(null)" onclick="modlistimg('add,img_pageadd,page,imgsmall,<%=img_bigwidth%>,<%=img_bigheight%>,img,<%=img_big2width%>,<%=img_big2height%>')">选图</a></p>
		<%End if%>
			<p><label>页面标题</label><input type="text" name="title" id="title" class="i300" /></p>
			<p><label>关键词</label><input id="keywords" name="keywords" type="text" class="i300" /></p>
			<p><label>内容</label><%=iframestr%><textarea id="content" name="content" rows="15" cols="80" style="display:none"></textarea><iframe ID="htmlbuilder0" src="htmlbuilder/htmlbuilder.asp?id=content&style=<%=htmlbuilderstyle%>" frameborder="0" scrolling="no" width="550" height="300"></iframe>
			</p>
			<p><input type="submit" value="增加" class="isubmit" onclick="return Pageok();" /><input type="hidden" id="actionok" name="actionok" value="add" /><input type="hidden" id="pageid" name="pageid"><input type="hidden" id="list_id" name="list_id" value="<%=list_id%>" /></p><p id="addimginput"></p>
		</form>
	<%
	End If
	If action <> "add" then
		rsPAGE.close()
		Set rsPAGE = Nothing
	End If
End if'如果不是提交增加或修改的数据，而是空白增加表单或者查看表单
%>