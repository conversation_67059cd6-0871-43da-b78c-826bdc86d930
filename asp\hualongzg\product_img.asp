<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<!--#include file="_config.asp" -->
<!--#include file="_extend.asp" --><%
Dim page_para
Dim proarea_para(2)		'对于产品类和产品，必须有这行
Dim pro_out(8)				'对于产品必须有这行
Call pro_out_sub(SafeRequest2(pro_querystingid,1,""))
page_para = Array(Left(pro_out(1),4),"area")

Dim product_out,current_cid		'对于产品list，必须有这行
Call product_out_sub(pro_out(3),1,"没有图集")
%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><%=pro_out(4)%>产品图集</title>
<meta name="Keywords" content="<%=front_Keywords%>" />
<meta name="Description" content="<%=front_Description%>" />
<link rel="stylesheet" type="text/css" href="layout.css" />
<script type="text/javascript" src="common.js"></script>
<script type="text/javascript" src="js/jquery.lightbox-0.5.js" charset="utf-8"></script>
<script type="text/javascript" src="Portalclient.js" charset="utf-8"></script>
</head>
<body id="P01">
<div class="page_wrapper">
	<div class="logo">
		<a href="default.asp" id="logo"><img src="images/hualongzg_logo.jpg" width="165" height="41" alt="hualongzg logo" /></a>
		<div id="language">
			<a href="#" class="en">English</a><a href="default.asp" class="cn">中文</a>
		</div>
       <form method="get" action="#" id="frmsearch">
          <fieldset>
			 <input type="text" name="keywords" id="searchArea" value="关键词"  />
			 <input id="searchGo" type="submit" class="go" value="" />
          </fieldset>
		</form>
	</div>
	<div class="flash"></div>
	<div class="navigator">
		<ul><li><a href="default.asp">首　　页</a></li><%=nav_tree("01",0)%></ul>
	</div>
	<div class="content_wrapper">
		<div class="sub_navigator">
			<h2>产品中心</h2>
			<ul><%=nav_tree("0101",0)%></ul>
		</div>
		<div class="content">
			<div class="crumb"><p><span>首页</span></p><%=pro_path(pro_out(1))%></div>
			<h1><span><%=pro_out(4)%></span></h1>
			<div class="prodetail_navigator">
				<ul>
					<li><a href="product_detail.asp?<%=pro_querystingid%>=<%=pro_out(3)%>">产品说明</a></li>
					<li><a href="product_case.asp?<%=pro_querystingid%>=<%=pro_out(3)%>">产品案例</a></li>
					<li><a class="ac" href="product_img.asp?<%=pro_querystingid%>=<%=pro_out(3)%>">产品图集</a></li>
					<li><a href="buy.asp?<%=pro_querystingid%>=<%=pro_out(3)%>&amp;pro=<%=server.urlencode(pro_out(4))%>">在线订购</a></li>
				</ul>
			</div>
			<div class="content_in">
				<ul class="list1"><%=product_out%></ul>
			</div>
		</div>
	</div>
	<div class="copyright">
		<p><%=front_copy%></p>
	</div>
</div>
</body>
</html>