<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<%
Response.Buffer = True
Response.ExpiresAbsolute = Now() - 1
Response.Expires = 0
Response.CacheControl = "no-cache"
%>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="../_md5.asp"-->
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

'''''强制过滤一些常用字符开始
no_id_str = "username,user,name,u_name,administrators,userid,adminuser,adminpass,adminname,user_name,admin_name,usr_n,usr,dw,nc,uid,admin,admin_user,admin_username,user_admin,adminusername"
no_pw_str ="userpass,password,adminpassword,adminpass,user_pass,admin_password,user_password,user_pwd,adminpwd,admin_pass,admin_password"
no_id_str_1 = split(no_id_str,",")
no_pw_str_1 = split(no_pw_str,",")

if (request.form("id") = back_site) or (request.form("pw") = back_site) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('admin.asp');</script>"
	Response.End
end if

for no_admin_i = lbound(no_id_str_1) to ubound(no_id_str_1)
	if request.form("id") = no_id_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('admin.asp');</script>"
	Response.End
	end if
next

for no_admin_i = lbound(no_pw_str_1) to ubound(no_pw_str_1)
	if request.form("pw") = no_pw_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('admin.asp');</script>"
	Response.End
	end if
next

'''''强制过滤一些常用字符结束

  MM_editConnection = MM_conn_STRING
  MM_editTable = "admin"
  MM_editColumn = "id"
  MM_recordId = "'" + Request.Form("MM_recordId") + "'"
  MM_editRedirectUrl = "admin.asp"
  MM_fieldsStr  = "id|value|pw|value|email|value"
  MM_columnsStr = "id|',none,''|pw|',none,''|email|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
  '此处对ID、PW进行MD5加密处理
	if i=0 or i=2 then
    MM_fields(i+1) = md5(CStr(Request.Form(MM_fields(i))))
	else
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
	end if
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

set rsADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsADMINMAIL.ActiveConnection = MM_conn_STRING
rsADMINMAIL.Source = "SELECT email FROM admin"
rsADMINMAIL.CursorType = 0
rsADMINMAIL.CursorLocation = 2
rsADMINMAIL.LockType = 3
rsADMINMAIL.Open()
rsADMINMAIL_numRows = 0
%>
<%'发送email
real_subject= "网站" & back_siteurl & "修改了管理员帐号"
real_content="尊敬的网管：<br><br>网站管理员修改了该网站的管理员帐号，如下：<br>"
real_content = real_content & "帐号：<font color=ff0000>" & request.form("id") & "</font><br>"
real_content = real_content & "密码：<font color=ff0000>" & request.form("pw") & "</font><br>"
real_content = real_content & "电子信箱：" & request.form("email") & "<br><br>" & "请保存此信。"
real_mailfrom = request.form("email")
real_email= request.form("email")
real_body= "<font size=2>" & real_content & "<br><br><br>　　致<br>礼!<br><br><div align=right><a href='http://" & back_siteurl & "'>" & back_coname & "</a></font></div>"
Set JMail = Server.CreateObject("JMail.SMTPMail") 
JMail.Priority = 1 ' 1 - highest priority (Urgent)  3 - normal  5 - lowest 
JMail.Sender = real_mailfrom
JMail.SenderName = back_coname
JMail.ContentType = "text/html"
Jmail.ISOEncodeHeaders = false
JMail.replyto = real_mailfrom
JMail.Subject = real_subject
JMail.AddRecipient rsADMINMAIL.Fields.Item("email").Value
JMail.ServerAddress = back_smtpsever
JMail.Charset = "utf-8"
JMail.Body = real_body
JMail.Execute
JMail.ClearRecipients()
JMail.AddRecipient(back_adminmail)
JMail.Execute
set jmail=nothing
%>
<%
rsADMINMAIL.Close()

end if
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
set rsADMIN = Server.CreateObject("ADODB.Recordset")
rsADMIN.ActiveConnection = MM_conn_STRING
rsADMIN.Source = "SELECT * FROM admin"
rsADMIN.CursorType = 0
rsADMIN.CursorLocation = 2
rsADMIN.LockType = 3
rsADMIN.Open()
rsADMIN_numRows = 0
%>
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">系统管理员账号维护</h1>
<script LANGUAGE="javascript">
<!--
function check()
{
if (document.form1.id.value==""){
  alert("请输入账号");
  document.form1.id.focus();
  return false;
  }
if (document.form1.pw.value==""){
  alert("请输入密码");
  document.form1.pw.focus();
  return false;
  }

if (document.form1.id.value == document.form1.pw.value){
  alert("账号与密码不能相同，请重新输入");
  document.form1.pw.focus();
  return false;
  }

if (document.form1.pw.value.length < 6){
  alert("密码长度小于6位，请重新输入");
  document.form1.pw.focus();
  return false;
  }
if (document.form1.pw.value.length < 6){
  alert("密码长度小于6位，请重新输入");
  document.form1.pw.focus();
  return false;
  }

if (document.form1.pw2.value==""){
  alert("请再次输入密码确认");
  document.form1.pw2.focus();
  return false;
  }

if (document.form1.pw2.value != document.form1.pw.value){
  alert("二次输入的密码不同，请重新输入");
  document.form1.pw.focus();
  return false;
  }

if (document.form1.email.value==""){
  alert("请输入信箱");
  document.form1.email.focus();
  return false;
  }
}
//-->
</script> 
	<form method="POST" action="<%=MM_editAction%>" name="form1" onSubmit="return check()">
	  <table cellspacing=0 cellpadding=4 align=center>
		<tr> 
		  <td nowrap align="right">新的帐号</td>
		  <td> 
			<input type="text" name="id" size="32"  style="width:250px">
		  </td>
		</tr>
		<tr> 
		  <td nowrap align="right">新的密码</td>
		  <td> 
			<input name="pw" type=password size="32" style="width:250px">
		  </td>
		</tr>
		<tr> 
		  <td nowrap align="right">密码确认</td>
		  <td> 
			<input name="pw2" type=password size="32" style="width:250px">
		  </td>
		</tr>
		<tr> 
		  <td nowrap align="right">Email</td>
		  <td> 
			<input type="text" name="email" value="<%=(rsADMIN.Fields.Item("email").Value)%>" size="32"  style="width:250px">
		  </td>
		</tr>
		<tr> 
		  <td nowrap align="right">&nbsp;</td>
		  <td> 
			<input type="submit" value="更新">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsADMIN.Fields.Item("id").Value %>">
	</form>
<font color=ff0000>注意：系统管理员（网站管理员）email必须使用本站的已经设置好的正式信箱（即以<%=(back_site)%>为后缀的信箱）。<br>
</font>
<!--#include file ="_bottom.asp"-->
<%
rsADMIN.Close()
%>