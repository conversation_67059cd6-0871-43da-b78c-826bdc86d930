<%
'--------------------------------
'　　表单设置文件
'--------------------------------
'调用 call front_form(form_style,form_id)
'form_style 的值
'0 产品订购
'1 产品咨询
'2 应聘 
'3 投诉
'4 咨询问题
'5 服务请求
'6 留言
'form_id 为对应的guestbook_cate表的 id值

'在调用程序中要包含 <!--#include file="_form.asp" -->
'在调用的页面头部包含 <meta name="save" content="history" /> 
'＊＊提交后的提示文字的风格是<h1>，注意与css相配合
'＊＊产品订购的request变量都是"pro"

dim form_must_color			'必填的项目的名称颜色
dim form_submit_style		'提交表单的风格 1为文本，2为图形（如果为图形，必须定义form_submit_button）

form_must_color = "#FF6500" '此项在用css排版中无用，应修改css中的.needs 样式
form_submit_style = 1

'如果form_submit_style =2 要作出所有的 submit_[form_style].gif图出来

'如果有英文，指定 this_lan 变量为 "en" 就可以
%>