<HTML>
<HEAD>
<META content="text/html; charset=UTF-8" http-equiv=Content-Type>
<style>
BODY {PADDING:5PX}
TD,BODY,SELECT,P,INPUT {FONT-SIZE:9PT}
</style>

<script language="JavaScript" src="dialog.js"></script>

<script language=javascript>
var sAction = "INSERT";
var sTitle = "插入";
var sel = dialogArguments.htmlbuilder.document.selection.createRange();
sel.type = dialogArguments.htmlbuilder.document.selection.type;

var el;
var sText = "";
var sBehavior = "";

if (sel.type=="Control") {
	if (sel.item(0).tagName=="MARQUEE"){
		sAction = "MODI";
		sTitle = "修改";
		el = sel.item(0);
		sBehavior = el.behavior;
		sText = el.innerHTML;
	}
}

document.write("<title>字幕属性（" + sTitle + "）</title>");


// 单选的点击事件
function check(){
	sBehavior = event.srcElement.value;
}

// 初始值
function InitDocument() {
	d_text.value = sText;
	switch (sBehavior) {
	case "scroll":
		document.all("d_behavior")[0].checked = true;
		break;
	case "slide":
		document.all("d_behavior")[1].checked = true;
		break;
	default:
		sBehavior = "alternate";
		document.all("d_behavior")[2].checked = true;
		break;
	}

}
</script>


<SCRIPT event=onclick for=Ok language=JavaScript>
	sText = d_text.value;
	if (sAction == "MODI") {
		el.behavior = sBehavior;
		el.innerHTML = sText;
	}else{
		dialogArguments.insertHTML("<marquee behavior='"+sBehavior+"'>"+sText+"</marquee>");
	}
	window.returnValue = null;
	window.close();
</script>
</HEAD>

<body bgcolor=menu onload="InitDocument()">

<table border=0 cellpadding=0 cellspacing=0 align=center>
<tr><td>
	<FIELDSET align=left>
	<LEGEND></LEGEND>
	<table border=0 cellspacing=5 cellpadding=0>
	<tr valign=middle><td>文本:&nbsp;</td><td><input type=text id="d_text" size=50 value=""></td></tr>
	<tr valign=middle><td>表现:&nbsp;</td><td><input onclick="check()" type="radio" name="d_behavior" value="scroll"> 滚动条 <input onclick="check()" type="radio" name="d_behavior" value="slide"> 幻灯片 <input onclick="check()" type="radio" name="d_behavior" value="alternate"> 交替</td></tr>
	</table>
	</FIELDSET>

</td></tr>
<tr><td height=10></td></tr>
<tr><td align=right><input type=submit value='  确定  ' id=Ok>&nbsp;&nbsp;<input type=button value='  取消  ' onclick="window.close();"></td></tr>
</table>

</body>
</html>
