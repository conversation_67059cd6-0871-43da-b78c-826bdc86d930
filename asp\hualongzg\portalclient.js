document.write("<style type=\"text/css\">"); 
document.write(".content_in{text-justify: inter-ideograph; text-align: justify;}");
document.write("</style>"); 

$(function(){
	$("div.logo").eq(0).append("<span id=\"sresult\"><img src=\"images/lightbox-ico-loading.gif\" width=\"24\" height=\"24\" alt=\"正在传送数据...\" /></span>");
	$("#sresult").css("opacity","0.93","display","none");
	$("#frmsearch").submit(function(){searchinput();return false;});
	$("searchGo").click(function(){searchinput();return false;});
	$("#searchArea").click(function(){
		if ($("input[name='keywords']").val() == "关键词"){
		$("input[name='keywords']").val("");
		}
	});

    var navigatorLI = $("div.navigator li,div.sub_navigator li");
	for (var i = navigatorLI.length; i > 0; i--){
		var childA = navigatorLI.eq(i-1).children("a");
		if(childA.attr("href").indexOf(":") > 0) {
			childA.attr("href", $("li:eq(0)>a", childA.next()).attr("href"));
		}
	}

	$("div.navigator li:has(ol)").hover(function(){
			if ($(this).children("ol").children("li").length > 1){$(this).children("ol").show();$(this).children("a").eq(0).addClass("ac")}
		},function(){
			if ($(this).children("ol").children("li").length > 1){$(this).children("ol").hide();$(this).children("a").eq(0).removeClass("ac")}
		});

	$("div.navigator ol").hover(function(){
			$(this).show();
		},function(){
			$(this).hide();
		});


	$("div.sub_navigator li:has(ol)").hover(function(){
			if ($(this).children("ol").children("li").length > 1){$(this).children("ol").show();$(this).children("a").eq(0).addClass("ac")}
		},function(){
			if ($(this).children("ol").children("li").length > 1){$(this).children("ol").hide();$(this).children("a").eq(0).removeClass("ac")}
		});

	$("ul.list0 li:even").addClass("even");
	if ($("ul.list1").length > 0) {	$("ul.list1 li a.thumb").lightBox();}
	if ($("ul.list2").length > 0) {	$("ul.list2 li a.thumb").lightBox();}
    if ($("#media").length > 0) {mediaPlay();}

	/**/

	$("div.navigator > ul > li > ol a").css("opacity","0.93");

	$("body#P01 li#menu01 > a").addClass("ac");
	$("body#P02 li#menu02 > a").addClass("ac");
	$("body#P03 li#menu03 > a").addClass("ac");
	$("body#P04 li#menu04 > a").addClass("ac");
	$("body#P05 li#menu05 > a").addClass("ac");
	$("body#P06 li#menu06 > a").addClass("ac");
	$("body#P07 li#menu07 > a").addClass("ac");
	$("body#P08 li#menu08 > a").addClass("ac");
	$("body#P09 li#menu09 > a").addClass("ac");

	if($("body").attr("id") == "P00" ){
		$("#product > dd > ul > li > a").eq(0).addClass("ac");
		$("#product > dd > ul > li > div").eq(0).show();
		$("#product > dd > ul > li > a").mouseover(function(){
			$("#product > dd > ul > li").each(function(){
				$(this).children("a.proname").eq(0).removeClass("ac");
				$(this).children("div").eq(0).hide();
			});
			$(this).addClass("ac");$(this).next().show();
		});
	}
	if($("body").attr("id") == "P00" ) {$("div.ba").eq(0).html(fplay("images/home.swf","986","377"));}
    if($("body").attr("id") != "P00") {$("div.flash").eq(0).html(fplay("images/page.swf","986","108"));}

})

function mediaPlay (){
        var ml = 0;                                             //当前 marginLeft 是 0；
        var win = $("#win");
        win.before("<img class='left' src='images/turn_left.gif' width='9' height='51'>");
        win.after("<img class='right' src='images/turn_right.gif' width='9' height='51'>");
		var winli = $("li", win);
        
		if(winli.length > 4) {
            win.next().css("visibility", "visible");
        }

        var img_btn = $("#media").find(".slide_pl").find("img").not(".point").not(".min_scr");

        var rec = 1;
        img_btn.click(function() {
            var tar_ul = $(this).parent().children("#win").children("ul");
            if (this.className == "left") {
                if (rec > 1) {
                    ml = ml + 98;
                    tar_ul.animate({marginLeft: ml}, 800);

                    if (rec == 2) {
                        win.prev().css("visibility", "hidden");
                    }
                    win.next().css("visibility", "visible");    
                    rec--;
                }
            } else {
                if (rec <= tar_ul.children("li").length - 4) {
                    ml = ml - 98;
                    tar_ul.animate({marginLeft: ml}, 800);

                    win.prev().css("visibility", "visible");
                    if (rec == tar_ul.children("li").length - 4) {
                        win.next().css("visibility", "hidden");   
                    }
                    rec++;
                }
            }
        });

        var img_scr = $("#media").find(".min_scr");
        var img_pt = $("#media").find(".point");
        var title = $("#media > ul > .avi_txt > .title");
        var txt = title.next();

        title.text(img_scr.eq(0).next().next().text());
        txt.text(img_scr.eq(0).next().next().next().text());

        img_scr.click(function() {
            var cur = $(this);
            var rel = $("img", cur).attr("rel"); 
            var adr = "images/media_cn.swf?media="+rel;
            cur.parent().append(img_pt);

            var cur_title = cur.nextAll(".title").text();
            var cur_txt = cur.nextAll(".txt").text();
            title.text(cur_title);
            txt.text(cur_txt);

            document.getElementById("flashcontent").innerHTML = fplay(adr,"480","360","full");
        });
        
        //初始化指向。
        winli.eq(0).append(img_pt);

        //页面加载自动播放。
        var adr_rel = $("img", winli.eq(0)).attr("rel");        //获取第一个视频的 rel。
        var adr = "images/media_cn.swf?media="+adr_rel;
        document.getElementById("flashcontent").innerHTML = fplay(adr,"480","360","full");  //不需要判断了。
}

function fplay(swf, swfwidth, swfheight,full) {
    if (navigator.userAgent.indexOf('Firefox') > - 1) {
		if (full == "full"){
	        return('<embed src="' + swf + '" width="' + swfwidth + '" height="' + swfheight + '" quality="high" wmode="Opaque" allowFullScreen="true" allowScriptAccess="always"></embed>');
		}else{
        return('<embed src="' + swf + '" width="' + swfwidth + '" height="' + swfheight + '" quality="high" wmode="transparent"></embed>');}
    } else {
		if (full == "full"){
			return('<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="' + swfwidth + '" height="' + swfheight + '"><param name="movie" value="' + swf + '" /><param name="quality" value="high" /><param name="menu" value="false" /><param name="wmode" value="Opaque" /><param name="allowScriptAccess" value="always" /><param name="allowFullScreen" value="true" /></object>');
		}else{
        return('<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="' + swfwidth + '" height="' + swfheight + '"><param name="movie" value="' + swf + '" /><param name="quality" value="high" /><param name="menu" value="false" /><param name="wmode" value="transparent" /></object>');}
    }
}

function searchinput(){
	if ($("input[name='keywords']").attr("value") == "" || $("input[name='keywords']").attr("value") == "关键词"){
		alert("请输入关键词");
		$("input[name='keywords']").val("");
	    $("input[name='keywords']").focus();
		return false;
    }else{
		$("input[name='keywords']").attr("value",trim($("input[name='keywords']").attr("value")));
		$("#sresult").css("display","block");
		$("#sresult").html("<img src=\"images/lightbox-ico-loading.gif\" width=\"24\" height=\"24\" alt=\"正在传送数据...\" />");

		showrequest("search_ajax.asp?keywords=" + encodeURIComponent(escape($("input[name='keywords']").attr("value"))),"sresult",false);
		cls_button();
	}
}

function trim(a_strVarContent) {
var pos1, pos2, newstring;pos1 = 0;pos2 = 0;newstring = "";
	if ( a_strVarContent.length > 0 ) {
		for( i=0; i<=a_strVarContent.length; i++) { 
			if ( a_strVarContent.charAt(i) == " " ) pos1 = pos1 + 1;
			else break;
		} 
		for( i=a_strVarContent.length; i>=0 ; i--) {
			if ( a_strVarContent.charAt(i) == " " ) pos2 = pos2 + 1;
			else break;
		}
		newstring = a_strVarContent.substring(pos1, a_strVarContent.length-pos2) 
	} 
	return newstring; 
} 

function searchp(keywords,r,p,ttype){
	showrequest("search_ajax.asp?keywords=" + keywords + "&r=" + r + "&p=" + p + "&type=" + ttype,"sresult",false);cls_button();
	return false;
}

function cls_button(){
	nCol = $("#sresult > A");
	for (i=0;i<nCol.length;i++){
		if (nCol[i].className == "cls"){
			nCol[i].onclick = function(){
				this.parentNode.style.display = "none";
			}
		}
	}
}

var http_request = false;
function showrequest(url,element,asyn) {
	var isFFCLS = true;
	http_request = false;
    if (window.XMLHttpRequest) {
        http_request = new XMLHttpRequest();
        if (http_request.overrideMimeType) {
            http_request.overrideMimeType('text/xml');
        }
    } else if (window.ActiveXObject) {
        try {
            http_request = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            try {
            http_request = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {}
        }
    }
    if (!http_request) {alert('Giving up :( Cannot create an XMLHTTP instance');return false;}
    http_request.onreadystatechange = sub_showrequest;
    http_request.open('GET', url, asyn);
    http_request.send(null);
	if ((!asyn) && (isFFCLS)) sub_showrequest();
	function sub_showrequest(){
	    if (http_request.readyState == 4) {
        if (http_request.status == 200) {
			isFFCLS = false;
			document.getElementById(element).innerHTML = http_request.responseText;
        } else {alert('There was a problem with the request.');}
		}	
	}
}