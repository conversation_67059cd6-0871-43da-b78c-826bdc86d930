<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
dim rsADDPAGE1_MMColParam
rsADDPAGE1_MMColParam = session("isadmin_level")

set rsADDPAGE1 = Server.CreateObject("ADODB.Recordset")
rsADDPAGE1.ActiveConnection = MM_conn_STRING
If top_node <> "" Then
	sql = "SELECT * FROM area where left(cate_lcode,len('" & top_node & "')) = '" & top_node & "' AND len(cate_lcode) > len('" & top_node & "') and cate_type_id <> 3 ORDER BY cate_lcode ASC"
else
	sql = "SELECT * FROM area ORDER BY cate_lcode ASC"
End If
rsADDPAGE1.open sql,MM_conn_STRING,1,1
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">页面管理</h1>
<div style="clear:both;width:100%;padding-top:10px">
<div style="float:left">
<p>选择区域</p>
<form name="form1" method="get" action="page.asp" id="dissel">
<p style="float:left">
	<select name="class" size="<%=cate_select_size%>"><%

While (NOT rsADDPAGE1.EOF)
	cate_dot = ""
	cate_dot_i = rsADDPAGE1("cate_level") -1
	cate_dot_i2 = 0
	while cate_dot_i > 0 
		if cate_dot_i2 = 0 then
		cate_dot = "　－"
		else
		cate_dot = "　" & cate_dot
		end if
		cate_dot_i = cate_dot_i - 1
		cate_dot_i2 = cate_dot_i2 + 1
	wend 
If session("isadmin") = True Then
%>		<option value="<%=(rsADDPAGE1.Fields.Item("cate_id").Value)%>" <%If rsADDPAGE1("cate_pro_sign")= false Then response.write("disabled=""disabled""")%> <%If CStr(rsADDPAGE1("cate_id")) = request.querystring("class") Then response.write(" selected=selected")%>><%=cate_dot%><%=(rsADDPAGE1.Fields.Item("cate_name").Value)%></option>
<%Elseif inint2(session("flag"),CStr(rsADDPAGE1("cate_id"))) = true then
%>
		  <option value="<%=(rsADDPAGE1.Fields.Item("cate_id").Value)%>" <%If rsADDPAGE1("cate_pro_sign")= false Then response.write("disabled=""disabled""")%> <%If CStr(rsADDPAGE1("cate_id")) = request.querystring("class") Then response.write(" selected=selected")%>><%=cate_dot%><%=(rsADDPAGE1.Fields.Item("cate_name").Value)%></option>
		  <%
end if

  rsADDPAGE1.MoveNext()
Wend
If (rsADDPAGE1.CursorType > 0) Then
  rsADDPAGE1.MoveFirst
Else
  rsADDPAGE1.Requery
End If
rsADDPAGE1.Close()
set rsADDPAGE1 = nothing
%></select></p>
<p style="float:left;padding:0 10px 0 10px"><input type="submit" name="Submit" value="选定" style="width:100px"></p>
	  </form>
</div>
<%If request.querystring("class")<> "" Then '如果选择了区域%>
<div style="float:left;padding-left:80px">
<%
set rs=server.createobject("adodb.recordset")
sql="select list.name,list.id,list.sort_id,area.cate_type_id,list_style.style_id from (list inner join area on list.cate_id=area.cate_id) inner join list_style on list_style.style_id=list.style_id where list.cate_id=" & request.querystring("class") & " order by list.sort_id,list.id asc"
rs.open sql,MM_conn_STRING,1,1
If Not rs.eof Then
	pro_name_str = cate_type_list_name(request.querystring("class"))
%>
<p>选择<%=pro_name_str%></p>
<script type="text/javascript">
//<![CDATA[
function chkselect(){
    if (document.form2.subclass.value == "")
		{
		alert("请选择<%=pro_name_str%>");
		document.form2.subclass.focus();
		return false;
		}
return true;}
//]]>
</script>
<form name="form2" method="get" action="page+2.asp">
<p style="float:left">
	<select name="subclass" size="<%=cate_select_size%>"><%
While Not rs.eof%>
	<option value="<%=rs("id")%>"<%If rs("style_id") = 99 Then response.write("disabled=""disabled""")%>><%=rs("name")%></option><%
rs.movenext()
Wend
%>
	</select>
</p>
<p style="float:left;padding:0 10px 0 10px"><input type="submit" name="Submit" value="选定" style="width:100px" onclick="return chkselect()"></p>
	  </form>
<%
Else
	response.write("<h3>本区域没有列表，请在系统管理里增加。</h3>")
End if
rs.close()
Set rs=nothing
%>
</div>
<%End if%>
</div>
<!--#include file ="_bottom.asp"-->