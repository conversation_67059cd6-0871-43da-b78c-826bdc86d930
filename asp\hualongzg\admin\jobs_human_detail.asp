<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="event__config.asp"-->
<%
Function hs_title'尊称
	hs_title = ""
	hs_title = rsRDETAIL("surname")
	If rsRDETAIL("sex") = "male" Then 
		hs_title = hs_title & "先生"
	Else
		hs_title = hs_title & "小姐"	
	End if
End Function

dim rsRDETAIL__MMColParam
rsRDETAIL__MMColParam = request.querystring("id")
dim rsRDETAIL
set rsRDETAIL = server.createobject("adodb.recordset")
sql ="select jobs_human.* from jobs_human where h_id =" + Replace(rsRDETAIL__MMColParam, "'", "''") + ""
rsRDETAIL.open sql,MM_conn_STRING,1,1

Dim t_action
t_action =request.querystring("action")

Select Case t_action
Case "score_submit"
set rs = server.createobject("adodb.recordset")
sql = "select score from jobs_human where h_id=" & request.querystring("id")
rs.open sql,MM_conn_STRING,3,3
If request.Form("score") <> "" Then rs("score") = request.Form("score")
rs.update
rs.close()
Set rs=Nothing
response.redirect("jobs_human_detail.asp?id=") & request.querystring("id")
End select
%>
<!--#include file ="_head.asp"-->
<h1 class="b_ch1"><%=rsRDETAIL("surname")%><%=rsRDETAIL("f_name")%>的简历详细信息与处理过程</h1>
<table id="detail" style="display:block;margin-right:10px;float:left;width:500px;">
<tr><td colspan="8" class="p14">应聘职位：<%=rsRDETAIL("job_name")%></td></tr>
<%
'面试评介
If rsRDETAIL("this_type") = 2 Then%>
<tr><td colspan=8 style='background-color:#ececec;padding:5px;margin-bottom:10px;'>
<table class=table0 border=0><form action="jobs_human_detail.asp?id=<%=rsRDETAIL__MMColParam%>&action=score_submit" method=post name="frm_score" onsubmit="return checkscore()">
<tr>
	<td class="td0">面试评价（总分五分）：</td>
	<td class="td0"><select name="score">
<option>请选择分数</option>
<option value="1" style="color:#f00;font-size:11px"<%If rsRDETAIL("score") = 1 Then response.write(" selected")%>>★</option>
<option value="2" style="color:#f00;font-size:11px"<%If rsRDETAIL("score") = 2 Then response.write(" selected")%>>★★</option>
<option value="3" style="color:#f00;font-size:11px"<%If rsRDETAIL("score") = 3 Then response.write(" selected")%>>★★★</option>
<option value="4" style="color:#f00;font-size:11px"<%If rsRDETAIL("score") = 4 Then response.write(" selected")%>>★★★★</option>
<option value="5" style="color:#f00;font-size:11px"<%If rsRDETAIL("score") = 5 Then response.write(" selected")%>>★★★★★</option>
</select></td>
	<td class=td0><input type="submit" value="评分"></td>
</tr></form>
</table>
</td></tr>
<%End if
%>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><b>基本信息</b></td>
</tr>
<%
if rsRDETAIL("sex") = "female" then
jobs_human_sex_2 = "女"
else
jobs_human_sex_2 = "男"
end if
%>
<tr>
	<td>姓名	</td><td><%=rsRDETAIL("surname")%><%=rsRDETAIL("f_name")%></td>
	<td>生日	</td><td><%=rsRDETAIL("birth")%></td>
	<td>性别</td>
	<td><%=jobs_human_sex_2%></td>
	<td>籍贯</td><td><%=rsRDETAIL("jiguan")%>&nbsp;</td>
</tr>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><b>联系信息</b></td>
</tr>
<tr>
	<td>联系电话</td>
	<td><%=rsRDETAIL("telphone")%>&nbsp;</td>
	<td>手机</td>
	<td><%=rsRDETAIL("mobile")%>&nbsp;</td>
	<td>即时通讯</td>
	<td colspan=3><%=rsRDETAIL("QQ_icq_msn")%>&nbsp;</td>
</tr>
<tr><td>电子信箱</td><td colspan=7><a href="mailto:<%=rsRDETAIL("email")%>"><%=rsRDETAIL("email")%></a></td></tr>
<tr>
	<td>通讯地址</td>
	<td colspan=7><%=rsRDETAIL("address")%>&nbsp;</td>
</tr>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><b>教育背景</b></td>
</tr>
<tr>
	<td>学历</td>
	<td><%=rsRDETAIL("xueli")%></td>
	<td>毕业时间</td>
	<td colspan=5><%=rsRDETAIL("biye_date")%></td>
</tr>
<tr>
	<td>毕业学校</td>
	<td><%=rsRDETAIL("school")%></td>
	<td>专业</td>
	<td><%=rsRDETAIL("zhuangye")%></td>
	<td>毕业学校</td>
	<td><%=rsRDETAIL("school2")%>&nbsp;</td>
	<td>专业二</td>
	<td><%=rsRDETAIL("zhuanye2")%>&nbsp;</td>
</tr>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><b>工作经验</b></td>
</tr>
<tr>
	<td>工作经历</td>
	<td colspan=7><%if trim(rsRDETAIL("jingli")) <> "" then Response.Write(replace(Replace(HTMLEncode(rsRDETAIL("jingli")),vbCrLF,"<br>"),"　","&nbsp;&nbsp;"))
%>&nbsp;</td>
</tr>
<tr>
	<td>自我评价</td>
	<td colspan=7><%if trim(rsRDETAIL("ziwojieshao")) <> "" then Response.Write(replace(Replace(HTMLEncode(rsRDETAIL("ziwojieshao")),vbCrLF,"<br>"),"　","&nbsp;&nbsp;"))
%>&nbsp;</td>
</tr>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><b>其他</b></td>
</tr>
<tr>
	<td>应聘动机</td>
	<td colspan=7><%if trim(rsRDETAIL("what_want")) <> "" then Response.Write(replace(Replace(HTMLEncode(rsRDETAIL("what_want")),vbCrLF,"<br>"),"　","&nbsp;&nbsp;"))
%>&nbsp;</td>
</tr>
<tr>
	<td>未来规划</td><td colspan=7><%if trim(rsRDETAIL("jihua")) <> "" then Response.Write(replace(Replace(HTMLEncode(rsRDETAIL("jihua")),vbCrLF,"<br>"),"　","&nbsp;&nbsp;"))
%>&nbsp;</td>
</tr>
<tr>
	<td>兴趣爱好</td><td colspan=7><%if trim(rsRDETAIL("aihao")) <> "" then Response.Write(replace(Replace(HTMLEncode(rsRDETAIL("aihao")),vbCrLF,"<br>"),"　","&nbsp;&nbsp;"))
%>&nbsp;</td>
</tr>
<%
if trim(rsRDETAIL("this_where")) <> "" then
%>
<tr>
	<td colspan=8>从何处获得本公司招聘信息</td>
</tr>
<tr>
	<td colspan=8><%=rsRDETAIL("this_where")%></td>
</tr>
<%end if%>
<%
if trim(rsRDETAIL("attachment")) <> "" then

	 if trim(rsRDETAIL("attachment_descript")) <> "" then
	 jobs_human_attachment_descript_1 = "<a target=_blank href=""../upload/" & rsRDETAIL("attachment") & """>" & HTMLEncode(rsRDETAIL("attachment_descript")) & "</a>"
	 else
	 jobs_human_attachment_descript_1 = "<a target=_blank href=""../upload/" & rsRDETAIL("attachment") & """>" & "附件" & "</a>"
	 end if
%>
<tr>
	<td bgcolor=#f4f4f4 colspan=8><%=jobs_human_attachment_descript_1%></td>
</tr>
<%end if%>
<%if trim(rsRDETAIL("links")) <>"" then%>
<tr>
	<td>相关链接</td><td colspan=7><%=rsRDETAIL("links")%></td>
</tr>
<%end if%>
<!-- <tr><td colspan=8 style="padding-top:15px;padding-bottom:15px;"><a href="javascript:history.go(-1)">&gt;&gt; 返回</a></td></tr>
 --></table>
 <script language="JavaScript">
<!--
var hs_id = <%=rsRDETAIL("h_id")%>,hs_title="<%=hs_title%>",this_type=<%=rsRDETAIL("this_type")%>;
var http_request = false;
function showrequest(url,element,asyn) {
	var isFFCLS = true;
	http_request = false;
    if (window.XMLHttpRequest) {
        http_request = new XMLHttpRequest();
        if (http_request.overrideMimeType) {
            http_request.overrideMimeType('text/xml');
        }
    } else if (window.ActiveXObject) {
        try {
            http_request = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            try {
            http_request = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {}
        }
    }
    if (!http_request) {alert('出错 :( 不能建立XMLHTTP实例');return false;}
    http_request.onreadystatechange = sub_showrequest;
    http_request.open('GET', url, asyn);
    http_request.send(null);
	if ((!asyn) && (isFFCLS)) sub_showrequest();
	function sub_showrequest(){
	    if (http_request.readyState == 4) {
        if (http_request.status == 200) {
			isFFCLS = false;
			document.getElementById(element).innerHTML = http_request.responseText;
        } else {alert('请求时出错');}
		}	
	}
}

function checkscore(){
if (document.frm_score.score.value=="")
{alert("请选择分数");document.frm_score.score.focus();return false;}
}

function hide_detail(){
var detail = document.getElementById("detail"),detaillbottom = document.getElementById("detaillbottom"),mback = document.getElementById("mback");
	if (detail.style.display == "block"){
		detail.style.display = "none";
		detaillbottom.innerHTML = "→显示简历";
		$("ac").style.width = "100%";
//		mback.innerHTML = "<a href='javascript:history.go(-1)'><span class=hbottom>&gt;&gt; 返回</span></a>"
	}
	else {
		$("ac").style.width = "400px";
		detail.style.display = "block";
		detaillbottom.innerHTML = "←隐藏简历";
//		mback.innerHTML = "";
	}
}

function emailcheck(){
var emailon = document.frm_detail.email_on.checked;
if(emailon){
document.getElementById("email0").style.backgroundColor = "<%=back_menubackcolor%>";
document.getElementById("email1").style.backgroundColor = "<%=back_menubackcolor%>";
document.frm_detail.sub.value="发 信";
	}
else{
document.getElementById("email0").style.backgroundColor = "";
document.getElementById("email1").style.backgroundColor = "";
document.frm_detail.sub.value="确 定";
	}
}

function new_event(){
	var newevent = document.getElementById("newevent"),neweventb = document.getElementById("neweventb");
		if (newevent.style.display == "none")
		{
			var new_event_go = true;
			if (this_type == 1) {new_event_go = confirm("您已经把" + hs_title + "加入到黑名单，您还要继续处理这个应聘者的应聘事务吗？");}
			if (this_type == 11){new_event_go = confirm("您已经退掉了" + hs_title + "，您还要继续处理这个应聘者的应聘事务吗？");}
			if (new_event_go){
			newevent.style.display = "block";
			neweventb.innerHTML = "隐藏新处理";
			}
		}
		else
		{newevent.style.display = "none";
		neweventb.innerHTML = "新处理";
		}
}


function hide_more(){
	var morebottom = document.getElementById("morebottom");
	var divColl = (document.all)?document.all.tags("div"):document.getElementsByTagName("div");
	if (morebottom.innerHTML == "隐藏内容"){
		for(i=0;i<divColl.length;i++){
			whichEl=divColl[i];
			if (whichEl.className == "eventc") whichEl.style.display = "none";
		}
		morebottom.innerHTML = "显示内容";}
	else{
		for(i=0;i<divColl.length;i++){
		whichEl=divColl[i];
		if (whichEl.className == "eventc") whichEl.style.display = "block";}
		morebottom.innerHTML = "隐藏内容";
	}
}

function eventype(){//选择处理类型
var event_name = document.frm_detail.event_name;
if (event_name.value!=""){
	showrequest('jobs_human_detail_ajax.asp?action=eventype&hs_title='+ escape(hs_title) + '&id=' + event_name.value,'newevent',false);
	neweventb.innerHTML = "隐藏新处理";
	newevent.style.display = "block";
	}
}

function validateadd(){
	var n0=document.frm_detail.event_name;
	var n1=document.frm_detail.email_on;
	var alert_str;
	alert_str = "处理事务提交后不能修改与删除，请检查。\n\n主题与内容是否正确？";
	if (n0.value=="")	{alert("请选择流程名称");n0.focus();return false;}
	if (n1.checked){
		var email_subject = document.frm_detail.subject; var email_content = document.frm_detail.event_content;
		alert_str = "将会把主题与内容通过Email发送给" + hs_title + "\n\n请确定主题与内容是否正确？";
		if (email_subject.value==""){alert("请输入主题");email_subject.focus();return false;}
		if (email_content.value==""){alert("请输入内容");email_content.focus();return false;}
	}
	if (confirm(alert_str)){
	document.frm_detail.sub.disabled = true;
	add2();
	}
	return false;
}

function add2(){
var subject = escape(document.frm_detail.subject.value);
var event_name = document.frm_detail.event_name.options[document.frm_detail.event_name.selectedIndex].text;
if (document.frm_detail.event_name_2.value != ""){event_name += ': ' + document.frm_detail.event_name_2.value}
var subject = escape(document.frm_detail.subject.value);
var event_content = escape(document.frm_detail.event_content.value);
this_type = document.frm_detail.event_name.value;
showrequest('jobs_human_detail_ajax.asp?action=add2&category_id=1&hs_id=' + hs_id + '&event_name=' + escape(event_name) + "&event_subject=" + subject + "&event_content=" + event_content + "&event_type_id=" + document.frm_detail.event_name.value + '&send_email=' + document.frm_detail.email_on.checked,'ac',true);
return false;
}

function initIt(){
showrequest('jobs_human_detail_ajax.asp?action=initIt&category_id=1&hs_id=' + hs_id,'ac',false);
}

function initIt2(){
initIt();
if ((this_type==1)||(this_type==2)||(this_type==3)||(this_type==11)) hide_more();
}
onload = initIt2;
//-->
</script>
<div id="ac" style="width:400px;float:left"></div>
<%
rsRDETAIL.close()
set rsRDETAIL = nothing
%>
<!--#include file ="_bottom.asp"-->