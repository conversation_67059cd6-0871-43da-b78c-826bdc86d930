<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="form_config.asp"-->
<%'留言本类别管理
' *** Edit Operations: declare variables

Dim MM_editAction
Dim MM_editAction_0
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction_0 = CStr(Request.ServerVariables("SCRIPT_NAME"))
MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: set variables

If (CStr(Request("MM_insert")) = "addpro") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook_cate"
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "proname|value"
  MM_columnsStr = "name_en|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) = "fadmin" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook_cate"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "admin_name_en|value|email|value"
  MM_columnsStr = "admin_name_en|',none,''|email|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) = "modpro" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook_cate"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "proname|value"
  MM_columnsStr = "name_en|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) = "open" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook_cate"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "this_open|value"
  MM_columnsStr = "this_open|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>

<%
' *** Insert Record: construct a sql insert statement and execute it

Dim MM_tableValues
Dim MM_dbValues

If (CStr(Request("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End If
    MM_tableValues = MM_tableValues & MM_columns(MM_i)
    MM_dbValues = MM_dbValues & MM_formVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(MM_i) & " = " & MM_formVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
' *** Delete Record: declare variables
if request.form("submit2") <>"" or request.form("submit32") <>"" then
if (CStr(Request("MM_delete")) = "form3" And CStr(Request("cateid")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook_cate"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("cateid") + ""
  MM_editRedirectUrl = ""

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If


  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the delete
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

end if
%>
<%
Dim rsPRO
Dim rsPRO_numRows

Set rsPRO = Server.CreateObject("ADODB.Recordset")
rsPRO.ActiveConnection = MM_conn_STRING
rsPRO.Source = "SELECT * FROM guestbook_cate ORDER BY id ASC"
rsPRO.CursorType = 0
rsPRO.CursorLocation = 2
rsPRO.LockType = 1
rsPRO.Open()

rsPRO_numRows = 0
%>
<%
Dim rsMODPROID__MMColParam
rsMODPROID__MMColParam = "1"
If (Request.QueryString("cateid") <> "") Then 
  rsMODPROID__MMColParam = Request.QueryString("cateid")
End If
%>
<%
Dim rsMODPROID
Dim rsMODPROID_numRows

Set rsMODPROID = Server.CreateObject("ADODB.Recordset")
rsMODPROID.ActiveConnection = MM_conn_STRING
rsMODPROID.Source = "SELECT id, name_en,name FROM guestbook_cate WHERE id = " + Replace(rsMODPROID__MMColParam, "'", "''") + ""
rsMODPROID.CursorType = 0
rsMODPROID.CursorLocation = 2
rsMODPROID.LockType = 1
rsMODPROID.Open()

rsMODPROID_numRows = 0
%>
<%
Dim Repeat4__numRows
Dim Repeat4__index

Repeat4__numRows = -1
Repeat4__index = 0
rsPRO_numRows = rsPRO_numRows + Repeat4__numRows
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">在线服务项目设置</h1>
<p class="b_tip"><% if request.querystring("cateid") <>"" then'如果选择了项目	%>
提示：如果没有设置服务项目负责人电子信箱，则网站管理员自动成为该服务项目负责人
<%end if%>
</p>
<SCRIPT language=javascript>

function chkselect3(form3)
{
    if (document.form3.cateid.value == "")
		{
		alert("请选择服务项目");
		document.form3.cateid.focus();
		return false;
		}
return true;
}

function chkmodpro(modpro)
{
    if (document.modpro.proname.value == "")
		{
		alert("请输入内容");
		document.modpro.proname.focus();
		return false;
		}
}
function chkaddpro(addpro)
{
    if (document.addpro.proname.value == "")
		{
		alert("请输入内容");
		document.addpro.proname.focus();
		return false;
		}
}


//-->
</SCRIPT>
 <table border="0" cellspacing="0" cellpadding="5" height=100 align=left <%if request.querystring("cateid") <>"" then 
response.write("width=540")
else
response.write("width=300")
end if'如果选择了项目%>><tr>

<!-- 以下为项目编辑 -->
          <td valign="top" width=300>服务项目<%if request.querystring("cateid")<>""then response.write "：<font color=ff0000>"& HTMLEncode(rsMODPROID.Fields.Item("name").Value) &"</font>"%><br>

<%if request.form("submit31") <> "" then
redi__pro = MM_editAction_0 & "?cateid=" & request.form("cateid")
response.redirect(redi__pro)
else%>
            <form name="form3" method="POST" action="<%=(MM_editAction_0)%>" onsubmit="return chkselect3(form3)">
              
<%end if%>
              <% If Not rsPRO.EOF Or Not rsPRO.BOF Then %>
              <select name="cateid" size="8">
                <% 
While ((Repeat4__numRows <> 0) AND (NOT rsPRO.EOF)) 
%>
                <option value="<%=(rsPRO.Fields.Item("id").Value)%>"><%=(rsPRO.Fields.Item("name").Value)%></option>
                <% 
  Repeat4__index=Repeat4__index+1
  Repeat4__numRows=Repeat4__numRows-1
  rsPRO.MoveNext()
Wend
%>
              </select>
              <input type="submit" name="Submit31" value="选定这个项目"><%if form_adddel = 1 then'如果不能增加删除项目%>
              <input name="Submit32" type="submit" onClick="GP_popupConfirmMsg('删除项目将删除该项目的服务记录及该项目的设置，确认删除？');return document.MM_returnValue" value="删除">
              <input type="hidden" name="MM_delete" value="form3"><%end if''如果不能增加删除项目%>
			  <%else%>
			  <h3>尚无在线服务项目</h3>
              <% End If ' end Not rsPRO.EOF Or NOT rsPRO.BOF %>
           </form>
		   <%if form_adddel = 1 then'如果不能增加删除项目%> 
           <form name="addpro" action="<%=(MM_editAction_0)%>" method="POST" onsubmit="return chkaddpro(addpro)">
              <input name="proname" type="text" size="20"><input type="submit" value="增加服务项目">
              <input type="hidden" name="MM_insert" value="addpro">
            </form><%end if''如果不能增加删除项目%>

<%if request.querystring("cateid")<>"" AND form_adddel = 1 then'如果没有选择项目，则不显示修改%>
<form action="<%=MM_editAction%>" name="modpro" method="POST" onsubmit="return chkmodpro(modpro)">
              <input name="proname" type="text" size="20" value="<%=(rsMODPROID.Fields.Item("name_en").Value)%>"><input type="submit" value="修改项目名称">
              <input type="hidden" name="MM_update" value="modpro">
              <input type="hidden" name="MM_recordId" value="<%= rsMODPROID.Fields.Item("id").Value %>">
            </form><%end if'如果没有选择项目，则不显示修改%>
          </td>
<% if request.querystring("cateid") <>"" then'如果选择了项目	%>
<%
Dim rsGUESTADMIN__MMColParam
rsGUESTADMIN__MMColParam = "1"
If (Request.QueryString("cateid") <> "") Then 
  rsGUESTADMIN__MMColParam = Request.QueryString("cateid")
End If
%>
<%
Dim rsGUESTADMIN
Dim rsGUESTADMIN_numRows

Set rsGUESTADMIN = Server.CreateObject("ADODB.Recordset")
rsGUESTADMIN.ActiveConnection = MM_conn_STRING
rsGUESTADMIN.Source = "SELECT * FROM guestbook_cate WHERE id = " + Replace(rsGUESTADMIN__MMColParam, "'", "''") + ""
rsGUESTADMIN.CursorType = 0
rsGUESTADMIN.CursorLocation = 2
rsGUESTADMIN.LockType = 1
rsGUESTADMIN.Open()

rsGUESTADMIN_numRows = 0
%>
  <td align=left valign=top nowrap>本服务项目负责人设置：<br>
<form METHOD="POST" name=fadmin action="<%=MM_editAction%>">
  <input type="hidden" name="admin_name_en" type="text" value="<%=(rsGUESTADMIN.Fields.Item("admin_name_en").Value)%>" size=20>
  <font class=p12>负责人信箱</font><br>
  <input name="email" type="text" value="<%=(rsGUESTADMIN.Fields.Item("email").Value)%>" size=20>
  <br>
  <font class=p12>负责人信箱必须使用本站的已经设置好的信箱<br>（即以<%=(back_site)%>为后缀的信箱）</font><br>
  <input type="submit" value="确定修改">
  <input type="hidden" name="MM_update" value="fadmin">
  <input type="hidden" name="MM_recordId" value="<%= rsGUESTADMIN.Fields.Item("id").Value %>">
</form>
</td>
<%
rsGUESTADMIN.Close()
Set rsGUESTADMIN = Nothing
%>
<%end if'如果选择了项目%>
</tr>
</table>
<!--#include file ="_bottom.asp"-->  
<%
rsPRO.Close()
Set rsPRO = Nothing
%>
<%
rsMODPROID.Close()
Set rsMODPROID = Nothing
%>