# 华隆重工官方网站

## 项目结构

```
├── src/                    # 网站源文件
│   ├── css/
│   │   ├── input.css      # Tailwind CSS 输入文件
│   │   └── style.css      # 自定义样式（已弃用）
│   ├── images/            # 图片资源
│   ├── js/                # JavaScript 文件
│   └── *.html             # HTML 页面
├── dist/                  # 构建输出目录
├── vite.config.js         # Vite 构建配置
├── postcss.config.js      # PostCSS 配置
├── tailwind.config.js     # Tailwind CSS 配置
├── package.json           # 项目依赖
├── build.bat              # 生产构建脚本
├── dev.bat                # 开发服务器脚本
└── preview.bat            # 预览生产构建脚本
```

## 开发指南

### 首次设置

1. 确保已安装 Node.js 和 Yarn
2. 运行 `yarn install` 安装依赖

### 开发模式

运行 `dev.bat` 或 `yarn dev` 启动 Vite 开发服务器：

- 支持热重载（HMR）
- 实时预览
- 自动打开浏览器
- 默认端口：http://localhost:3000

### 生产构建

运行 `build.bat` 或 `yarn build` 生成优化的生产版本：

- 输出到 `dist/` 目录
- CSS/JS 压缩和优化
- 资源文件处理

### Vite 构建特性

- **快速启动**：使用原生 ES 模块，启动速度极快
- **热重载**：保存文件后立即在浏览器中看到更改
- **优化构建**：生产构建时自动优化和压缩资源
- **多页面支持**：自动检测并构建所有 HTML 页面

### Tailwind CSS 配置

- 自定义颜色已在 `tailwind.config.js` 中定义
- 主要颜色：
  - `primary`: #1e3a8a (蓝色)
  - `secondary`: #6b7280 (灰色)
  - `accent`: #f97316 (橙色)
  - `light-gray`: #f3f4f6
  - `dark-gray`: #111827

### 样式修改

1. 编辑 `src/css/input.css` 文件
2. 开发模式下会自动重新编译
3. 浏览器会自动刷新显示更改

### 预览生产构建

运行 `preview.bat` 或 `yarn preview` 可以本地预览生产构建的结果：

- 默认端口：http://localhost:4173
- 需要先运行 `yarn build` 生成构建文件

### 注意事项

- 开发时使用 `yarn dev`，Vite 会自动处理 CSS 编译
- 所有自定义样式应添加到 `src/css/input.css` 中
- 生产构建会自动优化和压缩所有资源
- 构建输出在 `dist/` 目录，可直接部署到服务器
