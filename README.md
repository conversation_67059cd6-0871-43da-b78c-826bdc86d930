# 华隆重工官方网站

## 项目结构

```
├── src/                    # 网站源文件
│   ├── css/
│   │   ├── input.css      # Tailwind CSS 输入文件
│   │   ├── tailwind.css   # 生成的 Tailwind CSS 文件
│   │   └── style.css      # 自定义样式（已弃用）
│   ├── images/            # 图片资源
│   ├── js/                # JavaScript 文件
│   └── *.html             # HTML 页面
├── tailwind.config.js     # Tailwind CSS 配置
├── package.json           # 项目依赖
├── build.bat              # 生产构建脚本
└── dev.bat                # 开发模式脚本
```

## 开发指南

### 首次设置

1. 确保已安装 Node.js
2. 运行 `npm install` 安装依赖

### 开发模式

运行 `dev.bat` 或 `npm run build-css` 启动开发模式，会监听文件变化并自动重新构建 CSS。

### 生产构建

运行 `build.bat` 或 `npm run build-css-prod` 生成压缩的生产版本 CSS。

### Tailwind CSS 配置

- 自定义颜色已在 `tailwind.config.js` 中定义
- 主要颜色：
  - `primary`: #1e3a8a (蓝色)
  - `secondary`: #6b7280 (灰色)
  - `accent`: #f97316 (橙色)
  - `light-gray`: #f3f4f6
  - `dark-gray`: #111827

### 样式修改

1. 编辑 `src/css/input.css` 文件
2. 运行构建命令重新生成 CSS
3. 刷新浏览器查看效果

### 注意事项

- 不要直接编辑 `src/css/tailwind.css`，该文件是自动生成的
- 所有自定义样式应添加到 `src/css/input.css` 中
- 使用生产构建可以显著减小 CSS 文件大小
