<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="../_md5.asp"-->
<!--#include file ="validate.asp"-->
<%
'用到的session
'得到用户名、权限值
'session("ismember")=false
'ssession("isadmin")=false
'session("name") = ""
'session("flag") = ""
'session("current_lesson") = ""

'以下为验证码判断
if Request.form("id")<> "" then
if Request.form("validate") <> "" then
	If not IsNumeric(Request.Form("validate")) Then
         Response.Write "<script>alert('验证码错误，应该是四位数字，请重新输入');window.location.href('default.asp');</script>"
         Response.End
	elseif  cint(Session("validate")) <> cint(Request.Form("validate")) Then
		Response.Write "<script>alert('验证码输入错误');window.location.href('default.asp');</script>"
		response.end
	end if
else
	Response.Write "<script>alert('未输入验证码，请输入右边图片中的四位数字');window.location.href('default.asp');</script>"
    Response.End
end if
end if
'验证码判断结束

' *** Validate request to log in to this site.
MM_LoginAction = Request.ServerVariables("URL")
MM_redirectLoginSuccess="page.asp"
MM_redirectLoginFailed="default.asp"
If Request.QueryString<>"" Then MM_LoginAction = MM_LoginAction + "?" + Request.QueryString
if INStr(CStr(Request.Form("id")),"'") <> 0 then Response.Redirect(MM_redirectLoginFailed)
if INStr(CStr(Request.Form("pw")),"'") <> 0 then Response.Redirect(MM_redirectLoginFailed)
if session("isadmin") = True Or session("ismember") = True Then
	Response.Redirect(MM_redirectLoginSuccess)
	response.End
End if

MM_valUsername=CStr(Request("id"))
If MM_valUsername <> "" Then
  Dim MM_rsUser
  set MM_rsUser =  Server.CreateObject("ADODB.Recordset")
  sql = "SELECT id, pw,admin.name,flag,this_lock,this_super,admin.group_id FROM admin inner join usergroup on usergroup.group_id=admin.group_id WHERE id='" & MM_valUsername &"' AND pw='" & md5(CStr(Request("pw"))) & "'"  
  MM_rsUser.Open sql,MM_conn_STRING,1,1
  If Not MM_rsUser.EOF Or Not MM_rsUser.BOF Then 
		If MM_rsUser("group_id") =1 then
			session("isadmin")=True
			session("ismember")=False
			session("admin_id") = MM_rsUser("id")
			session("name") = MM_rsUser("name")
			session("flag") = MM_rsUser("flag")
		Else
			If MM_rsUser("this_lock") = True Then
				response.redirect("intra_failure.asp?info=" & server.urlencode("您的账号因故被管理员停用，请与本厂网管联系。"))
				session("ismember") = false
				session("isadmin") = False
				session("admin_id") = ""
				session("name") = ""
				session("flag") = ""
				response.end
			else
				session("ismember") = True
				session("isadmin") = False
				session("name") = MM_rsUser("name")
				session("flag") = MM_rsUser("flag")
				session("admin_id") = MM_rsUser("id")
			End if
		End if
		MM_rsUser.Close
		Set MM_rsUser = Nothing
		Response.Redirect(MM_redirectLoginSuccess)
  else
	  MM_rsUser.Close
	  Set MM_rsUser = Nothing
	  Response.Redirect(MM_redirectLoginFailed)
  End if
End If
%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<title><%=(back_coname)%> - 门户系统管理登录</title>
<style type="text/css">
/* <![CDATA[ */
*{padding:0;margin:0;font-size:12px;line-height:120%}
body{text-align:center;vertical-align:middle}
div{width:600px;height:300px;;background:url(../images/adminbg.jpg) no-repeat left top;text-align:left;margin-left:auto;margin-right:auto;position:relative;top:50%;border:1px solid #ccc}
div p{height:22px}
.i300 {width:300px}
form{width:320px;height:124px;padding-left:200px;padding-top:140px;}
label{display:block;float:left;font-size:14px;width:50px;text-align:right;line-height:135%}
label,#validate{margin-right:15px;color:#0e3c69}
#validate{color:#000}
#id{}
#id input,#pw input{width:120px}
#Submit{width:70px;border:1px solid #fff;margin-left:65px;margin-top:10px;background-color: #0e3c69;color:#fff}
a.fav{display:block;position:absolute;right:25px;bottom:25px;color:#0e3c69;text-decoration: none;}
a.fav:hover{color:#333}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
if (navigator.userAgent.indexOf('Firefox') > - 1) {document.write("<style type=\"text/css\">div{margin-top:150px}</style>");}
window.onload = function(){obj = document.getElementById("form1");obj.setAttribute("autocomplete","off");obj.id.focus();}
function check(){if (obj.id.value==""){ alert("请输入账号"); obj.id.focus(); return false;}
if (obj.pw.value==""){ alert("请输入密码"); obj.pw.focus(); return false;}
if (obj.validate.value==""){alert("请输入验证码");obj.validate.focus(); return false;}
}
//]]>
</script>
</head>
<body>
<div>
<a href="javascript:window.external.AddFavorite('http://www.<%=back_site%>/admin/','<%=back_coname%>门户网站管理')" class="fav">收藏</a>
<form id="form1" method="post" action="<%=MM_LoginAction%>">
<p id="id"><label>账　号</label><input size="15" type="text" name="id" /></p>
<p id="pw"><label>密　码</label><input type="password" name="pw" /></p>
<p><label>验证码</label><input type="text" id="validate" size="6" maxlength="4" name="validate" /><%Call validate("ffffff",20)%></p>
<p><input type="submit" id="Submit" value="确 认" onclick="return check()" /></p>
</form>
</div>
</body>
</html>