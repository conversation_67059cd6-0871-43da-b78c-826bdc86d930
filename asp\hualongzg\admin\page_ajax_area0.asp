<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
Dim list_id,list_sort,list_p
list_id = request.querystring("id")
list_sort = request.querystring("sort")
list_p = request.querystring("p")
page_style_id = request.querystring("page_style_id")
pro_name_str = request.querystring("pro")
page_name_str = request.querystring("page")
set rsSELPAGE = Server.CreateObject("ADODB.Recordset")
if list_sort = "1" then
	sql = "SELECT page.pageid, page.title,page.this_not_del,page.isscreen,list.style_id,imgsmall,imgsmallwidth,imgsmallheight FROM page inner join list on page.subid=list.id WHERE page.subid = " & list_id & " AND page_style_id=" & page_style_id & " ORDER BY page.sort_id asc,pageid ASC"
else
	sql = "SELECT page.pageid, page.title,page.this_not_del,page.isscreen,list.style_id,imgsmall,imgsmallwidth,imgsmallheight FROM page inner join list on page.subid=list.id WHERE page.subid = " & list_id & " AND page_style_id=" & page_style_id & " ORDER BY page.sort_id desc,pageid DESC"
end If
rsSELPAGE.open sql,MM_conn_String,1,1
If Not rsSELPAGE.eof then
	rsSELPAGE.pagesize = list_num
	If list_p = "" then
		if list_sort = "1" then
			rsSELPAGE.absolutepage = rsSELPAGE.pagecount
		Else
			rsSELPAGE.absolutepage = 1
		End If
	Else
		If CInt(list_p) > rsSELPAGE.pagecount Then 
			rsSELPAGE.absolutepage = rsSELPAGE.pagecount
		ElseIf CInt(list_p) < 1 Then 
			rsSELPAGE.absolutepage = 1
		else
			rsSELPAGE.absolutepage = CInt(list_p)
		End if
	End if
	cupage = rsSELPAGE.absolutepage

	response.write "<div class=""navnum"" id=""navnum""><a href=""javascript:void(null)"" onclick=""addPage(" & list_id & ")"" class=""add"">增加</a>"
	If rsSELPAGE.pagecount > 1 Then
		response.write page_list(rsSELPAGE.pagecount,cupage,7,"cn","gotoPage(#page#)", "javascript:void(null)")
	End if
	response.write  "</div>"

	Dim this_list_style_id
	If rsSELPAGE("style_id") = 1 Or page_style_id = 1 Then 
		this_list_style_id = 1
	ElseIf rsSELPAGE("style_id") = 4 Or page_style_id = 4 Then
		this_list_style_id = 4		
	Else
		this_list_style_id = 0
	End if
	
	%>
	<p class="prompt">点击标题查看或修改页面</p>
	<ul class="ulmain list<%=this_list_style_id%>"><%
	for i = 1 to list_num 
	If rsSELPAGE("isscreen") = True Then 	
		isscreenclass = " class=""iscreen"""
		isscreenstr = "显示"
	Else
		isscreenclass = ""
		isscreenstr = "屏蔽"
	End if
	%>
			<li id="p__<%=rsSELPAGE("pageid")%>"><div<%=isscreenclass%>><a href="javascript:void(null)" class="view" onclick="view(<%=rsSELPAGE("pageid")%>)"><%=rsSELPAGE("title")%></a>
			<%
			If this_list_style_id = 1 Then%>
			<div class="imgsmall"><img src="../upload/<%=rsSELPAGE("imgsmall")%>" width="<%=rsSELPAGE("imgsmallwidth")%>" height="<%=rsSELPAGE("imgsmallheight")%>" alt="" /></div>
			<%End IF
			%>
			<div class="atobutton"><a href="javascript:void(null)" class="del" onclick="del(<%=rsSELPAGE("pageid")%>)"><span>删除</span></a><a href="javascript:void(null)" class="screen" onclick="screen(<%=rsSELPAGE("pageid")%>)"><%=isscreenstr%></a><a href="javascript:void(null)" onmouseover="sort(<%=rsSELPAGE("pageid")%>)" onmouseout="sorthide(<%=rsSELPAGE("pageid")%>)" class="sort">排序</a><div class="sortmenu" id="sortmenu<%=rsSELPAGE("pageid")%>"><a href="javascript:void(null)" onclick="sortmenu(<%=rsSELPAGE("pageid")%>,'top')">顶部↑↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsSELPAGE("pageid")%>,'up')">向上↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsSELPAGE("pageid")%>,'down')">向下↓</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsSELPAGE("pageid")%>,'bottom')">底部↓↓</a></div></div></div></li><%
		rsSELPAGE.MoveNext()
		if rsSELPAGE.eof then exit for
	next
			%>
	</ul>
	<%
Else response.write "<div class=""p14"" style=""padding-bottom:8px"">尚无内容</div><div class=""navnum""><a href=""javascript:void(null)"" onclick=""addPage(" & list_id & ")"" class=""add"">增加</a></div>"
End If ' end Not rsSELPAGE.EOF Or NOT rsSELPAGE.BOF 
rsSELPAGE.close()
Set rsSELPAGE = nothing
%>