<%@LANGUAGE="VBSCRIPT" codepage="65001"%>

<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel<2 then Response.Redirect "stat_help.asp?id=004&error=您没有查看月统计的权限。"
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>月/年统计</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指图形柱或者图形柱下的数字可以看到对应的访问量</font>
  </td></tr>
</table>
<br>
<br>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  <tr height="30">
    <td class=td0 width="1"></td>
    <td class=td0 width="100%"><%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
%>

<table class=table0 border="0" cellpadding="0" cellspacing="0" width="310" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="101">
<%
Set rs = Server.CreateObject("ADODB.Recordset")
'计算12个月（零头也算一个月）前的时间
datetwelve=dateadd("m",-11,date())
datetwelve=cdate(year(datetwelve) & "-" & month(datetwelve) & "-1")

sql="select vmonth,count(id) as allmonth from view where vtime>=datevalue('" & _
	datetwelve & "') group by vmonth"
rs.Open sql,conn,1,1

dim vallmonth(12)
maxallmonth=0
sumallmonth=0
do while not rs.EOF
	vallmonth(clng(rs("vmonth"))-1)=clng(rs("allmonth"))
	if vallmonth(clng(rs("vmonth"))-1)>maxallmonth then maxallmonth=vallmonth(clng(rs("vmonth"))-1)
	sumallmonth=sumallmonth+vallmonth(clng(rs("vmonth"))-1)
	rs.MoveNext
loop
	'防止除数为零而出错
	if maxallmonth=0 then maxallmonth=1
	if sumallmonth=0 then sumallmonth=1

%>
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallmonth*10+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxallmonth*10/4+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallmonth*10/2+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxallmonth*10/4+0.5)/10%><br></font></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 11
themonth=month(now())+i
if themonth>11 then themonth=themonth-12

%>
<td class=td0 width=20 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	height="<%=(vallmonth(themonth)/maxallmonth)*100%>" width="9"
	alt="<%=themonth+1%>月，访问<%=vallmonth(themonth)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallmonth(themonth)*1000/sumallmonth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>
<tr>
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></td>
<td class=td0 width=10></td>
<%
for i= 0 to 11
themonth=month(now())+i
if themonth>11 then themonth=themonth-12
%>
<td class=td0 width=20 align=center><a title="<%=themonth+1%>月，访问<%=vallmonth(themonth)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallmonth(themonth)*1000/sumallmonth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%">
	<font class=p12 style="letter-spacing: -1"><%=themonth+1%></font></a></td>
<%
next
%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5"><td class=td0 colspan=29></td></tr>
<tr height="5"><td class=td0 colspan=29 align=center><font class=p14><u>最近12个月访问量</u></font></td></tr>
</table>

	</td>
    <td class=td0 width="1"></td>
  </tr>

</table>
<%
rs.Close
%>


<br>



<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  
  <tr height="30">
    <td class=td0 width="1"></td>
    <td class=td0 width="100%"><table class=table0 border="0" cellpadding="0" cellspacing="0" width="310" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="101">
<%

sql="select vmonth,count(id) as allmonth from view group by vmonth"
rs.Open sql,conn,1,1

for i=0 to 11
	vallmonth(i)=0
next
maxallmonth=0
sumallmonth=0
do while not rs.EOF
	vallmonth(clng(rs("vmonth"))-1)=clng(rs("allmonth"))
	if vallmonth(clng(rs("vmonth"))-1)>maxallmonth then maxallmonth=vallmonth(clng(rs("vmonth"))-1)
	sumallmonth=sumallmonth+vallmonth(clng(rs("vmonth"))-1)
	rs.MoveNext
loop
	'防止除数为零而出错
	if maxallmonth=0 then maxallmonth=1
	if sumallmonth=0 then sumallmonth=1

%>
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallmonth*10+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxallmonth*10/4+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallmonth*10/2+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxallmonth*10/4+0.5)/10%><br></font></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 11
themonth=i

%>
<td class=td0 width=20 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	height="<%=(vallmonth(themonth)/maxallmonth)*100%>" width="9"
	alt="<%=themonth+1%>月，访问<%=vallmonth(themonth)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallmonth(themonth)*1000/sumallmonth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>
<tr>
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></td>
<td class=td0 width=10></td>
<%
for i= 0 to 11
themonth=i
%>
<td class=td0 width=20 align=center><a title="<%=themonth+1%>月，访问<%=vallmonth(themonth)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallmonth(themonth)*1000/sumallmonth)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%">
	<font class=p12 style="letter-spacing: -1"><%=themonth+1%></font></a></td>
<%
next
%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5">&nbsp;<td class=td0 colspan=29></td></tr>
<tr height="5"><td class=td0 colspan=29 align=center><font class=p14><u>所有12个月访问量</u></font></td></tr>
</table>

	</td>
    <td class=td0 width="1"></td>
  </tr>
</table>
<%
rs.Close
%>

<br>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  <tr height="30">
    <td class=td0 width="1"></td>
    <td class=td0 width="100%">
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="270" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="10">
	<td class=td0 width="40"></td><td class=td0 width="230"><img src="stat_images/chart_head.gif"></td>
</tr>
<%
sql="select vyear,count(id) as allyear from view group by vyear order by vyear DESC"
rs.Open sql,conn,1,1

maxallyear=0
sumallyear=0
do while not rs.EOF
	if rs("allyear")> maxallyear then maxallyear=rs("allyear")
	sumallyear=sumallyear+rs("allyear")
	rs.MoveNext
loop
	'防止除数为零而出错
	if maxallyear=0 then maxallyear=1
	if sumallyear=0 then sumallyear=1

rs.MoveFirst
do while not rs.EOF
theyear=rs("vyear")
vallyear=rs("allyear")
%>
<tr>
<td class=td0 width="40" align=right><a title="<%=theyear%>年，访问<%=vallyear%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallyear*1000/sumallyear)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12><%=theyear%></font></a>&nbsp;</td>
<td class=td0 width="230" background="stat_images/chart_b2.gif" align=left>
<img style="BORDER-left: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	width="<%=(vallyear/maxallyear)*183%>" height="9"
	alt="<%=theyear%>年，访问<%=vallyear%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallyear*1000/sumallyear)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12> <%=vallyear%></font></td>
</tr>
<%
rs.MoveNext
loop
%>
<tr height="10">
	<td class=td0 width="40"></td><td class=td0 width="230"><img src="stat_images/chart_bottom.gif"></td>
</tr>
<tr height="5"><td class=td0 colspan=29 align=center><font class=p14><u>年访问量统计</u></font></td></tr>
</table>
	</td>
    <td class=td0 width="1"></td>
  </tr>
</table>
<%
rs.Close

set rs=nothing
conn.Close 
set conn=nothing
%>
<br>
<!--#include file="stat__bottom.asp"-->
</body>
</html>
