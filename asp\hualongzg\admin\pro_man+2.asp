<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="pro_config.asp" -->
<%


'*****************************************************************************************

'*** File Upload to: ../img, Extensions: "GIF,JPG,JPEG", Form: form2, Redirect: "", "file", "500000", "uniq"
'*** Pure ASP File Upload Modify Version by xPilot-----------------------------------------------------
' Copyright 2000 (c) <PERSON>
'
' Script partially based on code from <PERSON> 
'              (http://www.asptoday.com/articles/20000316.htm)
'
' New features from GP:
'  * Fast file save with ADO 2.5 stream object
'  * new wrapper functions, extra error checking
'  * UltraDev Server Behavior extension
'
' Copyright 2001-2002 (c) Modify by xPilot
' *** Date: 12/15/2001 ***
' *** 支持所有双字节文件名，而且修复了原函数中遇到空格也会自动截断文件名的错误！ ***
' *** 保证百分百以原文件名保存上传文件！***
' *** Welcome to visite pilothome.yeah.net <NAME_EMAIL> to me！***
'
' Version: 2.0.1 Beta for GB2312,BIG5,Japan,Korea ...
'------------------------------------------------------------------------------
Sub BuildUploadRequest(RequestBin,UploadDirectory,storeType,sizeLimit,nameConflict)
  'Get the boundary
  PosBeg = 1
  PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
  if PosEnd = 0 then
    Response.Write "<b>Form was submitted with no ENCTYPE=""multipart/form-data""</b><br>"
    Response.Write "Please correct the form attributes and try again."
    Response.End
  end if
  'Check ADO Version
	set checkADOConn = Server.CreateObject("ADODB.Connection")
	adoVersion = CSng(checkADOConn.Version)
	set checkADOConn = Nothing
	if adoVersion < 2.5 then
    Response.Write "<b>You don't have ADO 2.5 installed on the server.</b><br>"
    Response.Write "The File Upload extension needs ADO 2.5 or greater to run properly.<br>"
    Response.Write "You can download the latest MDAC (ADO is included) from <a href=""www.microsoft.com/data"">www.microsoft.com/data</a><br>"
    Response.End
	end if		
  'Check content length if needed
	Length = CLng(Request.ServerVariables("HTTP_Content_Length")) 'Get Content-Length header
	If "" & sizeLimit <> "" Then
    sizeLimit = CLng(sizeLimit)
    If Length > sizeLimit Then
      Request.BinaryRead (Length)
      Response.Write "Upload size " & FormatNumber(Length, 0) & "B exceeds limit of " & FormatNumber(sizeLimit, 0) & "B"
      Response.End
    End If
  End If
  boundary = MidB(RequestBin,PosBeg,PosEnd-PosBeg)
  boundaryPos = InstrB(1,RequestBin,boundary)
  'Get all data inside the boundaries
  Do until (boundaryPos=InstrB(RequestBin,boundary & getByteString("--")))
    'Members variable of objects are put in a dictionary object
    Dim UploadControl
    Set UploadControl = CreateObject("Scripting.Dictionary")
    'Get an object name
    Pos = InstrB(BoundaryPos,RequestBin,getByteString("Content-Disposition"))
    Pos = InstrB(Pos,RequestBin,getByteString("name="))
    PosBeg = Pos+6
    PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(34)))
    Name = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
    PosFile = InstrB(BoundaryPos,RequestBin,getByteString("filename="))
    PosBound = InstrB(PosEnd,RequestBin,boundary)
    'Test if object is of file type
    If  PosFile<>0 AND (PosFile<PosBound) Then
      'Get Filename, content-type and content of file
      PosBeg = PosFile + 10
      PosEnd =  InstrB(PosBeg,RequestBin,getByteString(chr(34)))
      FileName = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      FileName = Mid(FileName,InStrRev(FileName,"\")+1)
      'Add filename to dictionary object
      UploadControl.Add "FileName", FileName
      Pos = InstrB(PosEnd,RequestBin,getByteString("Content-Type:"))
      PosBeg = Pos+14
      PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
      'Add content-type to dictionary object
      ContentType = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      UploadControl.Add "ContentType",ContentType
      'Get content of object
      PosBeg = PosEnd+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = FileName
      ValueBeg = PosBeg-1
      ValueLen = PosEnd-Posbeg
    Else
      'Get content of object
      Pos = InstrB(Pos,RequestBin,getByteString(chr(13)))
      PosBeg = Pos+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      ValueBeg = 0
      ValueEnd = 0
    End If
    'Add content to dictionary object
    UploadControl.Add "Value" , Value	
    UploadControl.Add "ValueBeg" , ValueBeg
    UploadControl.Add "ValueLen" , ValueLen	
    'Add dictionary object to main dictionary
    UploadRequest.Add name, UploadControl	
    'Loop to next object
    BoundaryPos=InstrB(BoundaryPos+LenB(boundary),RequestBin,boundary)
  Loop

  GP_keys = UploadRequest.Keys
  for GP_i = 0 to UploadRequest.Count - 1
    GP_curKey = GP_keys(GP_i)
    'Save all uploaded files
    if UploadRequest.Item(GP_curKey).Item("FileName") <> "" then
      GP_value = UploadRequest.Item(GP_curKey).Item("Value")
      GP_valueBeg = UploadRequest.Item(GP_curKey).Item("ValueBeg")
      GP_valueLen = UploadRequest.Item(GP_curKey).Item("ValueLen")

      if GP_valueLen = 0 then
        Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
        Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
        Response.Write "File does not exists or is empty.<br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
	  	  response.End
	    end if
      
      'Create a Stream instance
      Dim GP_strm1, GP_strm2
      Set GP_strm1 = Server.CreateObject("ADODB.Stream")
      Set GP_strm2 = Server.CreateObject("ADODB.Stream")
      
      'Open the stream
      GP_strm1.Open
      GP_strm1.Type = 1 'Binary
      GP_strm2.Open
      GP_strm2.Type = 1 'Binary
        
      GP_strm1.Write RequestBin
      GP_strm1.Position = GP_ValueBeg
      GP_strm1.CopyTo GP_strm2,GP_ValueLen
    
      'Create and Write to a File
      GP_curPath = Request.ServerVariables("PATH_INFO")
      GP_curPath = Trim(Mid(GP_curPath,1,InStrRev(GP_curPath,"/")) & UploadDirectory)
      if Mid(GP_curPath,Len(GP_curPath),1)  <> "/" then
        GP_curPath = GP_curPath & "/"
      end if 
      GP_CurFileName = UploadRequest.Item(GP_curKey).Item("FileName")
      GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & GP_CurFileName
      'Check if the file alreadu exist
      GP_FileExist = false
      Set fso = CreateObject("Scripting.FileSystemObject")
      If (fso.FileExists(GP_FullFileName)) Then
        GP_FileExist = true
      End If      
      if nameConflict = "error" and GP_FileExist then
        Response.Write "<B>File already exists!</B><br><br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
				GP_strm1.Close
				GP_strm2.Close
	  	  response.End
      end if
      if ((nameConflict = "over" or nameConflict = "uniq") and GP_FileExist) or (NOT GP_FileExist) then
        if nameConflict = "uniq" and GP_FileExist then
          Begin_Name_Num = 0
          while GP_FileExist    
            Begin_Name_Num = Begin_Name_Num + 1
            GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
            GP_FileExist = fso.FileExists(GP_FullFileName)
          wend  
          UploadRequest.Item(GP_curKey).Item("FileName") = fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
					UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
        end if
        on error resume next
        GP_strm2.SaveToFile GP_FullFileName,2
        if err then
          Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
          Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
          Response.Write "Maybe the destination directory does not exist, or you don't have write permission.<br>"
          Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
    		  err.clear
  				GP_strm1.Close
  				GP_strm2.Close
  	  	  response.End
  	    end if
  			GP_strm1.Close
  			GP_strm2.Close
  			if storeType = "path" then
  				UploadRequest.Item(GP_curKey).Item("Value") = GP_curPath & UploadRequest.Item(GP_curKey).Item("Value")
  			end if
        on error goto 0
      end if
    end if
  next

End Sub

'把普通字符串转成二进制字符串函数
Function getByteString(StringStr)
    getByteString=""
  For i = 1 To Len(StringStr) 
    XP_varchar = mid(StringStr,i,1)
    XP_varasc = Asc(XP_varchar) 
    If XP_varasc < 0 Then 
       XP_varasc = XP_varasc + 65535 
    End If 

    If XP_varasc > 255 Then 
       XP_varlow = Left(Hex(Asc(XP_varchar)),2) 
       XP_varhigh = right(Hex(Asc(XP_varchar)),2) 
       getByteString = getByteString & chrB("&H" & XP_varlow) & chrB("&H" & XP_varhigh) 
    Else 
       getByteString = getByteString & chrB(AscB(XP_varchar)) 
    End If 
  Next 
End Function

'把二进制字符串转换成普通字符串函数 
Function getString(StringBin)
   getString =""
   Dim XP_varlen,XP_vargetstr,XP_string,XP_skip
   XP_skip = 0 
   XP_string = "" 
 If Not IsNull(StringBin) Then 
      XP_varlen = LenB(StringBin) 
    For i = 1 To XP_varlen 
      If XP_skip = 0 Then
         XP_vargetstr = MidB(StringBin,i,1) 
         If AscB(XP_vargetstr) > 127 Then 
           XP_string = XP_string & Chr(AscW(MidB(StringBin,i+1,1) & XP_vargetstr)) 
           XP_skip = 1 
         Else 
           XP_string = XP_string & Chr(AscB(XP_vargetstr)) 
         End If 
      Else 
      XP_skip = 0
   End If 
    Next 
 End If 
      getString = XP_string 
End Function 

Function UploadFormRequest(name)
  on error resume next
  if UploadRequest.Item(name) then
    UploadFormRequest = UploadRequest.Item(name).Item("Value")
  end if  
End Function

'Process the upload
UploadQueryString = Replace(Request.QueryString,"GP_upload=true","")
if mid(UploadQueryString,1,1) = "&" then
	UploadQueryString = Mid(UploadQueryString,2)
end if

GP_uploadAction = CStr(Request.ServerVariables("URL")) & "?GP_upload=true"
If (Request.QueryString <> "") Then  
  if UploadQueryString <> "" then
	  GP_uploadAction = GP_uploadAction & "&" & UploadQueryString
  end if 
End If

If (CStr(Request.QueryString("GP_upload")) <> "") Then
  GP_redirectPage = ""
  If (GP_redirectPage = "") Then
    GP_redirectPage = CStr(Request.ServerVariables("URL"))
  end if
    
  RequestBin = Request.BinaryRead(Request.TotalBytes)
  Dim UploadRequest
  Set UploadRequest = CreateObject("Scripting.Dictionary")  
  BuildUploadRequest RequestBin, "../img", "file", "500000", "uniq"

 '*** GP NO REDIRECT
end if  
if UploadQueryString <> "" then
  UploadQueryString = UploadQueryString & "&GP_upload=true"
else  
  UploadQueryString = "GP_upload=true"
end if  


%>
<%
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction2 = CStr(Request.ServerVariables("URL")) 'MM_editAction2 = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction2 = MM_editAction2 & "?" & UploadQueryString
End If
MM_editAction22 = CStr(Request.ServerVariables("URL")) 'MM_editAction22 = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction22 = MM_editAction22 & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: (Modified for File Upload) set variables

If (CStr(UploadFormRequest("MM_update0")) <> "" And CStr(UploadFormRequest("MM_recordId")) <> "") Then'更新图片

  MM_editConnection = MM_conn_STRING
  MM_editTable = "products"
  MM_editColumn = "id"
  MM_recordId = "" + UploadFormRequest("MM_recordId") + ""
  MM_editRedirectUrl = "pro_man2.asp"
  MM_fieldsStr  = "thumbwidth|value|thumbheight|value|thumb_pro|value"
  MM_columnsStr = "thumbwidth|none,none,NULL|thumbheight|none,none,NULL|thumb_pro|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & replace(UploadQueryString,"&&GP_upload=true","")
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & replace(UploadQueryString,"&&GP_upload=true","")
    End If
  End If

End If
%>
<%
' *** Update Record: (Modified for File Upload) set variables
If (CStr(UploadFormRequest("MM_update2")) <> "" And CStr(UploadFormRequest("MM_recordId")) <> "") Then'更新图片

  MM_editConnection = MM_conn_STRING
  MM_editTable = "products"
  MM_editColumn = "id"
  MM_recordId = "" + UploadFormRequest("MM_recordId") + ""
  MM_editRedirectUrl = "pro_man2.asp"
  MM_fieldsStr  = "imgwidth|value|imgheight|value|fmimg|value"
  MM_columnsStr = "imgwidth|none,none,NULL|imgheight|none,none,NULL|img|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & replace(UploadQueryString,"&&GP_upload=true","")
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & replace(UploadQueryString,"&&GP_upload=true","")
    End If
  End If

End If
%>
<%
' *** Update Record: (Modified for File Upload) set variables

If (CStr(UploadFormRequest("MM_update22")) <> "" And CStr(UploadFormRequest("MM_recordId")) <> "") Then'更新图片

  MM_editConnection = MM_conn_STRING
  MM_editTable = "products"
  MM_editColumn = "id"
  MM_recordId = "" + UploadFormRequest("MM_recordId") + ""
  MM_editRedirectUrl = "pro_man2.asp"
  MM_fieldsStr  = "imgwidth2|value|imgheight2|value|fmimg2|value"
  MM_columnsStr = "imgwidth2|none,none,NULL|imgheight2|none,none,NULL|img2|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & replace(UploadQueryString,"&&GP_upload=true","")
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & replace(UploadQueryString,"&&GP_upload=true","")
    End If
  End If

End If
%>
<%
if UploadFormRequest("oldimg") <> "" then'删除旧图
     Set File = CreateObject("Scripting.FileSystemObject")
	fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
	set fdir = File.getfile(fdir)
	set fdir = fdir.parentfolder
	set fdir = fdir.parentfolder								'获取父父目录
      ImagePath = fdir & "\img\" & UploadFormRequest("oldimg")
	  if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
end if'删除旧图
%>
<%
' *** Update Record: (Modified for File Upload) construct a sql update statement and execute it

If ((CStr(UploadFormRequest("MM_update2")) <> "" or CStr(UploadFormRequest("MM_update22")) <> "" or CStr(UploadFormRequest("MM_update0")) <> "") And CStr(UploadFormRequest("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next

if CStr(UploadFormRequest("MM_update2")) <> "" and UploadFormRequest("fmimg")<>"" then
  MM_editQuery = MM_editQuery & "," & made_thumb("..\img",UploadFormRequest("fmimg"),150,150) & " where " & MM_editColumn & " = " & MM_recordId
  else
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId
end if


  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

  if (CStr(UploadFormRequest("MM_update22")) <> "" or  CStr(UploadFormRequest("MM_update2")) <> "") AND watermark_on = true then	'加水印
	watermark MM_fields(5),"logo.gif",0,0.3,"&HFFFFFF"
  end if

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<% If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
end if
%>

<%
' *** Update Record: set variables

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "products"
  MM_editColumn = "id"
  MM_recordId = "" + request.form("MM_recordId") + ""
  MM_editRedirectUrl = "pro_man2.asp"
  MM_fieldsStr  = "html|value|chanpingid|value|content|value|content2|value|name|value|name_en|value|img|value|is_new|value|is_top|value|is_newest|value"
  MM_columnsStr = "html|none,none,NULL|chanpingid|',none,''|content|',none,''|content2|',none,''|name|',none,''|name_en|',none,''|img|',none,''|is_new|none,none,NULL|is_top|none,none,NULL|is_newest|none,none,NULL"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
	if filterjp_on = true and ( (INStr(MM_fields(i),"content") = 1) or (INStr(MM_fields(i),"name") = 1) or (INStr(MM_fields(i),"chanpingid") = 1)) then	'过滤日文片假名，但只过滤name, chanpingid,content开头的字符防止在windows2000上搜索出现错误
	MM_fields(i+1) = CStr(filterjp(request.form(MM_fields(i))))
	else
    MM_fields(i+1) = CStr(request.form(MM_fields(i)))
	end if
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId
'	response.write(MM_editQuery)
'	response.end
  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
	 response.write MM_editQuery

    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsMODPAGE4__MMColParam
rsMODPAGE4__MMColParam = "1"
if (Request.QueryString("proid") <> "") then rsMODPAGE4__MMColParam = Request.QueryString("proid")
%>
<%
set rsMODPAGE4 = Server.CreateObject("ADODB.Recordset")
rsMODPAGE4.ActiveConnection = MM_conn_STRING
if pro_trademark_on = 1 then
rsMODPAGE4.Source = "SELECT products.*,pro_trademark.name_en as trademark FROM products,pro_trademark WHERE products.id = " + Replace(rsMODPAGE4__MMColParam, "'", "''") + " and products.trademark_id=pro_trademark.id"
else
rsMODPAGE4.Source = "SELECT * FROM products WHERE id = " + Replace(rsMODPAGE4__MMColParam, "'", "''") + ""
end if

rsMODPAGE4.CursorType = 0
rsMODPAGE4.CursorLocation = 2
rsMODPAGE4.LockType = 3
rsMODPAGE4.Open()
rsMODPAGE4_numRows = 0
%>
<%
Dim rsBACKID__MMColParam
rsBACKID__MMColParam = "1"
If (Request.QueryString("proid") <> "") Then 
  rsBACKID__MMColParam = Request.QueryString("proid")
End If
%>
<%
Dim rsBACKID
Dim rsBACKID_numRows

Set rsBACKID = Server.CreateObject("ADODB.Recordset")
rsBACKID.ActiveConnection = MM_conn_STRING
rsBACKID.Source = "SELECT id, products.cate_id FROM products, pro_category WHERE id = " + Replace(rsBACKID__MMColParam, "'", "''") + " AND products.cate_id=pro_category.cate_id"
rsBACKID.CursorType = 0
rsBACKID.CursorLocation = 2
rsBACKID.LockType = 1
rsBACKID.Open()

rsBACKID_numRows = 0
%>
<%
Dim rsCATEGORY
Dim rsCATEGORY_numRows

Set rsCATEGORY = Server.CreateObject("ADODB.Recordset")
rsCATEGORY.ActiveConnection = MM_conn_STRING
rsCATEGORY.Source = "SELECT cate_name, cate_id FROM pro_category ORDER BY cate_id ASC"
rsCATEGORY.CursorType = 0
rsCATEGORY.CursorLocation = 2
rsCATEGORY.LockType = 1
rsCATEGORY.Open()

rsCATEGORY_numRows = 0
%>
<%
Dim Repeat1__numRows
Dim Repeat1__index

Repeat1__numRows = -1
Repeat1__index = 0
rsCATEGORY_numRows = rsCATEGORY_numRows + Repeat1__numRows
%>
<%
dim back_id
back_id = "pro_man.asp?id=" & rsBACKID.Fields.Item("cate_id").value & "&proid=" & rsBACKID.Fields.Item("id").Value
%>
<%
rsBACKID.Close()
Set rsBACKID = Nothing
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">查看与编辑产品</h1>
<font color=ff0000><%=(server.htmlencode(rsMODPAGE4.Fields.Item("name").Value))%></font>的资料
<a href="<%=(back_id)%>">← 返回</a><br><br>
	<% If Not rsMODPAGE4.EOF Or Not rsMODPAGE4.BOF Then %>
	<form ACTION="<%=MM_editAction%>" METHOD="POST" name="form11">
	<input type="hidden" name="img" value="<%=(rsMODPAGE4.Fields.Item("img").Value)%>">
	<input type="hidden" name="name" value="<%=(server.htmlencode(rsMODPAGE4.Fields.Item("name").Value))%>">
	  <table border="1" cellspacing="0" cellpadding="4" bordercolor="#C69EDE" align=center>
		<tr> 
		  <td valign="top" width="126">所属类别</td>
		  <td width="400"><%if pro_trademark_on = 1 then response.write("<font color=ff0000>" & rsMODPAGE4("trademark") & "</font>")%>
                    <select name="cate_id" disabled>
					  <% If Not rsCATEGORY.EOF Or Not rsCATEGORY.BOF Then %>
                    <% 
While ((Repeat1__numRows <> 0) AND (NOT rsCATEGORY.EOF)) 
%>                      <option value="<%=(rsCATEGORY.Fields.Item("cate_id").Value)%>"<%if cstr(rsMODPAGE4("cate_id"))=cstr(rsCATEGORY.Fields.Item("cate_id").Value) then response.write(" selected")%>><%=(server.htmlencode(rsCATEGORY.Fields.Item("cate_name").Value))%></option>
                    <% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsCATEGORY.MoveNext()
Wend
%><% End If ' end Not rsCATEGORY.EOF Or NOT rsCATEGORY.BOF %>
                    </select>

		  </td>
		</tr>
<%if pro_content = true then%>
		<tr> 
		  <td valign="top" width="126">产品简介<br>
			<font size="-1"><br>
			注：请将文字排好。<br>
			段中不要回车；<br>
			段前空格用全角；<br>
			段与段之间空一行。<br><font color=#ff0000>非HTML格式</font></font></td>
		  <td width="400"> 
			<textarea class=i550 name="content" cols="80" rows="10"><%=(rsMODPAGE4.Fields.Item("content").Value)%></textarea>
		  </td>
		</tr>
<%end if%>
<!-- 
		<tr> 
		  <td width="126">产品说明是否html</td>
		  <td width="400"> 
			<% if rsMODPAGE4.Fields.Item("html").Value=0 then
response.write "否 <input type='radio' name='html' value='0' checked><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='html' value='1'>"
else
response.write "否 <input type='radio' name='html' value='0'><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='html' value='1' checked>"
end if
%>
			<img src='/images/spacer.gif' width=40 height=1> <font size=-1>（注：如果不懂html语法请不要选择html格式） 
			</font></td>
		</tr>

 		<tr> 
		  <td valign="top" width="126">产品号（不显示）</td>
		  <td width="400"> 
			<input style="width:200px" name="chanpingid" value="<%=(rsMODPAGE4.Fields.Item("chanpingid").Value)%>">
		  </td>
		</tr>
		<tr> 
		  <td valign="top" width="126">产品中文名</td><td width=400><input style="width:200px" name="name" value="<%=(rsMODPAGE4.Fields.Item("name").Value)%>"></td></tr>
 		<tr><td valign="top" width="126">英文名(english)</td><td width=400><input style="width:200px" name="name_en" value="<%=(rsMODPAGE4.Fields.Item("name_en").Value)%>"></td></tr>
		</tr>
 -->
 		<tr> 
		  <td valign="top" width="126">详细说明</font></td>
		  <td width="550"><textarea name="content" style="display:none"><%
if trim(rsMODPAGE4.Fields.Item("content").Value)<>"" then
response.write(Server.HtmlEncode(rsMODPAGE4.Fields.Item("content").Value))
else
end if%></textarea>
			<iframe ID="htmlbuilder1" src="htmlbuilder/htmlbuilder.asp?id=content&style=<%=hb_css%>" frameborder="0" scrolling="no" width="550" HEIGHT="350"></iframe>
		  </td>
<%if is_newest_on =true then%>
		<tr> 
		  <td width="126">是否在首页新品里显示</td>
		  <td width="400"> 
			<% if rsMODPAGE4.Fields.Item("is_newest").Value=0 then
response.write "否 <input type='radio' name='is_newest' value='0' checked><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_newest' value='1'>"
else
response.write "否 <input type='radio' name='is_newest' value='0'><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_newest' value='1' checked>"
end if
%></td>
		</tr>
<%end if%>
<%if is_new_on =true then%>
		<tr> 
		  <td width="126">是否推荐新品</td>
		  <td width="400"> 
			<% if rsMODPAGE4.Fields.Item("is_new").Value=0 then
response.write "否 <input type='radio' name='is_new' value='0' checked><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_new' value='1'>"
else
response.write "否 <input type='radio' name='is_new' value='0'><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_new' value='1' checked>"
end if
%></td>
		</tr>
<%end if%>
<%if is_top_on =true then%>
		<tr> 
		  <td width="126">是否在页面顶部显示</td>
		  <td width="400"> 
			<% if rsMODPAGE4.Fields.Item("is_top").Value=0 then
response.write "否 <input type='radio' name='is_top' value='0' checked><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_top' value='1'>"
else
response.write "否 <input type='radio' name='is_top' value='0'><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_top' value='1' checked>"
end if
%></td>
		</tr>
<%end if%>
		<tr> 
		  <td width="126">&nbsp;</td>
		  <td width="400"> 
			<input type="submit" name="Submit" value="提交" onClick="MM_validateForm('fmtitle','','R');return document.MM_returnValue">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="form11">
	  <input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
	</form>
<script language="JavaScript">
<!--

function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} }

function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}
function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(gif|jpg|png|bmp|jpeg)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('上传文件太小!\n应大于 ' + img.minWidth + ' x ' + img.minHeight + ' 像素'); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('上传文件太大!\n不超过 ' + img.maxWidth + ' x ' + img.maxHeight + ' 像素'); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('上传文件太大!\n不超过 ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}
//-->
</script>
<%if thumb_pro_on = true then%>

产品缩略图
<form name="form12" method="POST" action="<%=MM_editAction2%>" enctype="multipart/form-data" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG',false,400,'','','','80','imgwidth','imgheight');return document.MM_returnValue">
	  <table border="1" cellspacing="0" cellpadding="4" bordercolor="#C69EDE" align=center>
		  <tr> 
			<td width="126" align=center><% if rsMODPAGE4.Fields.Item("thumb").Value <> "" then%><a onClick="MM_openBrWindow('../img.asp?img_dir=img&img=<%=server.urlencode((rsMODPAGE4.Fields.Item("thumb").Value))%>&imgwidth=<%=(rsMODPAGE4.Fields.Item("thumbwidth").Value)%>&imgheight=<%=(rsMODPAGE4.Fields.Item("thumbheight").Value)%>&title=<%= Server.urlEncode((rsMODPAGE4.Fields.Item("name").Value))%>','','scrollbars=yes,resizable=yes,width=200,height=120')" href="javascript:void(null)" onmouseout="window.status='';return true;" 
onmouseover="window.status='单击打开详图';return true;"><img src="../img/<%=(rsMODPAGE4.Fields.Item("thumb").Value)%>" width="<%=rsMODPAGE4("thumbwidth")%>" alt="点击打开详图" border=0></a><%else%>
&nbsp;
<%END IF%></td>
			<td width="400"><input type=hidden name="thumbwidth">
			<input type=hidden name="thumbheight">
			  <input type="file" name="thumb" size="40" onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,500,'','','','80','thumbwidth','thumbheight')"></td>
		  </tr>
		  <tr><td width="126">&nbsp;</td><td width="400" nowrap><input type="submit" name="Submit" value="确定" <%if rsMODPAGE4.Fields.Item("thumb").Value <> "" then %>
	onClick="GP_popupConfirmMsg('确定修改或删除图片？');return document.MM_returnValue"<%end if%>><% if rsMODPAGE4.Fields.Item("thumb").Value <> "" then%><font size=-1>（如果要删除图片，不要选择图片，直接按下确定按钮即可）</font>
		  <%END IF%></td></tr>
<input type=hidden name="oldimg" value="<%=(rsMODPAGE4.Fields.Item("thumb").Value)%>">
<input type="hidden" name="MM_update0" value="form12">
<input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
	</table>
	</form>

<%end if%>

<%if img_big_on = true then%>
产品代表图片

<form name="form120" method="POST" action="<%=MM_editAction22%>" enctype="multipart/form-data" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG',false,400,'','','','','imgwidth','imgheight');return document.MM_returnValue">
	  <table border="1" cellspacing="0" cellpadding="4" bordercolor="#C69EDE" align=center>
		  <tr> 
			<td width="126"><% if rsMODPAGE4.Fields.Item("img").Value <> "" then%><a onClick="MM_openBrWindow('../img.asp?img_dir=img&imgwidth=<%=rsMODPAGE4("imgwidth")%>&imgheight=<%=rsMODPAGE4("imgheight")%>&img=<%=server.urlencode(rsMODPAGE4.Fields.Item("img").Value)%>&title=<%= Server.urlEncode((rsMODPAGE4.Fields.Item("name").Value))%>','','scrollbars=yes,resizable=yes,width=700,height=500')" href="javascript:void(null)" onmouseout="window.status='';return true;" 
onmouseover="window.status='单击打开详图';return true;"><img src="../img/<%=(rsMODPAGE4.Fields.Item("img").Value)%>" width="126" alt="点击打开详图" border=0></a><%else%>
&nbsp;
<%END IF%></td>
			<td width="400"> <input type=hidden name="imgwidth">
			<input type=hidden name="imgheight">
			<input type=hidden name="wartermark_on" value="false">
			  <input type="file" name="fmimg" size="40"onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,500,'','','','','imgwidth','imgheight')">
			  </td>
		  </tr>
		  <tr><td width="126">&nbsp;</td><td width="400" nowrap><input type="submit" name="Submit" value="确定" <%if rsMODPAGE4.Fields.Item("img").Value <> "" then %>
	onClick="GP_popupConfirmMsg('确定修改或删除图片？');return document.MM_returnValue"<%end if%>><% if rsMODPAGE4.Fields.Item("img").Value <> "" then%><font size=-1>（如果要删除图片，不要选择图片，直接按下确定按钮即可）</font>
		  <%END IF%></td></tr>
<input type=hidden name="oldimg" value="<%=(rsMODPAGE4.Fields.Item("img").Value)%>">
<input type="hidden" name="MM_update2" value="form120">
<input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
	</table>
	</form>
<%end if%>

<%if img_big2_on = true then%>
产品图二
<form name="form22" method="POST" action="<%=MM_editAction22%>" enctype="multipart/form-data" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG');return document.MM_returnValue">
	  <table border="1" cellspacing="0" cellpadding="4" bordercolor="#C69EDE" align=center>
		  <tr> 
			<td width="126"><% if rsMODPAGE4.Fields.Item("img2").Value <> "" then%><a onClick="MM_openBrWindow('../img.asp?img_dir=img&img=<%=server.urlencode(rsMODPAGE4.Fields.Item("img2").Value)%>&imgwidth=<%=rsMODPAGE4("img2width")%>&imgheight=<%=rsMODPAGE4("img2height")%>&title=<%= Server.urlencode((rsMODPAGE4.Fields.Item("name").Value))%>','','scrollbars=yes,resizable=yes,width=700,height=500')" href="javascript:void(null)" onmouseout="window.status='';return true;" 
onmouseover="window.status='单击打开详图';return true;"><img src="../img/<%=(rsMODPAGE4.Fields.Item("img2").Value)%>" width="126" alt="点击打开详图" border=0></a><%else%>
&nbsp;
<%END IF%></td>
			<td width="400"><input type=hidden name="imgwidth2">
			<input type=hidden name="imgheight2"><input type=hidden name="wartermark_on" value="true"> 
			  <input type="file" name="fmimg2" size="40"onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,500,'','','','','imgwidth2','imgheight2')">
			 </td>
		  </tr>
		  <tr><td width="126">&nbsp;</td><td width="400" nowrap><input type="submit" name="Submit" value="确定" <%if rsMODPAGE4.Fields.Item("img2").Value <> "" then %>
	onClick="GP_popupConfirmMsg('确定修改或删除图片？');return document.MM_returnValue"<%end if%>><% if rsMODPAGE4.Fields.Item("img2").Value <> "" then%><font size=-1>（如果要删除图片，不要选择图片，直接按下确定按钮即可）</font>
		  <%END IF%></td></tr>
<input type=hidden name="oldimg" value="<%=(rsMODPAGE4.Fields.Item("img2").Value)%>">
<input type="hidden" name="MM_update22" value="form22">
<input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
	</table>
	</form>
<%end if%>
	<% else
	  response.write "<h1>没有选择产品</h1>"
	  End If ' end Not rsMODPAGE4.EOF Or NOT rsMODPAGE4.BOF %>
<!--#include file ="_bottom.asp"-->  
<%
rsMODPAGE4.Close()
set rsMODPAGE4=nothing
rsCATEGORY.Close()
Set rsCATEGORY = Nothing


function made_thumb(path,img,imgwidth,imgheight)	
	'生成缩略图，源图相对路径（最后不带\），源图文件名，缩略图尺寸，尺寸中不能有0
	'输出缩略图名称, 宽度, 高度
	dim bigimg,bigimgpath,thumbpath
	bigimgpath = Server.MapPath(path) & "\" & img
	thumbpath = Server.MapPath(path) & "\" & "thumb__"  & img
	Set bigimg = Server.CreateObject("Persits.Jpeg")
	bigimg.Open bigimgpath

	if bigimg.OriginalWidth/bigimg.OriginalHeight < imgwidth/imgheight then	'如果原图比较高
		if bigimg.OriginalHeight <= imgheight then	'如果原图太小
			made_thumb = "thumb='" & img & "',thumbwidth=" & cstr(bigimg.OriginalWidth) & ",thumbheight="  & cstr(bigimg.OriginalHeight)
		else
			bigimg.Width = imgheight * bigimg.OriginalWidth/bigimg.OriginalHeight
			bigimg.Height = imgheight
			bigimg.Save thumbpath
			made_thumb = "thumb='thumb__"  & img & "',thumbwidth=" & cstr(bigimg.Width) & ",thumbheight=" & cstr(bigimg.Height)
		end if
	else	'如果原图比较宽
		if bigimg.OriginalWidth <= imgwidth then	'如果原图太小
			made_thumb = "thumb='" & img & "',thumbwidth=" & cstr(bigimg.OriginalWidth) & ",thumbheight="  & cstr(bigimg.OriginalHeight)
		else
			bigimg.Width = imgwidth
			bigimg.Height = imgwidth * bigimg.OriginalHeight/bigimg.OriginalWidth
			bigimg.Save thumbpath
			made_thumb = "thumb='thumb__"  & img & "',thumbwidth=" & cstr(bigimg.Width) & ",thumbheight=" & cstr(bigimg.Height)
		end if
	end if
	bigimg.close()
	set bigimg = nothing
end function
%>
