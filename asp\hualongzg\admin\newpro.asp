<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">新产品</h1>
<style type="text/css">
/* <![CDATA[ */
ul.newpro{list-style-type:none;}
ul.newpro li{display:block;width:150px;float:left;position:relative}
ul.newpro li a{width:100px;color:#333;padding:0 0 0 3px}
ul.newpro li a:hover{background-color:#dedede}
ul.newpro li div.sortmenu{position:absolute;left:28px;top:18px;display:none;background-color:#eee}
ul.newpro li div.sortmenu a{display:block;width:50px;border:1px solid #666}
ul.newpro li div.sortmenu a:hover{color:#f00}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
Event.onDOMReady(newprostart);

function newprostart(){
	gotoPage();newpro();
	Event.observe($("pro_area"),'change',function(){gotoPage();newpro();});
}

function shownew(obj){
	if(obj.value=="只显示新产品"){
		$A($$("ul.newpro li")).each(function(node){
			if (!node.down('input').checked){
				node.style.display = "none";
			}
		});		
		obj.value ="显示所有产品"
	}else{
		$A($$("ul.newpro li")).each(function(node){
			node.style.display = "block";	
		});		
		obj.value="只显示新产品"
	}
}

function newpro(){
	nCol = $$("ul.newpro")[0].getElementsByTagName("li");
	for (i=nCol.length-1;i>=0;i--){
		nCol[i].style.zIndex = nCol.length -i+2;
	}

	$A($$("ul.newpro a.proname")).each(function(node){
		Event.observe(node,'mouseover',function(){
			node.next(0).style.display = "block";
		});
		Event.observe(node,'mouseout',function(){
			node.next(0).style.display = "none";
		});
	});

	$A($$("ul.newpro div.sortmenu")).each(function(node){
		Event.observe(node,'mouseover',function(){
			node.previous(0).style.backgroundColor = "#dedede";
			node.style.display = "block";
		});
		Event.observe(node,'mouseout',function(){
			node.previous(0).style.backgroundColor = "";
			node.style.display = "none";
		});
	});
}
function gotoPage(){
	var url = 'newpro_ajax.asp?action=ok&lcode=' + $("pro_area").children[$("pro_area").selectedIndex].value;
	var myAjax = new Ajax.Updater(document.getElementsByClassName("newpro")[0], url, {method: 'get',asynchronous:false});
}

function sortmenu(id,po){
	var url = 'newpro_ajax_sort.asp';
	var pars = 'id=' + id + '&po=' + po;
	var myAjax = new Ajax.Request(url, {method: 'get', parameters: pars,onSuccess:function(){gotoPage();newpro();$("onlysnew").value="只显示新产品";}});
}
function newprosub(){
	var loop = 0,nid="";
	for (loop=0; loop < document.newpro.elements.length; loop++) { 
		if(document.newpro.elements[loop].checked == true) {
			nid=(nid=="")?document.newpro.elements[loop].value:nid+","+document.newpro.elements[loop].value;
		}
	}
	var url = 'newpro_ajax_sub.asp';
	var pars = 'action=ok&id='+nid+'&lcode=' + $("pro_area").children[$("pro_area").selectedIndex].value; 
	var myAjax = new Ajax.Request(url, {method: 'get', parameters: pars,onSuccess:function(){gotoPage();newpro();alert("新产品修改成功");}});
}

//]]>
</script>
<%
set rs=server.createobject("adodb.recordset")
sql="select area.cate_name,area.cate_lcode from area where cate_level=2 and cate_type_id=1 order by cate_lcode asc"
rs.open sql,MM_conn_STRING,1,1
If rs.recordcount > 0 then%>
<select id="pro_area" style="margin-bottom:10px"><%while Not rs.eof%>
	<option value="<%=rs("cate_lcode")%>"><%=rs("cate_name")%></option><%
	rs.movenext()
	Wend
	%>
</select><%
End If
rs.close()
Set rs=nothing
%>
<form method="post" action="newpro.asp" name="newpro">
<ul class="newpro"></ul>

<p style="display:block;clear:left;padding-top:10px"><input type="hidden" name="action" value="ok" />
<input type="submit" value="提交" id="submitnew" onclick="newprosub();return false;" style="width:100px;height:22px;border:1px #666 solid" />
<input type="submit" id="onlysnew" onclick="shownew(this);return false;" value="只显示新产品" style="width:100px;height:22px;border:1px #666 solid;margin-left:5px" />
</p>
</form>

<!--#include file ="_bottom.asp"-->