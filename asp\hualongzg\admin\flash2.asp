<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="../ScriptLibrary/Upload_class.asp"-->
<%'上传处理
ok_page_name = "flash2.asp"						'上传成功后转向页面

if request.querystring("style") = "" then
	Dim PostRanNum
	Randomize
	PostRanNum = Int(900*rnd)+1000
	Session("UploadCode") = Cstr(PostRanNum)
else
	UploadFile
end if

Sub UploadFile()
	Dim Upload,FilePath,FormName,File,F_FileName
	FilePath = "../images/"
	Set Upload = New UpFile_Cls
		Upload.UploadType			= 0										'设置上传组件类型
		Upload.UploadPath			= FilePath								'设置上传路径
		Upload.MaxSize				= 100									'单位 KB
		Upload.InceptMaxFile		= 1										'每次上传文件个数上限
		Upload.InceptFileType		= "swf"									'设置上传文件限制
		Upload.RName				= "banner-" & request("style")
		Upload.RName_TempStr_on		= false									'不在文件名后加自动字符串
		Upload.ChkSessionName		= "UploadCode"
		'执行上传
		Upload.SaveUpFile
		If Upload.ErrCodes<>0 Then
			Response.write "<script language=JavaScript>alert(""错误："& Upload.Description & "\n重新上传"")</script>"
'			response.redirect(ok_page_name)
			'<div class=p14>错误："& Upload.Description & "[ <a href=" & ok_page_name & ">重新上传</a> ]</div>"
			Exit Sub
		End If
		If Upload.Count > 0 Then
			For Each FormName In Upload.UploadFiles
				Set File = Upload.UploadFiles(FormName)
					F_FileName = FilePath & File.FileName
					Session("upface")="done"
					Response.Write "<script language=JavaScript>alert(""动画"& F_FileName &"上传成功!"")</script>"
				Set File = Nothing
			Next
			response.redirect(ok_page_name)
		Else
			Response.write "<script language=JavaScript>alert(""错误：请正确选择要上传的文件。\n重新上传"")</script>"
'			response.redirect(ok_page_name)
			Exit Sub
		End If
	Set Upload = Nothing
end sub



%>

<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">页顶动画</h1>
<script language="JavaScript">
<!--

function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} }

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('请选择文件上传!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(swf)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}
//-->
</script>
<div class=p14><a href=../images/banner.fla>→ 点击下载FLASH源文件</a>（动画大小：430×131）。修改动画时注意与背景协调<br>
　 修改后如果看不到效果，请清空浏览器缓存。</div><br>

<table align=center width=700 cellpadding=2 cellspacing=0 border=1 bordercolor=<%=(back_menubackcolor)%>>
<tr align=center bgcolor=<%=(back_menubackcolor)%>>
	<td width=100>栏目</td><td>修改</td><td>预览</td>
</tr>
<form method=post enctype="multipart/form-data" action="flash2.asp?style=0" onSubmit="checkFileUpload(this,'swf',true,100,'','','','','','');return document.MM_returnValue">
<tr align=center>
	<td class=p12>网站首页</td><td align=right><input type="file" name="fmimg" size="80" style="width:200px"><br><input type="submit" value="修改" style="width:60px" onClick="GP_popupConfirmMsg('确定修改首页顶部的FLASH动画？');return document.MM_returnValue;"></td>
	<td width=430 height=131><object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="430" height="131"><param name="movie" value="../images/banner-0.swf"><param name="quality" value="high"><param name="menu" value="false"><embed src="../images/banner-0.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="430" height="131"></embed></object></td>
</tr>
<INPUT TYPE="hidden" NAME="UploadCode" value="<%=PostRanNum%>">
</form>
<%
set rs=server.createobject("adodb.recordset") 
sql="select id,name from class order by id asc" 
rs.open sql,MM_conn_STRING,1,1 
while not rs.eof 
%>
<form method=post enctype="multipart/form-data" action="flash2.asp?style=<%=rs("id")%>" onSubmit="checkFileUpload(this,'swf',true,100,'','','','','','');return document.MM_returnValue">
<tr align=center>
	<td class=p12><%=rs("name")%></td><td class=p12 align=right><input type="file" name="fmimg" size="80" style="width:200px"><br><input type="submit" value="修改" style="width:60px" onClick="GP_popupConfirmMsg('确定修改<%=rs("name")%>顶部的FLASH动画？');return document.MM_returnValue"></td><td width=430 height=131><object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="430" height="131"><param name="movie" value="../images/banner-<%=rs("id")%>.swf"><param name="quality" value="high"><param name="menu" value="false"><embed src="../images/banner-<%=rs("id")%>.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="430" height="131"></embed></object></td>
</tr>
<INPUT TYPE="hidden" NAME="UploadCode" value="<%=PostRanNum%>">
</form>
<%
rs.movenext()
wend
rs.close()
set rs=nothing
%>
<form method=post enctype="multipart/form-data" action="flash2.asp?style=16" onSubmit="checkFileUpload(this,'swf',true,100,'','','','','','');return document.MM_returnValue">
<tr align=center>
	<td class=p12>网站搜索</td><td align=right><input type="file" name="fmimg" size="80" style="width:200px"><br><input type="submit" value="修改" style="width:60px" onClick="GP_popupConfirmMsg('确定修改网站搜索顶部的FLASH动画？');return document.MM_returnValue"></td><td width=430 height=131><object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="430" height="131"><param name="movie" value="../images/banner-16.swf"><param name="quality" value="high"><param name="menu" value="false"><embed src="../images/banner-16.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="430" height="131"></embed></object></td>
</tr>
<INPUT TYPE="hidden" NAME="UploadCode" value="<%=PostRanNum%>">
</form>

<form method=post enctype="multipart/form-data" action="flash2.asp?style=17" onSubmit="checkFileUpload(this,'swf',true,100,'','','','','','');return document.MM_returnValue">
<tr align=center>
	<td class=p12>网站地图</td><td align=right><input type="file" name="fmimg" size="80" style="width:200px"><br><input type="submit" value="修改" style="width:60px" onClick="GP_popupConfirmMsg('确定修改网站地图顶部的FLASH动画？');return document.MM_returnValue"></td><td width=430 height=131><object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="430" height="131"><param name="movie" value="../images/banner-17.swf"><param name="quality" value="high"><param name="menu" value="false"><embed src="../images/banner-17.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="430" height="131"></embed></object></td>
</tr>
<INPUT TYPE="hidden" NAME="UploadCode" value="<%=PostRanNum%>">
</form>

<form method=post enctype="multipart/form-data" action="flash2.asp?style=18" onSubmit="checkFileUpload(this,'swf',true,100,'','','','','','');return document.MM_returnValue">
<tr align=center>
	<td class=p12>邮件中心</td><td align=right><input type="file" name="fmimg" size="80" style="width:200px"><br><input type="submit" value="修改" style="width:60px" onClick="GP_popupConfirmMsg('确定修改邮件中心顶部的FLASH动画？');return document.MM_returnValue"></td><td width=430 height=131><object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0" width="430" height="131"><param name="movie" value="../images/banner-18.swf"><param name="quality" value="high"><param name="menu" value="false"><embed src="../images/banner-18.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="430" height="131"></embed></object></td>
</tr>
<INPUT TYPE="hidden" NAME="UploadCode" value="<%=PostRanNum%>">
<input type=hidden name="style" value=18>
</form>
</table>

<!--#include file ="_bottom.asp"-->
