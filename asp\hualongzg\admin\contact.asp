<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<%
If request.Form("action") <> "" Then
	set rs=server.createobject("adodb.recordset")
	sql="select * from contact order by id asc"
	rs.open sql,MM_conn_STRING,3,3
	while Not rs.eof
		For Each x In Request.Form
			If x = rs("name") Then
				rs("content") = Request.Form(x)
				rs.update
				Exit For
			End if
		Next
	rs.movenext()
	wend

	'写前台_contact文件
	contact_body = "<%" & vbCrlf
	rs.MoveFirst()
	While Not rs.eof
		contact_body =  contact_body & rs("name") & "=""" & HTMLEncode(rs("content")) & """" & vbcrlf
		rs.movenext()
	wend
	rs.close()

	sql = "SELECT title,name FROM other WHERE left(name,11) = 'intro_title'"
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof then
		While Not rs.eof
		contact_body = contact_body & rs("name") & "=""" & rs("title") & """" & vbcrlf
		rs.movenext()
		Wend
	End if
	rs.close()
	set rs=Nothing
	contact_body = contact_body & vbCrlf & "%" & ">"			'结束封闭
	Call SaveToFile(contact_body,"..\_contact.asp")
	response.redirect("contact.asp")
End If

%>
<style type="text/css">
/* <![CDATA[ */
table.onlyh,table.onlyh td{border:0}
table.onlyh thead{background-color:#ccc}
table.onlyh tbody td{border-bottom:1px solid #ccc}
/* ]]> */ 
</style>
<h1 class="b_ch1">联系方式</h1>
<table width=700 border=1 cellspacing=2 class="onlyh">
<form method="post" action="contact.asp">
<thead><tr bgcolor="<%=(back_titlebackcolor)%>" align=center><td width=150 nowrap>联系项目</td><td>&nbsp;</td></tr></thead>
<tbody>
<%
set rs=server.createobject("adodb.recordset")
sql = "select * from contact order by id asc"
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
response.write "<tr><td align=center>" & rs("name_des") & "</td><td><input style=""width:550px"" name=""" & rs("name")  & """ value=""" & rs("content") & """></td></tr>"
rs.movenext()
Wend
rs.close()
Set rs=nothing
%></tbody>
<tfoot>
<tr><td colspan=2 align=center><input name="action" type="submit" value="修 改" /></tr></tfoot>
</form></table>
<!--#include file ="_bottom.asp"-->