Object.extend(Event, {
  _domReady : function() {
    if (arguments.callee.done) return;
    arguments.callee.done = true;

    if (this._timer)  clearInterval(this._timer);
    
    this._readyCallbacks.each(function(f) { f() });
    this._readyCallbacks = null;
},
  onDOMReady : function(f) {
    if (!this._readyCallbacks) {
      var domReady = this._domReady.bind(this);
      
      if (document.addEventListener)
        document.addEventListener("DOMContentLoaded", domReady, false);
        
        /*@cc_on @*/
        /*@if (@_win32)
            document.write("<script id=__ie_onload defer src=javascript:void(0)><\/script>");
            document.getElementById("__ie_onload").onreadystatechange = function() {
                if (this.readyState == "complete") domReady(); 
            };
        /*@end @*/
        
        if (/WebKit/i.test(navigator.userAgent)) { 
          this._timer = setInterval(function() {
            if (/loaded|complete/.test(document.readyState)) domReady(); 
          }, 10);
        }
        
        Event.observe(window, 'load', domReady);
        Event._readyCallbacks =  [];
    }
    Event._readyCallbacks.push(f);
  }
});

Event.onDOMReady(initialize);

function initialize(){
	disabledoption();
	var obj = document.getElementsByClassName("b_content_wapper")[0];
	if (obj.offsetHeight < 350){obj.style.height = "350px";	}

	nCol = $("b_nav").getElementsByTagName("UL");
	for (i=0;i<nCol.length;i++)	{
		if (nCol[i].up(1)==$("b_nav")){
			nCol[i].addClassName('b_nav1');
		}else{
			nCol[i].addClassName('b_nav' + (Number(nCol[i].up(1).className.substring(nCol[i].up(1).className.length-1,nCol[i].up(1).className.length))+1));
		}
	}

	nCol = $("b_nav").getElementsByTagName("LI");
	for (i=0;i<nCol.length;i++)	{
		if (nCol[i].className != "b_navstart" && nCol[i].className != "b_nav" && nCol[i].className != "b_navend"){
			nCol[i].addClassName(nCol[i].up(0).className);
		}
	}

	nCol = $("b_nav").getElementsByTagName("A");
	for (i=nCol.length-1;i>=0;i--)	{
		if (nCol[i].href.toString().match(/\/(\w*)\.[^\/]*$/)[1]==window.location.toString().match(/\/(\w*)[\+|\.][^\/]*$/)[1]){
			obj = nCol[i];
			while (obj.up(1) != $("b_nav")){
				obj.addClassName('ac');
				//如果是下拉菜单，当前菜单不用
				//if (obj.up(1).hasClassName('b_nav1')){obj.up(1).style.display = "block";}

				//设置ul.b_nav1宽度与边距，在下拉菜单中不用
				// if (obj.up(1).hasClassName('b_nav1')){ulBnav1Width(obj.up(1));}

				if(obj.up(2).down("a")){obj = obj.up(2).down("a");}
			}
			if (obj.up(1) == $("b_nav"))obj.addClassName('ac');
			break;
		}
	}
	nCol = $("b_nav").getElementsByTagName("A");
	for (i=0;i<nCol.length;i++)	{
		if (nCol[i].nextSibling && nCol[i].nextSibling.tagName=="UL"){
			//加下拉箭头
			nCol[i].style.paddingRight = "15px";
			nCol[i].style.backgroundImage = "url(interface/njtb.png)";
			nCol[i].style.backgroundRepeat = "no-repeat";
			nCol[i].style.backgroundPosition = "right 14px";
			//

			nCol[i].onmouseover = function(){
				$$('#b_nav a.ac').each(function(node){if(node.up(1) != $("b_nav")) node.up(1).style.display="none"})
				this.nextSibling.style.display = "block";

				//设置ul.b_nav1宽度与边距，在下拉菜单中不用
				//if (this.nextSibling.hasClassName('b_nav1')){ulBnav1Width(this.nextSibling);}
			}
			nCol[i].onmouseout=function(){
				if (!(this.hasClassName('ac') && this.hasClassName('b_nav1')))this.nextSibling.style.display = "none";
			}
		}

	}
	nCol = $("b_nav").getElementsByTagName("UL");
	for (i=0;i<nCol.length;i++)	{
		nCol[i].onmouseover = function(){
			this.style.display = "block";
			if(!this.previous(0).hasClassName('ao')){this.previous(0).addClassName('ao');}
		}
		nCol[i].onmouseout = function(){
			this.style.display = "none";
			if(this.previous(0).hasClassName('ao')){this.previous(0).removeClassName('ao');}
		}
	}

	$A($$("dl a.pucker")).each(function(node){
		Event.observe(node,'click',function(){
			if (node.hasClassName("down")){
				node.up(0).next("dd").style.display = "block";
				if (node.hasClassName("down")){node.up(0).removeClassName("down")}
				node.removeClassName("down");
			}else{
				node.up(0).next("dd").style.display = "none";
				node.up(0).addClassName("down")
				node.addClassName("down");			
			}
		});
	});

	function ulBnav1Width (obj){//水平放置时 ul.b_nav1 的位置与宽度调整
		var ulnav1Width = 0, ulnav1Left = 0;
		$A(obj.childNodes).each(function(node){
			ulnav1Width += (node.offsetWidth+10);
		});
		ulnav1Left = (ulnav1Width + obj.up(0).offsetLeft > $R(document.body.clientWidth,$("b_nav").offsetWidth).max())?($R(document.body.clientWidth,$("b_nav").offsetWidth).max() - ulnav1Width - obj.up(0).offsetLeft):0;
		obj.style.width = ulnav1Width + "px";
		obj.style.left = ulnav1Left + "px";
	}
}


function sswitch(obj){
	var obj1 = $(obj.getAttribute("rel").toString());
	if (obj1.style.height == ""){
		obj1.style.height = "300px";
	}else{
		obj1.style.height = "";
	}
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
  document.MM_returnValue = (errors == '');
}

function popUp(URL,r,w,h) {
        winl = (screen.width - w) / 2;
        wint = (screen.height - h) / 2;
        eval("page" + " = window.open(URL, '" + "', 'toolbar=0,scrollbars="+r+",location=0,statusbar=0,menubar=0,resizable=1,width="+w+",height="+h+",top="+wint+",left="+winl+"');");
}

function GP_popupConfirmMsg(msg) { document.MM_returnValue = confirm(msg);}

function MM_jumpMenu(targ,selObj,restore){ //v3.0
  eval(targ+".location='"+selObj.options[selObj.selectedIndex].value+"'");
  if (restore) selObj.selectedIndex=0;
}

function disabledoption(){
	if (document.getElementsByTagName) {
		var s = document.getElementsByTagName("select");

		if (s.length > 0) {
			window.select_current = new Array();

			for (var i=0, select; select = s[i]; i++) {
				select.onfocus = function(){ window.select_current[this.id] = this.selectedIndex; }
				select.onchange = function(){ restore(this); }
				emulate(select);
			}
		}
	}
}

function restore(e) {
	if (e.options[e.selectedIndex].disabled) {
		e.selectedIndex = window.select_current[e.id];
	}
}

function emulate(e) {
	for (var i=0, option; option = e.options[i]; i++) {
		if (option.disabled) {
			option.style.color = "graytext";
		}
//		else {
//			option.style.color = "menutext";
//		}
	}
}