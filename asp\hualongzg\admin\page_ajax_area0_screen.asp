<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
Dim pageid
pageid = request.querystring("pageid")
set rsPAGE = Server.CreateObject("ADODB.Recordset")
sql = "SELECT page.pageid, page.title,page.this_not_del,page.isscreen,list.style_id,imgsmall,imgsmallwidth,imgsmallheight FROM page inner join list on page.subid=list.id WHERE pageid = " & pageid
rsPAGE.open sql,MM_conn_String,3,2
If rsPAGE("isscreen") = True Then
	rsPAGE("isscreen") = False
else
	rsPAGE("isscreen") = true
End if
rsPAGE.update
If rsPAGE("isscreen") = True Then 	
	isscreenclass = " class=""iscreen"""
	isscreenstr = "显示"
Else
	isscreenclass = ""
	isscreenstr = "屏蔽"
End if
%>
<div<%=isscreenclass%>><a href="javascript:void(null)" class="view" onclick="view(<%=rsPAGE("pageid")%>)"><%=rsPAGE("title")%></a>
<%If rsPAGE("style_id") = 2 Or rsPAGE("style_id") = 1 Then%>
	<div class="imgsmall"><img src="../upload/<%=rsPAGE("imgsmall")%>" width="<%=rsPAGE("imgsmallwidth")%>" height="<%=rsPAGE("imgsmallheight")%>" alt="" /></div>
<%End IF	%>
<div class="atobutton"><a href="javascript:void(null)" class="del" onclick="del(<%=rsPAGE("pageid")%>)"><span>删除</span></a><a href="javascript:void(null)" class="screen" onclick="screen(<%=rsPAGE("pageid")%>)"><%=isscreenstr%></a><a href="javascript:void(null)" onmouseover="sort(<%=rsPAGE("pageid")%>)" onmouseout="sorthide(<%=rsPAGE("pageid")%>)" class="sort">排序</a><div class="sortmenu" id="sortmenu<%=rsPAGE("pageid")%>"><a href="javascript:void(null)" onclick="sortmenu(<%=rsPAGE("pageid")%>,'top')">顶部↑↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsPAGE("pageid")%>,'up')">向上↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsPAGE("pageid")%>,'down')">向下↓</a><a href="javascript:void(null)" onclick="sortmenu(<%=rsPAGE("pageid")%>,'bottom')">底部↓↓</a></div></div></div><%
rsPAGE.close()
Set rsPAGE = nothing
%>