<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="form_config.asp"-->
<%
function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function									

' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Delete Record: declare variables

if (CStr(Request("MM_delete")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "formAdmin.asp"

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If
%>
<%
' *** Delete Record: construct a sql delete statement and execute it

If (CStr(Request("MM_delete")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the delete
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
set rsguestbookLIST = Server.CreateObject("ADODB.Recordset")
rsguestbookLIST.ActiveConnection = MM_conn_STRING
if request.querystring("id") <> "" then
rsguestbookLIST__MMColParam = request.querystring("id")
rsguestbookLIST.Source = "SELECT * FROM guestbook WHERE cate_id = " + Replace(rsguestbookLIST__MMColParam, "'", "''") + " ORDER BY data DESC"
else
rsguestbookLIST.Source = "SELECT * FROM guestbook ORDER BY data DESC"
end if
rsguestbookLIST.CursorType = 0
rsguestbookLIST.CursorLocation = 2
rsguestbookLIST.LockType = 3
rsguestbookLIST.Open()
rsguestbookLIST_numRows = 0
%>
<%
Dim Repeat1__numRows
Repeat1__numRows = 5
Dim Repeat1__index
Repeat1__index = 0
rsguestbookLIST_numRows = rsguestbookLIST_numRows + Repeat1__numRows
%>
<%'分页处理
If Not rsguestbookLIST.EOF Or Not rsguestbookLIST.BOF Then
'  *** Recordset Stats, Move To Record, and Go To Record: declare stats variables

' set the record count
rsguestbookLIST_total = rsguestbookLIST.RecordCount

' set the number of rows displayed on this page
If (rsguestbookLIST_numRows < 0) Then
  rsguestbookLIST_numRows = rsguestbookLIST_total
Elseif (rsguestbookLIST_numRows = 0) Then
  rsguestbookLIST_numRows = 1
End If

' set the first and last displayed record
rsguestbookLIST_first = 1
rsguestbookLIST_last  = rsguestbookLIST_first + rsguestbookLIST_numRows - 1

' if we have the correct record count, check the other stats
If (rsguestbookLIST_total <> -1) Then
  If (rsguestbookLIST_first > rsguestbookLIST_total) Then rsguestbookLIST_first = rsguestbookLIST_total
  If (rsguestbookLIST_last > rsguestbookLIST_total) Then rsguestbookLIST_last = rsguestbookLIST_total
  If (rsguestbookLIST_numRows > rsguestbookLIST_total) Then rsguestbookLIST_numRows = rsguestbookLIST_total
End If
%>
<%
' *** Recordset Stats: if we don't know the record count, manually count them

If (rsguestbookLIST_total = -1) Then

  ' count the total records by iterating through the recordset
  rsguestbookLIST_total=0
  While (Not rsguestbookLIST.EOF)
    rsguestbookLIST_total = rsguestbookLIST_total + 1
    rsguestbookLIST.MoveNext
  Wend

  ' reset the cursor to the beginning
  If (rsguestbookLIST.CursorType > 0) Then
    rsguestbookLIST.MoveFirst
  Else
    rsguestbookLIST.Requery
  End If

  ' set the number of rows displayed on this page
  If (rsguestbookLIST_numRows < 0 Or rsguestbookLIST_numRows > rsguestbookLIST_total) Then
    rsguestbookLIST_numRows = rsguestbookLIST_total
  End If

  ' set the first and last displayed record
  rsguestbookLIST_first = 1
  rsguestbookLIST_last = rsguestbookLIST_first + rsguestbookLIST_numRows - 1
  If (rsguestbookLIST_first > rsguestbookLIST_total) Then rsguestbookLIST_first = rsguestbookLIST_total
  If (rsguestbookLIST_last > rsguestbookLIST_total) Then rsguestbookLIST_last = rsguestbookLIST_total

End If
%>
<%
' *** Move To Record and Go To Record: declare variables

Set MM_rs    = rsguestbookLIST
MM_rsCount   = rsguestbookLIST_total
MM_size      = rsguestbookLIST_numRows
MM_uniqueCol = ""
MM_paramName = ""
MM_offset = 0
MM_atTotal = false
MM_paramIsDefined = false
If (MM_paramName <> "") Then
  MM_paramIsDefined = (Request.QueryString(MM_paramName) <> "")
End If
%>
<%
' *** Move To Record: handle 'index' or 'offset' parameter

if (Not MM_paramIsDefined And MM_rsCount <> 0) then

  ' use index parameter if defined, otherwise use offset parameter
  r = Request.QueryString("index")
  If r = "" Then r = Request.QueryString("offset")
  If r <> "" Then MM_offset = Int(r)

  ' if we have a record count, check if we are past the end of the recordset
  If (MM_rsCount <> -1) Then
    If (MM_offset >= MM_rsCount Or MM_offset = -1) Then  ' past end or move last
      If ((MM_rsCount Mod MM_size) > 0) Then         ' last page not a full repeat region
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' move the cursor to the selected record
  i = 0
  While ((Not MM_rs.EOF) And (i < MM_offset Or MM_offset = -1))
    MM_rs.MoveNext
    i = i + 1
  Wend
  If (MM_rs.EOF) Then MM_offset = i  ' set MM_offset to the last possible record

End If
%>
<%
' *** Move To Record: if we dont know the record count, check the display range

If (MM_rsCount = -1) Then

  ' walk to the end of the display range for this page
  i = MM_offset
  While (Not MM_rs.EOF And (MM_size < 0 Or i < MM_offset + MM_size))
    MM_rs.MoveNext
    i = i + 1
  Wend

  ' if we walked off the end of the recordset, set MM_rsCount and MM_size
  If (MM_rs.EOF) Then
    MM_rsCount = i
    If (MM_size < 0 Or MM_size > MM_rsCount) Then MM_size = MM_rsCount
  End If

  ' if we walked off the end, set the offset based on page size
  If (MM_rs.EOF And Not MM_paramIsDefined) Then
    If (MM_offset > MM_rsCount - MM_size Or MM_offset = -1) Then
      If ((MM_rsCount Mod MM_size) > 0) Then
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' reset the cursor to the beginning
  If (MM_rs.CursorType > 0) Then
    MM_rs.MoveFirst
  Else
    MM_rs.Requery
  End If

  ' move the cursor to the selected record
  i = 0
  While (Not MM_rs.EOF And i < MM_offset)
    MM_rs.MoveNext
    i = i + 1
  Wend
End If
%>
<%
' *** Move To Record: update recordset stats

' set the first and last displayed record
rsguestbookLIST_first = MM_offset + 1
rsguestbookLIST_last  = MM_offset + MM_size
If (MM_rsCount <> -1) Then
  If (rsguestbookLIST_first > MM_rsCount) Then rsguestbookLIST_first = MM_rsCount
  If (rsguestbookLIST_last > MM_rsCount) Then rsguestbookLIST_last = MM_rsCount
End If

' set the boolean used by hide region to check if we are on the last record
MM_atTotal = (MM_rsCount <> -1 And MM_offset + MM_size >= MM_rsCount)
%>
<%
' *** Go To Record and Move To Record: create strings for maintaining URL and Form parameters

' create the list of parameters which should not be maintained
MM_removeList = "&index="
If (MM_paramName <> "") Then MM_removeList = MM_removeList & "&" & MM_paramName & "="
MM_keepURL="":MM_keepForm="":MM_keepBoth="":MM_keepNone=""

' add the URL parameters to the MM_keepURL string
For Each Item In Request.QueryString
  NextItem = "&" & Item & "="
  If (InStr(1,MM_removeList,NextItem,1) = 0) Then
    MM_keepURL = MM_keepURL & NextItem & Server.URLencode(Request.QueryString(Item))
  End If
Next

' add the Form variables to the MM_keepForm string
For Each Item In Request.Form
  NextItem = "&" & Item & "="
  If (InStr(1,MM_removeList,NextItem,1) = 0) Then
    MM_keepForm = MM_keepForm & NextItem & Server.URLencode(Request.Form(Item))
  End If
Next

' create the Form + URL string and remove the intial '&' from each of the strings
MM_keepBoth = MM_keepURL & MM_keepForm
if (MM_keepBoth <> "") Then MM_keepBoth = Right(MM_keepBoth, Len(MM_keepBoth) - 1)
if (MM_keepURL <> "")  Then MM_keepURL  = Right(MM_keepURL, Len(MM_keepURL) - 1)
if (MM_keepForm <> "") Then MM_keepForm = Right(MM_keepForm, Len(MM_keepForm) - 1)

' a utility function used for adding additional parameters to these strings
Function MM_joinChar(firstItem)
  If (firstItem <> "") Then
    MM_joinChar = "&"
  Else
    MM_joinChar = ""
  End If
End Function
%>
<%
' *** Move To Record: set the strings for the first, last, next, and previous links

MM_keepMove = MM_keepBoth
MM_moveParam = "index"

' if the page has a repeated region, remove 'offset' from the maintained parameters
If (MM_size > 0) Then
  MM_moveParam = "offset"
  If (MM_keepMove <> "") Then
    params = Split(MM_keepMove, "&")
    MM_keepMove = ""
    For i = 0 To UBound(params)
      nextItem = Left(params(i), InStr(params(i),"=") - 1)
      If (StrComp(nextItem,MM_moveParam,1) <> 0) Then
        MM_keepMove = MM_keepMove & "&" & params(i)
      End If
    Next
    If (MM_keepMove <> "") Then
      MM_keepMove = Right(MM_keepMove, Len(MM_keepMove) - 1)
    End If
  End If
End If

' set the strings for the move to links
If (MM_keepMove <> "") Then MM_keepMove = MM_keepMove & "&"
urlStr = Request.ServerVariables("URL") & "?" & MM_keepMove & MM_moveParam & "="
MM_moveFirst = urlStr & "0"
MM_moveLast  = urlStr & "-1"
MM_moveNext  = urlStr & Cstr(MM_offset + MM_size)
prev = MM_offset - MM_size
If (prev < 0) Then prev = 0
MM_movePrev  = urlStr & Cstr(prev)
end if '分页处理
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
	  <h1 class="b_ch1">在线服务记录</h1>
<p class="b_tip">说明：访问者通过前台在线服务页面提交后，信息自动发送到各服务项目负责人信箱。<br>　　　同时提交的信息进入服务数据库备查。选择不同项目查看不同类型的服务记录。</p>
	  <script language="javascript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}
//-->
</script>
<%'服务记录分类
Dim rsGUESTCATE
Dim rsGUESTCATE_numRows

Set rsGUESTCATE = Server.CreateObject("ADODB.Recordset")
rsGUESTCATE.ActiveConnection = MM_conn_STRING
rsGUESTCATE.Source = "SELECT * FROM guestbook_cate ORDER BY id ASC"
rsGUESTCATE.CursorType = 0
rsGUESTCATE.CursorLocation = 2
rsGUESTCATE.LockType = 1
rsGUESTCATE.Open()

rsGUESTCATE_numRows = 0
%>
<%
Dim RepeatGuestCate__numRows
Dim RepeatGuestCate__index

RepeatGuestCate__numRows = -1
RepeatGuestCate__index = 0
rsGUESTCATE_numRows = rsGUESTCATE_numRows + RepeatGuestCate__numRows
%>
<form name="form1">
  <select name="menu1" onchange="MM_jumpMenu('parent',this,0);">
<option value="<%=(CStr(Request("URL")))%>">所有项目</option>
<% 
While ((RepeatGuestCate__numRows <> 0) AND (NOT rsGUESTCATE.EOF)) 
%>
    <option value="<%=(CStr(Request("URL")))%>?id=<%=(rsGUESTCATE.Fields.Item("id").Value)%>" <%if cstr(rsGUESTCATE.Fields.Item("id").Value)= request.querystring("id") then response.write("selected style='color:#ff0000'")%>><%= HTMLEncode((rsGUESTCATE.Fields.Item("name").Value)) %></option>
<% 
  RepeatGuestCate__index=RepeatGuestCate__index+1
  RepeatGuestCate__numRows=RepeatGuestCate__numRows-1
  rsGUESTCATE.MoveNext()
Wend
%>
  </select>
</form>
<%
rsGUESTCATE.Close()
Set rsGUESTCATE = Nothing
%>
	  <% If Not rsguestbookLIST.EOF Or Not rsguestbookLIST.BOF Then %>
	  <table width="640" border="1" cellspacing="0" cellpadding="4" bordercolor="#188ee7" align=left>
	  <tr align=center bgcolor="<%=(back_menubackcolor)%>"><td class=p14 width=500>内容</td><td class=p14>联系信息</td><td class=p14>删除</td>
	  </tr>
		<% 
While ((Repeat1__numRows <> 0) AND (NOT rsguestbookLIST.EOF)) 
%>
		<form name="form1" method="POST" action="<%=MM_editAction%>">
		  <input type="hidden" name="MM_recordId" value="<%= rsguestbookLIST.Fields.Item("id").Value %>">
		  <tr> 
			<td class=p14 valign=top><b><%=(HTMLEncode(rsguestbookLIST.Fields.Item("subject").Value))%></b>
			<br><table>
			<%=(rsguestbookLIST.Fields.Item("html").value)%></table>
			<font class=p13><%=replace(Replace(HTMLEncode(rsguestbookLIST.Fields.Item("content").Value),vbCrLF,"<br>"),"  ","　")%></font>
			</td>
			<td class=p12 valign=top nowrap>时　间：<font class=p13><%= DoDateTime((rsguestbookLIST.Fields.Item("data").Value), 2, -1) %>&nbsp; <%= DoDateTime((rsguestbookLIST.Fields.Item("data").Value), 3, 2057) %></font>
			  <br>联系人：<%
	dim strNAME
	strNAME = rsguestbookLIST.Fields.Item("name").Value%>
	<%=(strNAME)%>
	<br>称　呼：<%=(rsguestbookLIST.Fields.Item("chenghu").Value)%>
			  <%
	dim strJOB
	strJOB = rsguestbookLIST.Fields.Item("co").Value
	if trim(strJOB) <> "" then
	response.write("<br>单　位：" & strJOB)
	end if
	%>
<br>电　话：<%=rsguestbookLIST.Fields.Item("tel").Value%>
<% if trim(rsguestbookLIST.Fields.Item("fax").Value) <> "" then response.write("<br>传　真：" & HTMLEncode(rsguestbookLIST.Fields.Item("fax").Value))%>
<br>信　箱：<%
	dim strEMAIL
	strEMAIL = rsguestbookLIST.Fields.Item("email").Value
	if trim(strEMAIL) <> "" then
	response.write "<a title='发信给这位联系人' href='mailto:" & strEMAIL &"'>" & strEMAIL & "</a>"
	else
	response.write "&nbsp;"
	end if
	%>
<%if trim(rsguestbookLIST.Fields.Item("url").Value) <> "" and trim(rsguestbookLIST.Fields.Item("url").Value) <> "http://" then
	strURL = trim(rsguestbookLIST.Fields.Item("url").Value) %>
	<br>网　址：<a href="<%=(strURL)%>"><%=(strURL)%></a>
	<%end if%>
			</td>
			<td align=center class=p14> 
			  <input name="submit" type="submit" value="删除" onClick="GP_popupConfirmMsg('确定删除这条记录？');return document.MM_returnValue">
			  <input type="hidden" name="MM_delete" value="true">
			</td>
		  </tr>
		</form>
		<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsguestbookLIST.MoveNext()
Wend
%>
<tr><td colspan=3 align=right>
	  <% if rsguestbookLIST_total > MM_size then
For i = 1 to rsguestbookLIST_total Step MM_size
TM_counter = TM_counter + 1
TM_PageEndCount = i + MM_size - 1
if TM_PageEndCount > rsguestbookLIST_total Then TM_PageEndCount = rsguestbookLIST_total
if i <> MM_offset + 1 then
Response.Write("<a class=p12 href=" & Request.ServerVariables("URL") & "?" & MM_keepMove & "offset=" & i-1 & ">")
Response.Write("第" & TM_counter & "页</a>")
else
Response.Write("<b><font class=p13>第" & TM_counter & "页</font></b>")
End if
if(TM_PageEndCount <> rsguestbookLIST_total) then Response.Write(" | ")
next
end if
 %>
</td></tr>
	  </table>
	  <br>
	  <%else%>
	  <h3>没有记录</h3>
	  <% End If ' end Not rsguestbookLIST.EOF Or NOT rsguestbookLIST.BOF %>
<!--#include file ="_bottom.asp"-->
<%
rsguestbookLIST.Close()
%>
