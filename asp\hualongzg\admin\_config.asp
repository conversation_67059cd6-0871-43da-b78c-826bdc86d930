<!--#include file="ScriptLib/fun.asp"  -->
<%
'v7.1 最早用于hn-power.com,前后台都改成UTF-8，此数据库用于多种语言版本

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'※　　　　　　　　　　　后台设置　　　　　　　　　　　　　　　※
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'数据库里的表导入 area list page jobs jobs_dept rels 整理 contact other guestbook_cate guestbook usergroup event_type(用blank.mdb则不必整理)
'自5.1起要复制的文件夹 admin Connections icon js ScriptLibrary
'要建立的文件夹 images upload
'要复制的文件 根目录下前面为 _的文件

'要修改的设置文件包包括：
'根目录下的 _config.asp Connections/conn.asp
'admin目录下的
'_config.asp 
'stat__config.asp
'htmlbuilder\_config.asp

'（后台界面）
'一个水印标志 logo.gif 、后台登录界面图片放在 images 目录下、后台界面图片放在 vi下
'后台登录界面 default.asp 中的css样式
'interface\style.css admin.js
'release.asp 中的css 样式 （未发布界面）

'在管理系统中添加语言
'后台菜单
'area_config.asp 设置


dim nav, subnav, nav_max_i, back_titlename, back_copyright

'◇客户名称,网址（不要加www）,后台版本
'---------------------------------------------------------------------------------
Const back_coname = "华隆机械", back_site = "hualongzg.com", back_ver = "7.1" '最早用于smjdsc.com
hb_css = "blue"	'html编辑器界面风格 standard full light blue green red yellow 3d coolblue mini popup

'◇菜单设置，如果没有链接，则用#代替
'---------------------------------------------------------------------------------
nav = "1,内容管理,page.asp|4,在线服务,formAdmin.asp|2,招聘系统,jobs_human.asp|5,流量统计,stat_default.asp|6,系统管理,area.asp|7,退出系统,logout.asp"

nav_max_i = 8

ReDim subnav(nav_max_i) '此行位置不能改

subnav(0) = "<ul><li><a href=""top_marquee.asp"">顶部滚动条</a></li><li><a href=""top_news.asp"">图片新闻</a></li></ul>"
subnav(1)="<ul><li><a href=""page.asp?class=1"">页面管理</a></li><li><a href=""contact.asp"">联系信息</a></li><li><a href=""flash.asp"">首页标题</a></li></ul>"
'subnav(2) = "<ul><li><a href=""links2.asp"">链接列表</a></li><li><a href=""links3.asp"">删除链接</a></li><li><a href=""links.asp"">链接规则</a></li></ul>"
subnav(2) = "<ul><li><a href=""jobs_human.asp"">简历列表</a></li><li><a href=""jobs_interview.asp"">面试列表</a></li><li><a href=""jobs_blacklist.asp"">黑名单</a></li><li><a href=""jobs_good.asp"">人才库</a></li><li><a href=""jobs_cancel.asp"">已退信</a></li><li><a href=""jobs_position.asp"">招聘职位</a></li><li><a href=""jobs_event_config.asp"">招聘流程</a></li><li><a href=""jobs_dept.asp"">部门设置</a></li><li><a href=""jobs_help.asp"">招聘系统帮助</a></li></ul>"
subnav(4) = "<ul><li><a href=""formAdmin.asp"">服务记录</a></li><li><a href=""formAdminConfig.asp"">服务项目设置</a></li></ul>"
subnav(6) = "<ul><li><a href=""area.asp"">区域与列表管理</a></li><li><a href=""permissionUser.asp"">用户管理</a></li><li><a href=""release.asp"">系统发布管理</a></li></ul>"
subnav(8) ="<ul><li><a href=""lesson.asp"">交互教程</a></li><li><a href=""supportonline.asp"">森捷在线支持</a></li></ul>"

'◇菜单权限设置
'---------------------------------------------------------------------------------
If session("isadmin")=true Then
	right_str = "0,1,2,3,4,5,6,7,8"
Else
	right_str = "4,2,5,7"
End if

'◇招聘系统设置
'---------------------------------------------------------------------------------
jobs_system_school = true	'是否打开校园招聘设置，如果打开的话，则招聘职位分校园招聘和社会招聘 从长泰起，jobs表里加了一个j_type字段

'以下如没有必要不用改
back_titlename = ""				'标题文字，如不设定，为公司名称+网站管理系统
back_copyright = ""				'页面底部声明，缺省为版本　<a href="http://www.soundjet.net">森捷信息技术公司版权所有</a>
back_siteurl = ""				'网站url，缺省为www. + back_site
back_adminmail = "<EMAIL>"				'我公司的系统维护信箱
back_smtpsever = "202.103.69.204"					'发信服务器

'以下一般不用改
if trim(back_titlename) = "" then back_titlename = back_coname & "平台系统 - 管理"
if trim(back_copyright) = "" then back_copyright = "<span>&copy;</span> 森捷"
if trim(back_siteurl) ="" then back_siteurl = "www." & back_site
%>