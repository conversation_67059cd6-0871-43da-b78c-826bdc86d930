<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<%
start_catelcode = "0206"
set rs=server.createobject("adodb.recordset")
sql="select page.pageid,page.title,list.name,list.id,list.cate_id,area.cate_name,area.cate_lcode,page.top_submit from (list inner join area on list.cate_id = area.cate_id) inner join page on page.subid=list.id where left(cate_lcode,len('" & start_catelcode & "')) = '" & start_catelcode & "' order by page.sort_id desc"
rs.open sql,MM_conn_STRING,1,1
while Not rs.eof
If rs("top_submit") = True Then
	checkedstr= " checked=""checked"""
Else
	checkedstr= ""
End if
%>
<li><div><input type="checkbox" name="id" value="<%=rs("pageid")%>"<%=checkedstr%> /> <%=rs("title")%></div></li>
<%rs.movenext()
wend
%>