<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
If isGoogleAnalytics = True Then
	response.redirect("stat_googleAnalytics.asp")
End If

set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath

'所有总访问数、开始访问日期（从主数据库读取）
    tmprs=conn.execute("Select vtop,starttime from vjian")
	vtotal=tmprs("vtop")
    vfirst=tmprs("starttime")
	set tmprs=nothing
	if isnull(vtotal) then vtotal=0

if vtotal=0 then
	conn.Close
	set conn=nothing
	Response.Redirect "stat_help.asp?error=统计系统还未启用，还没有统计报告。"
end if


'在线人数
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql="select vip from view where vtime >= dateadd('n',-20,now()) group by vip"
	rs.Open sql,conn,1,1
	vonline=rs.RecordCount
	rs.Close
	set rs=nothing
'最近一、二天访问量
    tmprs=conn.execute("Select today,yesterday from vjian")
    vtoday=tmprs("today")
    vyesterday=tmprs("yesterday")
	if isnull(vtoday) then vtoday=0
	if isnull(vyesterday) then vyesterday=0
'今天是否已有人访问
    tmprs=conn.execute("Select Top 1 vtime from view ORDER BY id DESC")
	today_view = DatePart("y",cdate(tmprs("vtime")))
	today_view = DatePart("y",cdate(now())) - today_view
'今年访问量
    tmprs=conn.execute("Select count(id) as vthisyear from view where vyear=" & year(now))
    vthisyear=tmprs("vthisyear")
	if isnull(vthisyear) then vthisyear=0
'本月访问量
    tmprs=conn.execute("Select count(id) as vthismonth from view where vmonth=" & month(now) & " and vyear=" & year(now))
    vthismonth=tmprs("vthismonth")
	if isnull(vthismonth) then vthismonth=0
'访问天数、平均每天访问量
	vdays=now()-vfirst
	vdayavg=vtotal/vdays
	vdays=int((vdays*10^mPrecision)+0.5)/(10^mPrecision)
	if vdays<1 then vdays="0" & vdays
	vdayavg=int((vdayavg*10^mPrecision)+0.5)/(10^mPrecision)
'预计今日访问量
	vdaylong=now()-date()
	vguess=int(((vtoday/vdaylong)+vyesterday)/2+0.5)
	if vguess< vtoday then vguess=int((vtoday/vdaylong)+0.5)
'当前用户访问量
	vuser=cint(Request.Cookies(mNameEn)("yold"))

conn.Close
set conn=nothing
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>总述</b></font></td></tr>
</table><br>
<table width="540" cellspacing="0" align="center" cellpadding="4" border="0" align=center>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>><br>总访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><br><%=vtotal+old_count%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>使用本系统后: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vtotal%></td>
</tr><%if today_view<=0 then'如果今天有访问量%>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>今日访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vtoday%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>昨日访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vyesterday%></td>
</tr><%else%>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>今日访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14>尚未有人访问</td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>最近二天访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vtoday%>/<%=vyesterday%></td>
</tr><%end if%>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>在线人数: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vonline%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>预计今日: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vguess%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>><br>开始统计时间: </td>
<td colspan=1 align=left width=70% valign=top class=p14><br><%=vfirst%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>统计天数: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vdays%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>今年访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vthisyear%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>本月访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vthismonth%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>>平均日访量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><%=vdayavg%></td>
</tr>
<tr height="20">
<td colspan=3 align=right width=30% valign=top class=p14 bgcolor=<%=(stat_style_bg)%>><br>您的访问量: </td>
<td colspan=1 align=left width=70% valign=top class=p14><br><%=vuser%></td>
</tr>
</table>
<!--#include file="stat__bottom.asp"-->