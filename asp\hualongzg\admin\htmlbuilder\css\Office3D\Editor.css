.yToolbar
{
}
TABLE.Toolbar
{
	BORDER-RIGHT: #F8FCF8 1px solid;
}
TABLE.Toolbar TD
{
	BACKGROUND-COLOR: #D0D0C8;
	BORDER-BOTTOM: #808080	1px solid;
	BORDER-RIGHT: #808080 1px solid;
	BORDER-TOP:	#F8FCF8	1px solid;
	HEIGHT: 27px;
	LEFT: 0px;
	POSITION: relative;
	TOP: 0px;
}
.Btn
{
	BACKGROUND-COLOR: #D0D0C8;
	border:1px outset;
	HEIGHT: 21px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH: 21px;
	filter:gray;
}
.TBSep
{
	BORDER-LEFT: #808080 1px solid;
	BORDER-RIGHT: #F8FCF8 1px solid;
	FONT-SIZE: 0px;
	HEIGHT: 20px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH:1px
}
.TBGen
{
	FONT: 8pt arial,sans-serif;
	HEIGHT: 20px;
	POSITION: absolute;
	TOP: 2px
}
.TBHandle
{
	BACKGROUND-COLOR: #D0D0C8;
	BORDER-LEFT: #F8FCF8 1px solid;
	BORDER-RIGHT: #808080 1px solid;
	BORDER-TOP:	#F8FCF8	1px solid;
	FONT-SIZE: 1px;
	HEIGHT: 20px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH: 3px
}
.Ico
{
	HEIGHT: 20px;
	LEFT: 0px;
	POSITION: absolute;
	TOP: 0px;
	WIDTH: 20px
}
.BtnMouseOverUp
{
	BACKGROUND-COLOR: #B5BED6;
	BORDER: #08246B	1px solid;
	HEIGHT: 21px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH: 21px
}
.BtnMouseOverDown
{
	BACKGROUND-COLOR: #EEEEEE;
	BORDER: 1px inset;
	HEIGHT: 21px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH: 21px
}
.BtnDown
{
	BACKGROUND-COLOR: #EEEEEE;
	BORDER: 1px inset;
	HEIGHT: 21px;
	POSITION: absolute;
	TOP: 1px;
	WIDTH: 21px
}
.IcoDown
{
	HEIGHT: 21px;
	LEFT: 0px;
	POSITION: absolute;
	TOP: 0px;
	WIDTH: 21px
}
.IcoDownPressed
{
	LEFT: 1px;
	POSITION: absolute;
	TOP: 1px
}

BODY
{
	BACKGROUND-COLOR:#FFFFFF;
	MARGIN: 0px;
	PADDING: 0px;
}
SELECT
{
    BACKGROUND: #eeeeee;
    FONT: 8pt verdana,arial,sans-serif
}
TABLE
{
    POSITION: relative
}
.Composition
{
    BACKGROUND-COLOR: #cccccc;
    POSITION: relative
}



.ContextMenuDiv {	border-top:buttonface 1px solid;border-left:buttonface 1px solid;border-bottom:windowframe 1px solid;border-right:windowframe 1px solid;}
.ContextMenuTable {	border-top:window 1px solid;border-left:window 1px solid;border-bottom:buttonshadow 1px solid;border-right:buttonshadow 1px solid;}
.ContextMenuMouseOver {background-color:highlight;color:highlighttext;font-size: 12px;cursor:default;font-size: 12px;}
.ContextMenuMouseOut {background-color:buttonface;color:buttontext;font-size: 12px;cursor:default;font-size: 12px;}
.ContextMenuLeftBg {background-color:#0072BC}



TABLE.StatusBar
{
	BORDER-RIGHT: #808080 1px solid;
	BORDER-BOTTOM: #808080	1px solid;
	BACKGROUND-COLOR: #D0D0C8;
}

TD.StatusBarBtnOff {padding:1px 5px;border:1px outset;cursor:pointer;}
TD.StatusBarBtnOn {padding:1px 5px;border:1px inset;background-color: #EEEEEE;}