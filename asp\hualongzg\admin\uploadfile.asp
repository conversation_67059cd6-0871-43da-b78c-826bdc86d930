<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%response.Charset = "utf-8"%>
<!--#include file="../Connections/conn.asp" -->
<!--#include file = "area_config.asp"-->
<!--#include file="ScriptLib/_upfile_class.asp"-->
<%If (Session("isadmin") <> true AND session("ismember")=false) Then 
	Call OutScript("alert('登录过期，或者没有权限');")
	Call OutScript("this.close();")
	response.end
End if
Server.ScriptTimeOut = 1800
' 参数变量
Dim sType
Dim sImgArgu,sImgwartmark,sImgArgutemp,originalfile,originalfile1

' 设置变量
Dim sAllowExt, nAllowSize, sUploadDir, nUploadObject, nAutoDir, sBaseUrl, sContentPath
' 接口变量
Dim sFileExt, sOriginalFileName, sSaveFileName, sPathFileName, nFileNum
' 上传图形是否打水印
Dim image_watermark_str

Call InitUpload()		' 初始化上传变量

Function Get_SafeStr(str)
	Get_SafeStr = Replace(Replace(Replace(Trim(str), "'", ""), Chr(34), ""), ";", "")
End Function

Dim sAction
sAction = UCase(Trim(Request.QueryString("action")))

Select Case sAction
Case "REMOTE"
	Call DoRemote()			' 远程自动获取
Case "SAVE"
	Call ShowForm()			' 显示上传表单
	Call DoSave()			' 存文件
Case Else
	Call ShowForm()			' 显示上传表单
End Select


Sub ShowForm() 
%>
<HTML>
<HEAD>
<TITLE>文件上传</TITLE>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style type="text/css">
body, a, table, div, span, td, th, input, select{font:9pt;font-family: "宋体", Verdana, Arial, Helvetica, sans-serif;}
body {padding:0px;margin:0px}
</style>

<script language="JavaScript" src="interface/dialog.js"></script>

</head>
<body bgcolor=menu>
<table width=100% cellpadding=0 cellspacing=0 border=0>
<form action="?action=save&type=<%=sType%>" method=post name="myform" enctype="multipart/form-data"><tr><%if watermark_on = true AND request.querystring("type") = "image" then%><td width=50>水印<input type="checkbox" checked="checked" name="image_watermark"></td><%end if%><td><input type=file name=uploadfile size=1 style="width:100%" onchange="originalfile.value=this.value"></td>
<input type=hidden name=originalfile value=""><input type=hidden name=originalfile1 value=""><input type="hidden" name="sImgArgu" /></tr>
</form>
</table>

<script language=javascript>
var sAllowExt = "<%=sAllowExt%>";
// 检测上传表单
function CheckUploadForm() {
	if (!IsExt(document.myform.uploadfile.value,sAllowExt)){
		parent.UploadError("提示：\n\n请选择一个有效的文件，\n支持的格式有（"+sAllowExt+"）！");
		return false;
	}
	return true
}

// 提交事件加入检测表单
var oForm = document.myform ;
oForm.attachEvent("onsubmit", CheckUploadForm) ;
if (! oForm.submitUpload) oForm.submitUpload = new Array() ;
oForm.submitUpload[oForm.submitUpload.length] = CheckUploadForm ;
if (! oForm.originalSubmit) {
	oForm.originalSubmit = oForm.submit ;
	oForm.submit = function() {
		if (this.submitUpload) {
			for (var i = 0 ; i < this.submitUpload.length ; i++) {
				this.submitUpload[i]() ;
			}
		}
		this.originalSubmit() ;
	}
}

// 上传表单已装入完成
try {
	parent.UploadLoaded();
}
catch(e){
}

</script>

</body>
</html>
<% 
End Sub 

' 保存操作	
Sub DoSave()

	' 默认无组件上传类
	Call DoUpload_Class
	'大图的水印
	if (watermark_on = true) AND (image_watermark_str = "on") Then sImgwartmark = True
	sImgArgu = Split(sImgArgutemp,",")

	If sImgArgu(0) = "mod" Then
		Call img_thumb_pro("..\upload",sSaveFileName,sImgArgu(7) & "," & sImgArgu(8) & "," & sImgArgu(9) & "," & CStr(sImgwartmark) & ",,false," & sImgArgu(4) & "," & sImgArgu(5) & "," & sImgArgu(6) & ",false,,false","update")	
		If sImgArgu(2) = "list" then
			sql = "update " &  sImgArgu(2) & " set " & table_sql_pro(0) & " where id=" & sImgArgu(3)
		Else
			sql = "update " &  sImgArgu(2) & " set " & table_sql_pro(0) & " where pageid=" & sImgArgu(3)
		End If
		sql_command(sql)
	ElseIf sImgArgu(0) = "add" Then
		Call img_thumb_pro("..\upload",sSaveFileName,sImgArgu(6) & "," & sImgArgu(7) & "," & sImgArgu(8) & "," & CStr(sImgwartmark) & ",,false," & sImgArgu(3) & "," & sImgArgu(4) & "," & sImgArgu(5) & ",false,,false","update")	
	End If

	If originalfile <> "" Then delfile(originalfile)
	If originalfile1 <> "" Then delfile(originalfile1)

	Call OutScript("parent.imgRestore('" & sSaveFileName & "','" & sImgArgu(0) & "');")	

End Sub

' 无组上传类
Sub DoUpload_Class()
	On Error Resume Next
	Dim oUpload, oFile
	' 建立上传对象
	Set oUpload = New upfile_class
	' 取得上传数据,限制最大上传
	oUpload.GetData(nAllowSize*1024)

	If oUpload.Err > 0 Then
		Select Case oUpload.Err
		Case 1
			Call OutScript("parent.UploadError('请选择有效的上传文件！')")
		Case 2
			Call OutScript("parent.UploadError('您上传的文件总大小超出了最大限制（" & nAllowSize & "KB）！')")
		End Select
		Response.End
	End If

	Set oFile = oUpload.File("uploadfile")
	sFileExt = LCase(oFile.FileExt)
	Call CheckValidExt(sFileExt)
	sOriginalFileName = oFile.FileName
	sSaveFileName = GetRndFileName(sFileExt)
	oFile.SaveToFile Server.Mappath(sUploadDir & sSaveFileName)

	image_watermark_str = oUpload.Form("image_watermark")
	sImgArgutemp = oUpload.form("sImgArgu")
	originalfile = oUpload.form("originalfile")
	originalfile1 = oUpload.form("originalfile1")
	Set oFile = Nothing
	Set oUpload = Nothing

End Sub

' 取随机文件名
Function GetRndFileName(sExt)
	Dim sRnd
	Randomize
	sRnd = Int(900 * Rnd) + 100
	GetRndFileName = year(now) & month(now) & day(now) & hour(now) & minute(now) & second(now) & sRnd & "." & sExt
End Function

' 输出客户端脚本
Sub OutScript(str)
	Response.Write "<script language=javascript>" & str & ";history.back()</script>"
End Sub
Sub OutScriptNoBack(str)
	Response.Write "<script language=javascript>" & str & "</script>"
End Sub


' 检测扩展名的有效性, 此段代码有问题，在windows 2003下有200K限制
Sub CheckValidExt(sExt)
	Dim b, i, aExt
	b = False
	aExt = Split(sAllowExt, "|")
	For i = 0 To UBound(aExt)
		If LCase(aExt(i)) = sExt Then
			b = True
			Exit For
		End If
	Next
	If b = False Then
		OutScript("parent.UploadError('出错：\n\n您选择的文件格式是" & sExt & "\n请选择一个有效的文件，\n支持的格式有（"+sAllowExt+"）！')")
		Response.End
	End If
End Sub


' 初始化上传限制数据
Sub InitUpload()
	sImgwartmark = False
	sType = Trim(Request.QueryString("type"))
		sBaseUrl = 1
		nUploadObject = 0
		nAutoDir = 0
		sUploadDir = "..\upload\"
		Select Case sBaseUrl
		Case "1"
			sContentPath = RelativePath2RootPath(sUploadDir)
		Case "2"
			sContentPath = RootPath2DomainPath(RelativePath2RootPath(sUploadDir))
		End Select
		sAllowExt = "gif|png|jpg|jpeg|bmp"
		nAllowSize = "3072"
	sAllowExt = Replace(UCase(sAllowExt), "ASP", "")
End Sub

'***********************************************************************************
'因为标志较大，太小的图没有必要打水印，除非另作一水印
'position: 0为中 1,2,3,4依次为左上、右上，右下，左下
'opacity: 不透明度 0-1
'transparence_color: 透明色，为16进制值，格式为 &HFFFFFF

Sub watermark(image,logoimage,position,opacity,transparence_color)

dim Img,Logo,ImgPath,LogoPath
Set Img = Server.CreateObject("Persits.Jpeg") 
Img.PreserveMetadata = True
Set Logo = Server.CreateObject("Persits.Jpeg") 
'Call OutScript("parent.UploadError('指示: " & sUploadDir & " | "  & sSaveFileName & "')")

ImgPath = Server.MapPath("..\upload") & "\" & image 
Img.Open ImgPath 

LogoPath = Server.MapPath("..\images") & "\" & logoimage
Logo.Open LogoPath 

'大小适应，即确定logo.width,logo.height的值
if  Logo.OriginalWidth / Logo.OriginalHeight >= Img.OriginalWidth / Img.OriginalHeight then
	if Logo.OriginalWidth > Img.OriginalWidth then
		Logo.Width = Img.OriginalWidth
		Logo.Height = Img.OriginalWidth / Logo.OriginalWidth  * Logo.OriginalHeight
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
else
	if Logo.OriginalHeight > Img.OriginalHeight then
		Logo.Height = Img.OriginalHeight
		Logo.Width = Img.OriginalHeight / Logo.OriginalHeight  * Logo.OriginalWidth
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
end if

'写位置
dim Logopadding_left,logopadding_height
select case position
case 0
Logopadding_left = cint((Img.OriginalWidth - Logo.Width)/2)
logopadding_height = cint((Img.OriginalHeight - Logo.Height)/2)
case 1
Logopadding_left = 0
logopadding_height = 0
case 2
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = 0
case 3
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
case 4
Logopadding_left = 0
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
end select

Img.Canvas.DrawImage Logopadding_left, logopadding_height, Logo,opacity,transparence_color,100
Img.Save Server.MapPath("../upload/" & image)	'保存 
Img.close()
Logo.close()
set Img=nothing
set Logo = nothing

end Sub


' 转为根路径格式
Function RelativePath2RootPath(url)
	Dim sTempUrl
	sTempUrl = url
	If Left(sTempUrl, 1) = "/" Then
		RelativePath2RootPath = sTempUrl
		Exit Function
	End If

	Dim sWebEditorPath
	sWebEditorPath = Request.ServerVariables("SCRIPT_NAME")
	sWebEditorPath = Left(sWebEditorPath, InstrRev(sWebEditorPath, "/") - 1)
	Do While Left(sTempUrl, 3) = "../"
		sTempUrl = Mid(sTempUrl, 4)
		sWebEditorPath = Left(sWebEditorPath, InstrRev(sWebEditorPath, "/") - 1)
	Loop
	RelativePath2RootPath = sWebEditorPath & "/" & sTempUrl
End Function

' 根路径转为带域名全路径格式
Function RootPath2DomainPath(url)
	Dim sHost, sPort
	sHost = Split(Request.ServerVariables("SERVER_PROTOCOL"), "/")(0) & "://" & Request.ServerVariables("HTTP_HOST")
	sPort = Request.ServerVariables("SERVER_PORT")
	If sPort <> "80" Then
		sHost = sHost & ":" & sPort
	End If
	RootPath2DomainPath = sHost & url
End Function

'================================================
'作  用：替换字符串中的远程文件为本地文件并保存远程文件
'参  数：
'	sHTML		: 要替换的字符串
'	sExt		: 执行替换的扩展名
'================================================
Function ReplaceRemoteUrl(sHTML, sExt)
	Dim s_Content
	s_Content = sHTML
	If IsObjInstalled("Microsoft.XMLHTTP") = False then
		ReplaceRemoteUrl = s_Content
		Exit Function
	End If
	
	Dim re, RemoteFile, RemoteFileurl, SaveFileName, SaveFileType
	Set re = new RegExp
	re.IgnoreCase  = True
	re.Global = True
	re.Pattern = "((http|https|ftp|rtsp|mms):(\/\/|\\\\){1}(([A-Za-z0-9_-])+[.]){1,}(net|com|cn|org|cc|tv|[0-9]{1,3})(\S*\/)((\S)+[.]{1}(" & sExt & ")))"

	Set RemoteFile = re.Execute(s_Content)
	Dim a_RemoteUrl(), n, i, bRepeat
	n = 0
	' 转入无重复数据
	For Each RemoteFileurl in RemoteFile
		If n = 0 Then
			n = n + 1
			Redim a_RemoteUrl(n)
			a_RemoteUrl(n) = RemoteFileurl
		Else
			bRepeat = False
			For i = 1 To UBound(a_RemoteUrl)
				If UCase(RemoteFileurl) = UCase(a_RemoteUrl(i)) Then
					bRepeat = True
					Exit For
				End If
			Next
			If bRepeat = False Then
				n = n + 1
				Redim Preserve a_RemoteUrl(n)
				a_RemoteUrl(n) = RemoteFileurl
			End If
		End If		
	Next
	' 开始替换操作
	nFileNum = 0
	For i = 1 To n
		SaveFileType = Mid(a_RemoteUrl(i), InstrRev(a_RemoteUrl(i), ".") + 1)
		SaveFileName = GetRndFileName(SaveFileType)
		If SaveRemoteFile(SaveFileName, a_RemoteUrl(i)) = True Then
			nFileNum = nFileNum + 1
			If nFileNum > 0 Then
				sOriginalFileName = sOriginalFileName & "|"
				sSaveFileName = sSaveFileName & "|"
				sPathFileName = sPathFileName & "|"
			End If
			sOriginalFileName = sOriginalFileName & Mid(a_RemoteUrl(i), InstrRev(a_RemoteUrl(i), "/") + 1)
			sSaveFileName = sSaveFileName & SaveFileName
			sPathFileName = sPathFileName & sContentPath & SaveFileName
			s_Content = Replace(s_Content, a_RemoteUrl(i), sContentPath & SaveFileName, 1, -1, 1)
		End If
	Next

	ReplaceRemoteUrl = s_Content
End Function

'================================================
'作  用：保存远程的文件到本地
'参  数：s_LocalFileName ------ 本地文件名
'		 s_RemoteFileUrl ------ 远程文件URL
'返回值：True  ----成功
'        False ----失败
'================================================
Function SaveRemoteFile(s_LocalFileName, s_RemoteFileUrl)
	Dim Ads, Retrieval, GetRemoteData
	Dim bError
	bError = False
	SaveRemoteFile = False
	On Error Resume Next
	Set Retrieval = Server.CreateObject("Microsoft.XMLHTTP")
	With Retrieval
		.Open "Get", s_RemoteFileUrl, False, "", ""
		.Send
		GetRemoteData = .ResponseBody
	End With
	Set Retrieval = Nothing

	If LenB(GetRemoteData) > nAllowSize*1024 Then
		bError = True
	Else
		Set Ads = Server.CreateObject("Adodb.Stream")
		With Ads
			.Type = 1
			.Open
			.Write GetRemoteData
			.SaveToFile Server.MapPath(sUploadDir & s_LocalFileName), 2
			.Cancel()
			.Close()
		End With
		Set Ads=nothing
	End If

	If Err.Number = 0 And bError = False Then
		SaveRemoteFile = True
	Else
		Err.Clear
	End If
End Function



'================================================
'作  用：检查组件是否已经安装
'参  数：strClassString ----组件名
'返回值：True  ----已经安装
'        False ----没有安装
'================================================
Function IsObjInstalled(strClassString)
	On Error Resume Next
	IsObjInstalled = False
	Err = 0
	Dim xTestObj
	Set xTestObj = Server.CreateObject(strClassString)
	If 0 = Err Then IsObjInstalled = True
	Set xTestObj = Nothing
	Err = 0
End Function

'///////////////////////////////////////////////////////////////////////////////////
sub img_thumb_pro(path,imgname,str,add_mod)
	'对多张图生成缩略图的函数,并生成相应的sql查询语句 其中 logo.gif 为 ..\images\目录下的 logo.gif
	'path 源图相对路径（最后不带\）
	'img 源图名称
	'str的格式 "col,imgwidth,imgheight,watermark,background" 6个一组
	'img_thumb_pro("..\upload","do.gif","img,450,450,false,k.gif,thumb,150,150,false,xk.gif,true")
	'col 为该缩略图所保存的表列名称；
	'imgwidth为保存时的最大宽度，数值，0不限制
	'imgheight为保存时的最大高度，数值，0不限制
	'watermark为是否加水印，逻辑；
	'background 装饰框图形，如果为''，则不加装饰框，装饰框图位于 ../images 目录
	'crop_on 是否切图与指定宽高一样
	'add_mod 生成的sql是用于update 还是add
	'如img_thumb_pro("..\upload","banner_ditu_01.jpg","img2,210,138,false,kuang.gif,true,img_small,150,50,false,kuang.gif,false","update")
	'最后要删除原图
dim thumb_i,thumb_array,img_thumb_i
thumb_i = split(str,",")
redim thumb_array((ubound(thumb_i)+1)/6-1,5)

'数据进数组
img_thumb_j=0
img_thumb_j2=0
for img_thumb_i = lbound(thumb_i) to ubound(thumb_i)
	thumb_array(img_thumb_j,img_thumb_j2) = thumb_i(img_thumb_i)
	img_thumb_j2 = img_thumb_j2 + 1
	if img_thumb_j2 = 6 then
		img_thumb_j = img_thumb_j + 1
		img_thumb_j2 = 0
	end if
next

'生成缩略图与sql
for img_thumb_i = 0 to (ubound(thumb_i)+1)/6-1
	call made_thumb(path,imgname,thumb_array(img_thumb_i,0),cint(thumb_array(img_thumb_i,1)),cint(thumb_array(img_thumb_i,2)),cbool(thumb_array(img_thumb_i,3)),thumb_array(img_thumb_i,4),cbool(thumb_array(img_thumb_i,5)),add_mod)

	table_sql_pro(0) = table_sql_pro(0) & table_sql(0)
	table_sql_pro(1) = table_sql_pro(1) & table_sql(1)

	if img_thumb_i < (ubound(thumb_i)+1)/6-1 then 
	table_sql_pro(0) = table_sql_pro(0) & ","
		if add_mod <> "update" then table_sql_pro(1) = table_sql_pro(1) & ","
	end if
Next

'删除原图
Set File = CreateObject("Scripting.FileSystemObject")
ImagePath = Server.MapPath("..\upload") & "\" & imgname
if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
Set File = Nothing

end sub

'删除upload下的一个文件
Sub delFile(filename)
	Set File = CreateObject("Scripting.FileSystemObject")
	filename = Server.MapPath("..\upload") & "\" & filename
	if file.FileExists(filename) then File.DeleteFile(filename)
	Set File = Nothing
End sub

sub made_thumb(path,img,col,imgwidth,imgheight,watermark_on,background,crop_on,up_add)	
	'生成缩略图，源图相对路径（最后不带\，也是目录图相对路径），源图文件名，缩略图尺寸，尺寸中不能有0
	'输出缩略图名称, 宽度, 高度
	'如果要写框，则图的宽高为框图的宽高
	'crop_on是否剪切img图，使其合上尺寸
	'up_add 为生成的sql是update还是添加
	'如 made_thumb("..\upload\","banner_ditu_01.jpg","img2",210,138,false,"kuang.gif",true,"update")
	dim bigimg,bigimgpath,thumbpath,bgpath
	bigimgpath = Server.MapPath(path) & "\" & img
	thumbpath = Server.MapPath(path) & "\" & col & "__"  & img
	if background <> "" then
		bgpath = Server.MapPath("..\images") & "\" & background
		set kuangimg = Server.CreateObject("Persits.Jpeg")
		kuangimg.open bgpath
	else
		bgpath = ""
	end if

	Set bigimg = Server.CreateObject("Persits.Jpeg")
	bigimg.PreserveMetadata = True
	bigimg.Open bigimgpath

	if bigimg.OriginalWidth/bigimg.OriginalHeight < imgwidth/imgheight then	'如果原图比较高
			if crop_on = true then	'如果要求剪切
				if bigimg.OriginalWidth <= imgwidth then	'源图较小
						bigimg.Width = bigimg.OriginalWidth
					else
						bigimg.Width = imgwidth
				end if
				bigimg.Height = bigimg.Width * bigimg.OriginalHeight/bigimg.OriginalWidth
				bigimg.Crop 0,cint((bigimg.Height-imgheight)/2),bigimg.Width,bigimg.Height-cint((bigimg.Height-imgheight)/2)
				bigimg.Save thumbpath
			else
				bigimg.Width = imgheight * bigimg.OriginalWidth/bigimg.OriginalHeight
				bigimg.Height = imgheight
				bigimg.Save thumbpath
			end if
			if watermark_on = true then call watermark(col & "__"  & img,"logo.gif",0,0.3,"&HFFFFFF")
			if background <> "" then
				call img_inimg(background,col & "__"  & img,0)
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(kuangimg.OriginalWidth) 
				img_height_value = cstr(kuangimg.OriginalHeight)
			else
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(bigimg.Width) 
				img_height_value = cstr(bigimg.Height)
			end if
	else	'如果原图比较宽
			if crop_on = true then	'如果要求剪切
				if bigimg.OriginalHeight <= imgheight then	'源图较小
						bigimg.Height = bigimg.OriginalHeight
					else
						bigimg.Height = imgheight
				end if
				bigimg.Width = bigimg.Height * bigimg.OriginalWidth/bigimg.OriginalHeight
				bigimg.Crop cint((bigimg.Width-imgwidth)/2),0,bigimg.Width-cint((bigimg.Width-imgwidth)/2),bigimg.Height
				bigimg.Save thumbpath
			else
				bigimg.Width = imgwidth
				bigimg.Height = imgwidth * bigimg.OriginalHeight/bigimg.OriginalWidth
				bigimg.Save thumbpath
			end if
			if watermark_on = true then call watermark(col & "__"  & img,"logo.gif",0,0.3,"&HFFFFFF")
			if background <> "" then
				call img_inimg(background,col & "__"  & img,0)
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(kuangimg.OriginalWidth) 
				img_height_value = cstr(kuangimg.OriginalHeight)
			else
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(bigimg.Width) 
				img_height_value = cstr(bigimg.Height)
			end if
	end if

	if up_add = "update" then
		table_sql(0) = img_col_name & "='" & img_col_value & "'," & img_width_name & "=" & img_width_value & "," & img_height_name & "="  & img_height_value
		table_sql(1) = ""
	else
		table_sql(0) = img_col_name & "," & img_width_name & "," & img_height_name
		table_sql(1) = "'" & img_col_value & "'," & img_width_value & "," & img_height_value
	end if

	bigimg.close()
	set bigimg = nothing
	if background <> "" then
		kuangimg.close()
		set kuangimg = nothing
	end if

end sub

'********************************************************
Sub img_inimg(bgimg,fimg,position)

dim Img,Logo,ImgPath,LogoPath
Set Img = Server.CreateObject("Persits.Jpeg") 
Img.PreserveMetadata = True
Set Logo = Server.CreateObject("Persits.Jpeg") 
'Call OutScript("parent.UploadError('指示: " & sUploadDir & " | "  & sSaveFileName & "')")

ImgPath = Server.MapPath("..\images") & "\" & bgimg 
Img.Open ImgPath 

LogoPath = Server.MapPath("..\upload") & "\" & fimg
Logo.Open LogoPath 

'大小适应，即确定fimg.width,logo.height的值
if  Logo.OriginalWidth / Logo.OriginalHeight >= Img.OriginalWidth / Img.OriginalHeight then
	if Logo.OriginalWidth > Img.OriginalWidth then
		Logo.Width = Img.OriginalWidth
		Logo.Height = Img.OriginalWidth / Logo.OriginalWidth  * Logo.OriginalHeight
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
else
	if Logo.OriginalHeight > Img.OriginalHeight then
		Logo.Height = Img.OriginalHeight
		Logo.Width = Img.OriginalHeight / Logo.OriginalHeight  * Logo.OriginalWidth
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
end if

'写位置
dim Logopadding_left,logopadding_height
select case position
case 0
Logopadding_left = cint((Img.OriginalWidth - Logo.Width)/2)
logopadding_height = cint((Img.OriginalHeight - Logo.Height)/2)
case 1
Logopadding_left = 0
logopadding_height = 0
case 2
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = 0
case 3
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
case 4
Logopadding_left = 0
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
end select

Img.Canvas.DrawImage Logopadding_left, logopadding_height, Logo
Img.Save Server.MapPath("../upload/" & fimg)	'保存 
Img.close()
Logo.close()
set Img=nothing
set Logo = nothing

end Sub

sub sql_command(ByVal str)
	Dim conn
	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	conn.execute str
	set conn=nothing
End sub

%>