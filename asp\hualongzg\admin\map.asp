<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %> 
<!--#include file="../Connections/conn.asp" -->
<%
dim map_v_s				'确定后台显示的方式，如果是false则显示没有服务商的地方，如果为true则显示已经有的地方
map_v_s = true
if map_v_s = false then map_v_s_1 = "分支机构"
if map_v_s = true then map_v_s_1 = "分支机构"

function repCON(strCON) '首先转变成html格式，再替换字符串里面的指定字符，这里是add，tel等为粗体
strCON = replace(Replace(HTMLEncode(strCON),vbCrLF,"<br>"),"  ","&nbsp;&nbsp;")
strCON = replace(strCON,"Add:","<font color=ffffcc><b>ADD:</b></font>")
strCON = replace(strCON,"add:","<font color=ffffcc><b>ADD:</b></font>")
strCON = replace(strCON,"Tel:","<font color=ffffcc><b>TEL:</b></font>")
strCON = replace(strCON,"tel:","<font color=ffffcc><b>TEL:</b></font>")
strCON = replace(strCON,"Fax:","<font color=ffffcc><b>FAX:</b></font>")
strCON = replace(strCON,"fax:","<font color=ffffcc><b>FAX:</b></font>")
strCON = replace(strCON,"email:","<font color=ffffcc><b>EMAIL:</b></font>")
strCON = replace(strCON,"Email:","<font color=ffffcc><b>EMAIL:</b></font>")
repCON = strCON
end function

Dim rsAGENCE
Dim rsAGENCE_numRows

Set rsAGENCE = Server.CreateObject("ADODB.Recordset")
rsAGENCE.ActiveConnection = MM_conn_STRING
rsAGENCE.Source = "SELECT * FROM chinamap"
rsAGENCE.CursorType = 0
rsAGENCE.CursorLocation = 2
rsAGENCE.LockType = 1
rsAGENCE.Open()

rsAGENCE_numRows = 0
%>
<%
Dim Repeat1__numRows
Dim Repeat1__index

Repeat1__numRows = -1
Repeat1__index = 0
rsAGENCE_numRows = rsAGENCE_numRows + Repeat1__numRows
%>
<%
' *** Edit Operations: declare variables

Dim MM_editAction
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) = "frmPROVI" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "chinamap"
  MM_editColumn = "en_name"
  MM_recordId = "'" + Request.Form("MM_recordId") + "'"
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "this_content|value"
  MM_columnsStr = "this_content|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(MM_i) & " = " & MM_formVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">销售网络</h1>
<%
Dim rsNONE
Dim rsNONE_numRows

Set rsNONE = Server.CreateObject("ADODB.Recordset")
rsNONE.ActiveConnection = MM_conn_STRING
'rsNONE.Source = "SELECT * FROM chinamap WHERE this_content = '' or isNULL(this_content) ORDER BY en_name ASC"
rsNONE.Source = "SELECT * FROM chinamap ORDER BY en_name ASC"
rsNONE.CursorType = 0
rsNONE.CursorLocation = 2
rsNONE.LockType = 1
rsNONE.Open()

rsNONE_numRows = 0
%>
<%
Dim RepeatNONE__numRows
Dim RepeatNONE__index

RepeatNONE__numRows = -1
RepeatNONE__index = 0
rsNONE_numRows = rsNONE_numRows + RepeatNONE__numRows
%>
<div align=center>
<MAP name=Services><% 
While ((Repeat1__numRows <> 0) AND (NOT rsAGENCE.EOF)) 
%><AREA shape=RECT coords="<%=(rsAGENCE.Fields.Item("map_link").Value)%>"  href="map.asp?Province=<%=(rsAGENCE.Fields.Item("en_name").Value)%>"><% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsAGENCE.MoveNext()
Wend
%></MAP>
<img src="../images/map.gif" width="323" height="265" border=0 useMap=#Services></div>
                  <br>
<%rsPROVIN__MMColParam = Request.QueryString("Province")
Dim rsPROVIN
Dim rsPROVIN_numRows

Set rsPROVIN = Server.CreateObject("ADODB.Recordset")
rsPROVIN.ActiveConnection = MM_conn_STRING
rsPROVIN.Source = "SELECT * FROM chinamap WHERE en_name = '" + Replace(rsPROVIN__MMColParam, "'", "''") + "'"
rsPROVIN.CursorType = 0
rsPROVIN.CursorLocation = 2
rsPROVIN.LockType = 1
rsPROVIN.Open()

rsPROVIN_numRows = 0
%>
<table width=400 cellpadding=4 cellspacing=0 border=0 align=center>
<tr><td align=center bgcolor=<%=(back_menubackcolor)%>><%=(map_v_s_1)%></td></tr>
<tr><td align=left class=p12>
<% 
While ((RepeatNONE__numRows <> 0) AND (NOT rsNONE.EOF)) 
%>
<font color=ff0000><a href="?Province=<%=(rsNONE.Fields.Item("en_name").Value)%>"><%
if rsNONE("this_content") <> "" then
response.write("<b>")
end if%><%=(rsNONE.Fields.Item("name").Value)%><%
if rsNONE("this_content") <> "" then
response.write("</b>")
end if%></a></font> 
<% 
  RepeatNONE__index=RepeatNONE__index+1
  RepeatNONE__numRows=RepeatNONE__numRows-1
  rsNONE.MoveNext()
Wend
%>
</td></tr>
<% If (Request.QueryString("Province") <> "") Then %>
  <form METHOD="POST" name=frmPROVI action="<%=MM_editAction%>">
    
<tr bgcolor="<%=(back_menubackcolor)%>"><td align=center colspan=2><b><%=(rsPROVIN("name"))%></b></td></tr>
<tr align=center><td><textarea name="this_content" rows=10 style="width:400px"><%=(rsPROVIN.Fields.Item("this_content").Value)%></textarea></td>
</tr>
<tr><td align=right colspan=2><input type="submit" value="确定" style="width:70px"></td></tr>
    <input type="hidden" name="MM_update" value="frmPROVI">
    <input type="hidden" name="MM_recordId" value="<%= rsPROVIN.Fields.Item("en_name").Value %>">
  </form>
<%
rsPROVIN.Close()
Set rsPROVIN = Nothing
end if
%></table>
<!--#include file ="_bottom.asp"-->	
<%
rsAGENCE.Close()
Set rsAGENCE = Nothing
%>
<%
rsNONE.Close()
Set rsNONE = Nothing
%>