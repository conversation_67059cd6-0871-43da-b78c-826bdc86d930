<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">职位列表</h2>
<SCRIPT language=javascript>
var http_request = false;
function showrequest(url,element,asyn) {
	var isFFCLS = true;
	http_request = false;
    if (window.XMLHttpRequest) {
        http_request = new XMLHttpRequest();
        if (http_request.overrideMimeType) {
            http_request.overrideMimeType('text/xml');
        }
    } else if (window.ActiveXObject) {
        try {
            http_request = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            try {
            http_request = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {}
        }
    }
    if (!http_request) {alert('Giving up :( Cannot create an XMLHTTP instance');return false;}
    http_request.onreadystatechange = sub_showrequest;
    http_request.open('GET', url, asyn);
    http_request.send(null);
	if ((!asyn) && (isFFCLS)) sub_showrequest();
	function sub_showrequest(){
	    if (http_request.readyState == 4) {
        if (http_request.status == 200) {
			isFFCLS = false;
			document.getElementById(element).innerHTML = http_request.responseText;
        } else {alert('There was a problem with the request.');}
		}	
	}
}

function validateadd(){
	var n2=document.frm_addjob.elements[2],n3=document.frm_addjob.elements[3],n4=document.frm_addjob.elements[4];
	if (n2.value=="")	{alert("请输入职位名称");n2.focus();return false;}
	if ((n3.value != "") && isNaN(n3.value)){alert("请输入招聘人数\n招聘人数应该为数字，或者不输入，如果为0则不限制");n3.focus();return false;}
	if (n4.value=="")	{alert("请输入职位详细要求");n4.focus();return false;}
}

function addjob(deptid){
showrequest('jobs_position_detail.asp?action=add&deptid=' + deptid,'ac',false);
var p = document.getElementById("dpt" + deptid),q=document.getElementById("ac");
q.style.top = (p.offsetTop + 165 + q.offsetHeight <=document.body.scrollHeight)?(p.offsetTop + 165):(document.body.scrollHeight-q.offsetHeight-100);
}

function viewjob(jobid){
showrequest('jobs_position_detail.asp?action=view&jobid=' + jobid,'ac',false);
var p = document.getElementById("job" + jobid),q=document.getElementById("ac");
q.style.top = (p.offsetTop + 165 + q.offsetHeight <=document.body.scrollHeight)?(p.offsetTop + 165):(document.body.scrollHeight-q.offsetHeight-100);
}
function modjob(jobid){
showrequest('jobs_position_detail.asp?action=mod&jobid=' + jobid,'ac',false);
var p = document.getElementById("job" + jobid),q=document.getElementById("ac");
q.style.top = (p.offsetTop + 165 + q.offsetHeight <=document.body.scrollHeight)?(p.offsetTop + 165):(document.body.scrollHeight-q.offsetHeight-100);
}
function deljob(){
return confirm("删除这个职位后，应聘这个职位的简历将列入其他简历中\n您可以停招这个职位，停招职位不会在前台显示，以后需要的时候，使用再招即可。这样以后用不着重新输入职位\n\n真的删除这个职位？");
}

function stopjob(jobid){
redirectUrl("jobs_position_detail.asp?action=stop&jobid=" + jobid);
}

function redirectUrl(url)
{
var link = document.createElement("A");
link.href = url;
document.body.insertBefore(link);
link.click();
}
</script>
<table width="300" style="padding:0;margin:0;border:1px solid #666">
<thead>
<tr>
	<td>职位名称</td>
	<td>人数</td>
	<td width=150>操作</td>
</tr>
</thead><tbody>
<%
set rs=server.createobject("adodb.recordset")
set rs1=server.createobject("adodb.recordset")
sql="select id,name from jobs_dept order by id asc"
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
%>
<tr id="dpt<%=rs("id")%>"><td colspan=4 height=2></td></tr><tr><td colspan=4><div style="float:right;margin-right:5px" class=hbottom><a href="javascript:void(null)" onclick="addjob(<%=rs("id")%>);"><span>增加职位</span></a></div><b><%=rs("name")%></b></td></tr>

<%

sql1="select id,name,stop,this_amount,availabe_date from jobs where dept_id=" & rs("id") & " order by id asc"
rs1.open sql1,MM_conn_STRING,1,1
While Not rs1.eof
is_availabe = True
availabe_str = "停招"
If rs1("stop") = True Then
	is_availabe = False
	availabe_str = "招聘"
End if

%>
<tr id="job<%=rs1("id")%>">
	<td<%if is_availabe = false then response.write(" class=linethrough")%>><%=rs1("name")%></td>
	<td><%If rs1("this_amount") > 0 Then response.write rs1("this_amount")%></td>
	<td><a href="javascript:void(null)" onclick="viewjob(<%=rs1("id")%>)"><span class=hbottom>查看</span></a> <a href="javascript:void(null)" onclick="modjob(<%=rs1("id")%>)"><span class=hbottom>修改</span></a> <a href="jobs_position_detail.asp?action=del&jobid=<%=rs1("id")%>" onclick="javascript:if(!deljob()){return false}"><span class=hbottom>删除</span></a> <a href="jobs_position_detail.asp?action=stop&jobid=<%=rs1("id")%>"><span class=hbottom><%=availabe_str%></span></a></td>
</tr>
<%
rs1.movenext()
Wend
rs1.close()

rs.movenext()
wend
rs.close()
Set rs=Nothing
Set rs1=nothing
%>
</tbody></table>
<div id="ac" style="position:absolute;top:190px;left:500px;z-index:2"></div>

<!--#include file ="_bottom.asp"-->