<HTML>
<HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style type="text/css">
body, a, table, div, span, td, th, input, select{font:9pt;font-family: "宋体", Verdana, Arial, Helvetica, sans-serif;}
body {padding:5px}
</style>
<script language="JavaScript" src="interface/dialog.js" charset="utf-8"></script>


<script language="JavaScript">
//取传到模式窗口的参数
var sImgArgu = unescape(URLParams['sImgArgu']);
var MDParams = new Array() ;
var mParams = sImgArgu.split(',') ;
for (i=0 ; i < mParams.length ; i++) {
	MDParams[i] = mParams[i] ;
}

//旧图
var oldImagstr,oldImagstr1;
findOldImage();
function findOldImage (){
	var obj = window.dialogArguments.document.getElementById(MDParams[1]);
	if (obj.getElementsByTagName("img").length>0){
		oldImagstr = urlFile(obj.getElementsByTagName("img")[0].src.toString());
		oldImagstr1 = urlFile(obj.getElementsByTagName("img")[0].parentNode.href.toString());
	}
}

function urlFile(str){
	var Array1 = str.split("\/")
	return Array1[Array1.length-1]
}
var sAction = "INSERT";
var sTitle = "插入";

var oControl;
var oSeletion;
var sRangeType;

var sFromUrl = "http://";
var sWidth = "";
var sHeight = "";

var sCheckFlag = "file";

document.write("<title>特征图片</title>");


// 初始值
function InitDocument(){
//	d_width.value = sWidth;
//	d_height.value = sHeight;
}


// 图片来源单选点击事件
function RadioClick(what){
	if (what=="url"){
//		d_checkfromfile.checked=false;
		d_file.myform.uploadfile.disabled=true;
	}else{
//		d_checkfromurl.checked=false;
		d_file.myform.uploadfile.disabled=false;
//		d_checkfromfile.checked=true;
	}
}

// 上传帧调入完成时执行
function UploadLoaded(){
	// 初始radio
	RadioClick(sCheckFlag);
}

// 上传错误
function UploadError(sErrDesc){
	AbleItems();
	RadioClick('file');
	divProcessing.style.display="none";
	try {
		BaseAlert(d_file.myform.uploadfile,sErrDesc);
	}
	catch(e){}
}

function imgRestore (imgname,sAction){
	var obj = window.dialogArguments.document.getElementById(MDParams[1]);
	if (obj.getElementsByTagName("img").length>0){	//改图，对于产品，即使未加图也作为改图
		if (MDParams[2] == 'page' && MDParams[0] == 'add'){
			obj.getElementsByTagName("img")[0].src = "../upload/" + MDParams[3] + "__" + imgname ;
			obj.getElementsByTagName("img")[0].parentNode.href = "../upload/" + MDParams[6] + "__" + imgname ;
		}else{
			obj.getElementsByTagName("img")[0].src = "../upload/" + MDParams[4] + "__" + imgname ;
			obj.getElementsByTagName("img")[0].parentNode.href = "../upload/" + MDParams[7] + "__" + imgname ;
		}
	}else{	//增加图形html码，只有page才有
		obj.innerHTML = "<span>特征图片</span>" + "<a href=\"../upload/" + MDParams[6] + "__" + imgname + "\" class=\"img\" target=\"_blank\"><img src=\"../upload/" + MDParams[3] + "__" + imgname + "\" alt=\"点击看大图\" /></a>" + obj.innerHTML;
		oldImagstr = urlFile(obj.getElementsByTagName("img")[0].src.toString());
		oldImagstr1 = urlFile(obj.getElementsByTagName("img")[0].parentNode.href.toString());
	}

	if (MDParams[2] == 'page' && MDParams[0] == 'add'){
		window.dialogArguments.document.getElementById("addimginput").innerHTML = "<input type=\"hidden\" name=\"imgsmall\" value=\"" + MDParams[3] + "__" + imgname + "\"><input type=\"hidden\" name=\"img\" value=\"" + MDParams[6] + "__" + imgname + "\">";
	}

	if (MDParams[2] == 'page'){//刷新列表中的当前面
		var pAc = "";
		if (window.dialogArguments.document.getElementById("navnum")){
			nCol = window.dialogArguments.document.getElementById("navnum").getElementsByTagName("A");
			for (i=0;i<nCol.length;i++){
				if (nCol[i].className == 'ac'){pAc = nCol[i].innerHTML;break;}
			}
		}
		window.dialogArguments.gotoPage(pAc);
	}

	setTimeout("ReturnValue()", 500);
}
// 文件上传完成时执行,带入上传文件名
function UploadSaved(sPathFileName){
	divProcessing.style.display="none";
//	d_fromurl.value = sPathFileName;
	ReturnValue();
}

// 本窗口返回值，对于add方式修改返回值
function ReturnValue(){
	window.returnValue = null;
	window.close();
//	sFromUrl = d_fromurl.value;
//	sWidth = d_width.value;
//	sHeight = d_height.value;
/*
	if (sAction == "MODI") {
		oControl.src = sFromUrl;
		oControl.width = sWidth;
		oControl.height = sHeight;
		oControl.style.width = sWidth;
		oControl.style.height = sHeight;
	}else{
		var sHTML = '';
		if (sHTML!=""){
			sHTML=' style="'+sHTML+'"';
		}
		sHTML = '<img id=htmlbuilder_TempElement_Img src="'+sFromUrl+'"'+sHTML;
		if (sWidth!=""){
			sHTML=sHTML+' width="'+sWidth+'"';
		}
		if (sHeight!=""){
			sHTML=sHTML+' height="'+sHeight+'"';
		}
		sHTML=sHTML+'>';
		dialogArguments.insertHTML(sHTML);

		var oTempElement = dialogArguments.document.getElementById("img_list"+dialogArguments.document.list_id);
//		oTempElement.src = sFromUrl;
		oTempElement.removeAttribute("id");

	}
*/
}

// 点确定时执行
function ok(){
		d_file.myform.originalfile.value = oldImagstr;
		d_file.myform.originalfile1.value = oldImagstr1;
		d_file.myform.sImgArgu.value = sImgArgu;
		// 使各输入框无效
		DisableItems();
		// 显示正在上传图片
		divProcessing.style.display="";
		// 上传表单提交
		d_file.myform.submit();
}

// 使所有输入框无效
function DisableItems(){
	Ok.disabled=true;
}

// 使所有输入框有效
function AbleItems(){
	Ok.disabled=false;
}

</script>

<BODY bgColor="menu" onload="InitDocument()">

<table border=0 cellpadding=0 cellspacing=0 align=center>
<tr>
	<td>
	<fieldset>
	<legend>图片来源</legend>
	<table border=0 cellpadding=0 cellspacing=0>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=5></td>
		<td colspan=5>
		<Script Language=JavaScript>
		document.write('<iframe id="d_file" frameborder=0 src="uploadfile.asp?type=image" width="100%" height="22" scrolling=no></iframe>');
		</Script>
		</td>
		<td width=7></td>
	</tr>
	</table>
	</fieldset>
	</td>
</tr>
<tr><td height=5></td></tr>
<tr><td align=right><input type="submit" value='  确定  ' id="Ok" onclick="ok()">&nbsp;&nbsp;<input type="button" value="  取消  " onclick="window.close();"></td></tr>
</table>

<div id="divProcessing" style="width:200px;height:30px;position:absolute;left:70px;top:15px;display:none">
<table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor="#0046D5"><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF>...图片上传中...请等待...</font></marquee></td></tr></table>
</div>

</body>
</html>