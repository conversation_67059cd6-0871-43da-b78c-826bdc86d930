<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">栏目管理</h1>
<script LANGUAGE="JavaScript">
function check()
{
document.Form1.submit()
}
</SCRIPT>
<%
sub select_groupid(i)	'输出选择用户组
response.write("<select name='class_id' style='width:125px'>")
dim rs,sql
sql="select id,name from class order by id asc"
Set rs= Server.CreateObject("ADODB.Recordset")
rs.open sql,MM_conn_STRING,1,1
while not rs.eof
response.write("<option value=" & rs("id"))
if rs("id") = i then response.write(" selected")
response.write ">" & rs("name") & "</option>"
rs.movenext()
wend
response.write("</select>")
rs.close()
set rs=nothing
end sub

call main()
set rs=nothing

sub main()
%>
<center>
<%
if request("action")="editsave" then 
call editsave()
elseif request("action")="add" then 
call add()
elseif request("action")="addsave" then 
call addsave()
else
call manager()
end if
%>
<p><%=body%></p>
<%
end sub

sub manager()
dim sql
dim rs
dim id
id=request("subid")
sql="select subclass.*,class.name as classname,class.this_edit from subclass inner join class on subclass.id = class.id where subid=" & cstr(id)
Set rs= Server.CreateObject("ADODB.Recordset")
rs.open sql,MM_conn_STRING,1,1
%>
<script language="JavaScript">
<!--
function CheckAll(form) {
for (var i=0;i<form.elements.length;i++) {
var e = form.elements[i];
if (e.name != 'chkall') e.checked = form.chkall.checked; 
}
}
//-->
</script>
<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" bordercolorlight=<%=back_menubackcolor0%>>
<FORM action="class_detail.asp?action=editsave&subid=<%=id%>" method="post" name="Form1">
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>>&nbsp;&nbsp;子栏目属性</td>
</TR>
<TR> 
<TD align="center" height="28">子栏目名称</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=name size="20" value=<%=rs("name")%>></font></TD>
</TR>
<TR> 
<TD align="center" height="28">栏目类型</td>
<TD>&nbsp;
<select name="style_id"><%
set rs_temp=server.createobject("adodb.recordset")
sql="select * from subclass_style"
rs_temp.open sql,MM_conn_STRING,1,1
while not rs_temp.eof
response.write("<option value=" & rs_temp("style_id"))
	if rs("style_id") = rs_temp("style_id") then response.write(" selected")
response.write (">" & rs_temp("style_name") & "</option>")
rs_temp.movenext()
wend 
rs_temp.close()
set rs_temp = nothing
%></select>

</TD>
</TR>
<TR>
<TD align="center" height="28">是否单页面栏目</TD>
<TD>&nbsp;&nbsp;是 <input type="radio" name="onlyone" value=1<%if rs("onlyone") = true then response.write(" checked")%>>　　否 <input type="radio" name="onlyone" value=0<%if rs("onlyone") = false then response.write(" checked")%>></TD>
</TR>
<TR>
<TD align="center" height="28">排序方式</TD>
<TD>&nbsp;&nbsp;最新的在后面 <input type="radio" name="date_sort" value=1<%if rs("date_sort") = 1 then response.write(" checked")%>>　　最新的在前面 <input type="radio" name="date_sort" value=0<%if rs("date_sort") = 0 then response.write(" checked")%>></TD>
</TR>
<tr>
<TD align="center" height="28">主题显示日期</td>
<TD>&nbsp;&nbsp;显示 <input type="radio" name="date_display_on" value=1<%if rs("date_display_on") = true then response.write(" checked")%>>　　不显示 <input type="radio" name="date_display_on" value=0<%if rs("date_display_on") = false then response.write(" checked")%>></TD>
</TR>
<%if rs("this_edit") = true then%>
<TR>
<TD colspan=2 height="28" align="center" bgcolor=<%=back_menubackcolor%>> 
<%If rs("not_edit") =False then%><input type="button" value="修 改" onclick="check()"><%End if%>
</TD>
</TR><%end if%>
</TABLE></FORM>
<%
rs.close
set rs=nothing
end sub
sub editsave()
set rs=server.createobject("adodb.recordset")
sql="select subclass.* from subclass where subid="&request("subid")
rs.open sql,MM_conn_STRING,3,3
rs("name")=request("name")
rs("style_id")=request("style_id")
rs("onlyone")=request("onlyone")
rs("date_sort")=request("date_sort")
rs("date_display_on")=request("date_display_on")
rs.update
rs_subid = rs("subid")
rs.close()
set rs = nothing
response.write "栏目信息修改成功"
response.redirect("class_detail.asp?subid=" & rs_subid)
end sub
sub add()
%>
<script language="JavaScript">
<!--
function CheckAll(form) {
for (var i=0;i<form.elements.length;i++) {
var e = form.elements[i];
if (e.name != 'chkall') e.checked = form.chkall.checked; 
}
}
//-->
</script>
<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" bordercolorlight=<%=back_menubackcolor0%>>
<FORM action="class_detail.asp?top=<%=request.querystring("top")%>&action=addsave" method="post" name="Form1">
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>><b>增加子栏目</b></td>
</TR>
<TR> 
<TD align="center" height="28">子栏目名称</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=name size="20"></font></TD>
</TR>
<TR> 
<TD align="center" height="28">栏目类型</td>
<TD><font color="#FF0000">&nbsp;&nbsp;<select name="style_id"><%
set rs_temp=server.createobject("adodb.recordset")
sql="select * from subclass_style"
rs_temp.open sql,MM_conn_STRING,1,1
while not rs_temp.eof
response.write("<option value=" & rs_temp("style_id") & ">" & rs_temp("style_name") & "</option>")
rs_temp.movenext()
wend 
rs_temp.close()
set rs_temp = nothing
%></select></font>
</TD>
</TR>
<TR>
<TD align="center" height="28">是否单页面栏目</TD>
<TD>&nbsp;&nbsp;是 <input type="radio" name="onlyone" value=1>　　否 <input type="radio" name="onlyone" value=0 checked></TD>
</TR>
<TR>
<TD align="center" height="28">排序方式</TD>
<TD>&nbsp;&nbsp;最新的在前面 <input type="radio" name="date_sort" value=1 checked>　　最新的在后面 <input type="radio" name="date_sort" value=0></TD>
</TR>
<tr>
<TD align="center" height="28">主题显示日期</td>
<TD>&nbsp;&nbsp;显示 <input type="radio" name="date_display_on" value=1 checked>　　不显示 <input type="radio" name="date_display_on" value=0></TD>
</TR>
<TR>
<TD colspan=2 height="28" align="center" bgcolor=<%=back_menubackcolor%>> 
<input type="button" value="添 加" onclick=check()>
</TD>
</TR>
</TABLE></FORM>

<%
end sub
sub addsave()

set rs=server.createobject("adodb.recordset")
sql="select * from subclass"
rs.open sql,MM_conn_STRING,3,3
rs.addnew
rs("name")=request("name")
rs("id")=request("top")
rs("style_id")=request("style_id")
rs("onlyone")=request("onlyone")
rs("date_sort")=request("date_sort")
rs("date_display_on")=request("date_display_on")
rs.update
response.redirect("class.asp?top=" & request.querystring("top"))
end sub
%>
</center>

<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" align=center>
<tr><td class=p12>
<div align=center class=p14>帮助</div>
<b>栏目类型</b><br>
1）标准栏目<br>
一般栏目<br>
2）纯图片栏目<br>
展示图形，点击图形看大图，除了图片、主题外，没有详细文字内容<br>
3）展示栏目<br>
展示图形，点击打开相应详细页面，与纯图片栏目相比，展示栏目有详细内容<br>
4）流媒体栏目<br>
支持windows media 与real 二种格式，自动识别<br>
5）软件下载栏目
<br><br>
<b>是否单页面栏目</b>：单页面栏目指该栏目始终只有一个页面
<br><br>
<b>排序方式</b>：分按时间顺序或者倒序
<br><br>
<b>主题显示日期</b>：对于新闻类栏目需要在主题上显示日期
</td></tr></table>
 
<!--#include file ="_bottom.asp"-->