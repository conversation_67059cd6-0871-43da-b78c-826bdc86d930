<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) AND (inint(session("flag"),"8") = false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: (Modified for File Upload) declare variables
Dim MM_editAction
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (request.querystring <> "") Then
  MM_editAction = MM_editAction & "?" & request.querystring
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: (Modified for File Upload) set variables

If (CStr(request.form("MM_insert")) = "form1") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "xueli"
  MM_editRedirectUrl = "member_xl.asp"
  MM_fieldsStr  = "xldescript|value|xlname|value"
  MM_columnsStr = "xldescript|',none,''|xlname|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(request.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And request.querystring <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And request.querystring <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & request.querystring
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & request.querystring
    End If
  End If

End If
%>
<%
' *** Insert Record: (Modified for File Upload) construct a sql insert statement and execute it

Dim MM_tableValues
Dim MM_dbValues

If (CStr(request.form("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End If
    MM_tableValues = MM_tableValues & MM_columns(MM_i)
    MM_dbValues = MM_dbValues & MM_formVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
' *** Delete Record: declare variables

if (CStr(Request("del")) = "del" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "xueli"
  MM_editColumn = "xlid"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "member_xl.asp"

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If
%>
<%
' *** Delete Record: construct a sql delete statement and execute it

If (CStr(Request("del")) = "del" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then


    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsMIDI
Dim rsMIDI_numRows

Set rsMIDI = Server.CreateObject("ADODB.Recordset")
rsMIDI.ActiveConnection = MM_conn_STRING
rsMIDI.Source = "SELECT xueli.xlid, xueli.xlname,xueli.xldescript,count(member.id) as shl FROM (xueli left join member on xueli.xlid=member.xlid) group by xueli.xlid, xueli.xlname,xueli.xldescript, member.xlid ORDER BY xueli.xlid asc"
rsMIDI.CursorType = 0
rsMIDI.CursorLocation = 2
rsMIDI.LockType = 1
rsMIDI.Open()

rsMIDI_numRows = 0
%>
<%
Dim Repeat1__numRows
Dim Repeat1__index

Repeat1__numRows = -1
Repeat1__index = 0
rsMIDI_numRows = rsMIDI_numRows + Repeat1__numRows
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">学历设置</h1>
<table width=500 border=0 cellpadding=3 align=center>
<script language="JavaScript">
function VerifyInput()
{
	if (document.form1.xlname.value == "")
	{
	alert("输入学历名称！");
	document.form1.name.focus();
	return false;
	}

}
</script>
<form action="<%=MM_editAction%>" method="POST" name="form1" onsubmit="return VerifyInput()">
<tr align=center bgcolor=<%=(back_menubackcolor)%>><td>学历名称</td><td nowrap>备注</td><td>&nbsp;</td></tr>  
<tr><td width=150><input name="xlname" type="text" size=20></td><td nowrap>
  <input type="text" name="xldescript" size=45></td><td width=20>
  <input type="submit" name="Submit2" value="增加">
  <input type="hidden" name="MM_insert" value="form1"></td></tr>
</form>
</table><br>
<table width=500 border=0 cellpadding=3 align=center>
<tr align=center bgcolor=<%=(back_menubackcolor)%>><td width=150>学历名称</td><td>备注</td><td nowrap>删除</td></tr>
<% 
While ((Repeat1__numRows <> 0) AND (NOT rsMIDI.EOF)) 
%>
<form name="form2" method="POST" action="member_xl.asp">
<tr><td nowrap class=p12 align=center><%=(HTMLEncode(rsMIDI.Fields.Item("xlname").Value))%>(<%=(rsMIDI.Fields.Item("shl").Value)%>)</td><td>
  <%=(rsMIDI.Fields.Item("xldescript").Value)%>&nbsp;</td><td width=20>
  <input type="submit" name="Submit" value="删除"<%if rsMIDI.Fields.Item("shl").Value > 0 then%> onClick="GP_popupConfirmMsg('删除这个学历将设定为该学历的学生的学历信息错误，确定吗？');return document.MM_returnValue"<%else%>onClick="GP_popupConfirmMsg('删除这个学历？');return document.MM_returnValue"<%end if%>>
  <input type="hidden" name="del" value="del">
  <input type="hidden" name="MM_recordId" value="<%= rsMIDI.Fields.Item("xlid").Value %>">
</td></tr></form>
<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsMIDI.MoveNext()
Wend
%>
</table>
<!--#include file ="_bottom.asp"-->
<%
rsMIDI.Close()
Set rsMIDI = Nothing
%>