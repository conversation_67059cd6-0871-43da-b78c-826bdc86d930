<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%> 
<!--#include file="Connections/conn.asp" -->
<!--#include file="_config.asp" -->
<a href="javascript:void(null)" class="cls"></a>
<%
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'※											网站搜索												 ※
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'ver 4.0 2009-03-23 用于新的数据库系统 etorch.cn 以后的版本
'说明: 可用于ajax模式，ajax文件为 search_ajax.asp ，也可以用于非ajax模式，搜索文件名可定义
'支持多个关键词，关键词之间用空格分开，空格不分全角半角；支持对页面、产品、论坛的搜索；支持高级搜索，即可指定搜索类别（页面、产品或者论坛|主题，内容）；支持搜索结果分二级显示，第一级显示各个搜索的部分结果，第二级显示所有搜索结果

'对于其他项目可能要修改一下产品搜索的 sql 串 rs_search_2 ，有二处 在'''''sql 处
'参数
search_file_name = "search_ajax.asp"
search_mode_default = "advance"		'缺省搜索模式，如果是 advance ，则搜索内容；如果是 simple 则只搜索主题
search_keywords_url = "keywords"	'关键词URL参数，不一定是keywords。
search_type_url = "type"			'搜索类URL参数，如无则全部搜索，不一定是type。页面为1，产品为2，论坛为3
search_range_url = "r"				'搜索范围URL参数，如为 title 为主题， all 为高级，如无则用缺省search_mode_default
search_display_sim = 5				'第一级显示搜索结果个数
search_display_pagerows =10			'第二级显示时的记录数
search_page_on	= true			'页面搜索是否打开，true/false
search_pro_on = true			'产品搜索是否打开
search_pro_split = true	'产品与类等是否分开，对于大量产品应该为 true，当分开时，搜索结果显示所有匹配的类名。如果为true的话要修改 v_search_pro视图，把其中的products表的去掉
search_procate_on = false		'产品类搜索是否开，先要建一个v_search_pro视图，其中列为id,name,content,from_table，如果产品类没有单独的页，不要打开
search_propara_on = false		'参数表搜索是否打开，搜索 para_class 表 （这个参数程序没有写完，不搜索，作时可参数jtpump.com）
search_download_on = false			'下载是否打开，搜索 pro_img 表
search_bbs_on = false		'bbs搜索是否打开
search_ajax_pages = 3		'ajax方式搜索块上显示页面数为单数

'链接 #id# 为用数据库里的ID替换
search_display_page_view = page_name & "?" & page_querystingid & "=#id#"			'到页面的链接
search_display_pro_view	 = plist_name & "?" & pro_querystingid & "=#id#"	'到产品详细页面的链接
search_display_propara_view = "product_para.asp?" & this__lan2b & "pid=#id#" '参数表链接
'下载链接为直接的下载 <a href="img/xxx.pdf">title</a>
search_display_procate_view_str = array("pro_category","product.asp?cid=#id#","para_class","product_para.asp?pid=#id#","products","product_detail.asp?id=#id#")	'产品类链接根据v_search_pro视图里的from_table来判断。前面是表，后面是链接

'界面
'搜索结果在 #sresult ul.r
'对应的显示头样式 #sresult li.page h2, #sresult li.pro h2, #sresult li.down h2, #sresult li.bbs h2
'列表样式 #sresult ul.xxx li 及 #sresult ul.xxx li a #sresult ul.pro li a.para, #sresult ul.pro li a.procate
'对应的显示区域的定义 #sresult li.xxx
'更多的定义 #sresult  a.more
'提示没有信息的样式 #sresult h1
'搜索到的页面数量显示样式 #sresult [li.xxx] div.spnav ,#sresult [li.xxx] div.spnav a #sresult [li.xxx] div.spnav a.ac #sresult [li.xxx] div.spnav span (搜索数量说明)  #sresult [li.xxx] div.spnav ul(页码）

'-----------------------------------------------------------------------------------------------------
'''''搜索模式不改，位置不能换
select case request.querystring(search_range_url)
	case "all"
	search_mode = "advance"
	case "title"
	search_mode = "simple"
	case else
	search_mode = search_mode_default
end select
'-----------------------------------------------------------------------------------------------------

dim search_display_word(7)		'显示字符串，一般不用改，除了 search_display_word(0) 中的颜色
if this_lan = "en" Or front__language = "english" Then
	this_lan = "en"
	front__language = "english"
End if

if this_lan = "en" Then
search_display_more = "more"
search_display_word(0) = "<h1 align=center>No match</h1>"
search_display_word(1) = "<h1>Matched pages</h1>"
search_display_word(2) = "<h1>Matched products</h1>"
search_display_word(3) = "<h1>Matched thread</h1>"
search_display_word(4) = "<h1>Matched files</h1>"
page_alt_name = " Page: "
lan_cateid = "02"
Else
search_display_more =  "...更多"
	if search_mode = "advance" then
		search_display_word(0) = "<h1>没有匹配信息，换简单的关键词试试！</h1>"
	else
		search_display_word(0) = "<h1>抱歉，没有匹配信息。请换简单的关键词或者使用高级搜索试试！</h1>"
	end if
search_display_word(1) = "<h2>匹配页面</h2>"
search_display_word(2) = "<h2>匹配产品</h2>"
search_display_word(3) = "<h2>匹配帖子</h2>"
search_display_word(4) = "<h2>匹配下载</h2>"
'page_alt_name = "页码:"
page_alt_name = ""
lan_cateid = "01"
end if

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
function search_display_procate_view(search_display_procate_view_str,from_table)	'产品类对应链接
	for search_display_procate_view_str_i=lbound(search_display_procate_view_str) to ubound(search_display_procate_view_str) STEP 2
	if search_display_procate_view_str(search_display_procate_view_str_i) = from_table then
	search_display_procate_view = search_display_procate_view_str(search_display_procate_view_str_i + 1)
	exit for
	end if
	next
end function

function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function

Function class_name(ByVal tablename)
	Select Case tablename
	Case "products"
	class_name = "pro"
	Case "pro_category"
	class_name = "procate"
	Case "para_class"
	class_name = "para"
	End Select
End Function

Function p_class(ByVal rs_search_pagecount_i)
	If rs_search_pagecount_i = curpage Then
		p_class = " class=""ac"""
	Else
		p_class = ""
	End if
End Function

search_keywords = replace(VBsUnEscape(SafeRequest(search_keywords_url,0)),"　"," ")
search_keywords = Replace(search_keywords,"，"," ")
search_keywords = Replace(search_keywords,","," ")
search_type = SafeRequest(search_type_url,0)

dim search_page_on_2, search_pro_on_2, search_procate_on_2, search_bbs_on_2,search_download_on_2	'是否进行搜索
dim search_page_on_3, search_pro_on_3, search_procate_on_3, search_bbs_on_3,search_download_on_3	'搜索记录集标记
dim rs_search_1, rs_search_2, rs_search_3, rs_search_4, rs_search_5		'5个数据集
dim search_sql_1, search_sql_2, search_sql_3, search_sql_4,search_sql_5	'4个SQL查询
dim search_mode		'搜索范围
dim search_result	'输出结果

search_page_on_2 = false
search_pro_on_2 = false
search_procate_on_2 = false
search_bbs_on_2 = False
search_download_on_2 = False
search_page_on_3 = 0
search_pro_on_3 = 0
search_procate_on_3 = 0
search_bbs_on_3 = 0
search_download_on_3 = 0

if search_keywords = "" then		'如果没有关键词，则输出
	search_result = "<h1>您没有输入关键词，请输入！</h1>"
else	'如果有关键词

'''''搜索类，确定4个类是否搜索, 只有当search_page_on_2等为true时才搜索
if search_type = "" then
	if search_page_on = true then search_page_on_2 = true
	if search_pro_on = true then search_pro_on_2 = true
	if search_procate_on = true then search_procate_on_2 = true
	if search_bbs_on = true then search_bbs_on_2 = True
	if search_download_on = true then search_download_on_2 = true	
else
search_type_1 = split(search_type,",")

for search_type_1_i = lbound(search_type_1) to ubound(search_type_1)
	if search_type_1(search_type_1_i) = 1 then
		if search_page_on = true then search_page_on_2 = true
	end if

	if search_type_1(search_type_1_i) = 2 then
		if search_pro_on = true then search_pro_on_2 = true
		if search_procate_on = true then search_procate_on_2 = true
	end if

	if search_type_1(search_type_1_i) = 3 then
		if search_bbs_on = true then search_bbs_on_2 = true
	end if

	if search_type_1(search_type_1_i) = 4 then
		if search_download_on = true then search_download_on_2 = true
	end if
next
end If

'''''关键词
function search_keywords_sql(search_keywords, field)
'本函数将提交的关键字转成sql字符串， field为字段名,注意输出后的末尾没有空格
search_keywords_1 = split(search_keywords," ")
for search_keywords_1_i = lbound(search_keywords_1) to ubound(search_keywords_1)
	search_keywords_tmp = trim(search_keywords_1(search_keywords_1_i))
	if search_keywords_tmp <> "" then
		if search_keywords_sql <> "" then '不是第一个
 			search_keywords_sql = search_keywords_sql & " AND " & field & " LIKE '%" & search_keywords_tmp & "%'"
			else
			search_keywords_sql = search_keywords_sql & " " & field & " LIKE '%" & search_keywords_tmp & "%'"
		end if
	end if
next
end function

'''''sql
if search_mode = "simple" then
	if search_page_on_2 = true then search_sql_1 = "SELECT pageid, title FROM (page inner join V_Menusbasic on page.subid=V_Menusbasic.menuID) WHERE" & search_keywords_sql(search_keywords, "title") & " and  page.isscreen=false and V_Menusbasic.type_id=0 and left(V_Menusbasic.sort_id,2)='" & lan_cateid & "' GROUP BY pageid, title"
	if search_pro_on_2 = true then search_sql_2 = "SELECT id, name FROM (list inner join V_Menusbasic on list.id=V_Menusbasic.menuID) WHERE" & search_keywords_sql(search_keywords, "name") & " and V_Menusbasic.type_id=1   left(V_Menusbasic.sort_id,2)='" & lan_cateid & "' ORDER BY id ASC"
'	if search_procate_on_2 = true then search_sql_3 = "SELECT id, name" & this__lan & " as name, content,from_table FROM v_search_pro WHERE" & search_keywords_sql(search_keywords, "(name" & this__lan & " & content)") & " ORDER BY from_table, id ASC"
Else
	if search_page_on_2 = true then search_sql_1 = "SELECT pageid, title FROM (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id WHERE" & search_keywords_sql(search_keywords, "(page.title & page.content)") & " and  page.isscreen=false and area.cate_type_id=0 and left(area.cate_lcode,2)='" & lan_cateid & "' ORDER BY data_creat desc"
	if search_pro_on_2 = true then search_sql_2 = "SELECT id, name FROM (list inner join V_Menusbasic on list.id=V_Menusbasic.menuID) WHERE" & search_keywords_sql(search_keywords, "(name  & content & content2)") & " and V_Menusbasic.type_id=1 and left(V_Menusbasic.sort_id,2)='" & lan_cateid & "' ORDER BY id ASC"
'	if search_procate_on_2 = true then search_sql_3 = "SELECT id, name" & this__lan & " as name, content,from_table FROM v_search_pro WHERE" & search_keywords_sql(search_keywords, "(name" & this__lan & " & content)") & " ORDER BY from_table, id ASC"

end if
'response.write(search_sql_1 & "<br>" & search_sql_2 & "<br>" & search_sql_3 & "<br>" & search_sql_4 & "<br>")
'response.write search_sql_5

'''''rs生成 rs_search_1, rs_search_2, rs_search_3, rs_search_4 rs_search_5，得到最大值，得到
if search_page_on_2 = true Then
	set rs_search_1 = Server.CreateObject("ADODB.Recordset")
	rs_search_1.open search_sql_1,MM_conn_STRING,1,1
	if rs_search_1.recordcount > 0 then search_page_on_3 = 1
end if

if search_pro_on_2 = true And search_pro_split = True Then
	set rs_search_2 = Server.CreateObject("ADODB.Recordset")
	rs_search_2.open search_sql_2,MM_conn_STRING,1,1
	if rs_search_2.recordcount > 0 then search_pro_on_3 = 1
end If

if search_procate_on_2 = true then
	set rs_search_3 = Server.CreateObject("ADODB.Recordset")
	rs_search_3.open search_sql_3,MM_conn_STRING,1,1
	if rs_search_3.recordcount > 0 then search_procate_on_3 = 1
end if

if search_bbs_on_2 = true then
	set rs_search_4 = Server.CreateObject("ADODB.Recordset")
	rs_search_4.open search_sql_4,MM_conn_STRING,1,1
	if rs_search_4.recordcount > 0 then search_bbs_on_3 = 1
end If

if search_download_on_2 = true then
	set rs_search_5 = Server.CreateObject("ADODB.Recordset")
	rs_search_5.open search_sql_5,MM_conn_STRING,1,1
	if rs_search_5.recordcount > 0 then search_download_on_3 = 1
end if

dim search_display_mode	'显示模式，如果只有一个，则显示分页形式，如果大于1，显示简洁形式
search_display_mode = search_page_on_3 + search_pro_on_3 + search_procate_on_3 + search_bbs_on_3 + search_download_on_3
if search_pro_on_3 + search_procate_on_3 = 2 And search_pro_split = True then search_display_mode = search_display_mode - 1

'''''输出开始
if search_display_mode = 0 then'如果没有搜索结果
	search_result = search_display_word(0)
else
	dim search_display_rows	'////显示条数
	if search_display_mode = 1 then '确定显示条目数
		search_display_rows = search_display_pagerows
	else
		search_display_rows = search_display_sim
	end if

'///页面内容
	if search_page_on_3 = 1 then	'搜索到的页面结果
		rs_search_1_row = rs_search_1.recordcount	'搜索到的结果数
		search_result = "<li class=""page"">" & search_display_word(1) & "<ul class=""page"">"

		if search_display_mode = 1 AND rs_search_1_row > search_display_rows then '如果详细页，rs定位
	'			dim curpage				'当前数据页
				if request.querystring("p")="" then
					curpage = 1
				else
					curpage = cint(request("p"))
				end if
				rs_search_1.pagesize = search_display_rows
				rs_search_pagecount = rs_search_1.pagecount		'总页数
				rs_search_count = rs_search_1_row				'总数量

				if rs_search_1.pagecount < curpage then
					rs_search_1.absolutepage = rs_search_1.pagecount
					curpage=rs_search_1.pagecount
					else
					rs_search_1.absolutepage = curpage
				end if
		end if

		search_display_rows_i = 1
		while (not rs_search_1.eof) AND (search_display_rows_i <= search_display_rows)
			search_result = search_result & "<li><a href=""" & Replace(search_display_page_view,"#id#",rs_search_1("pageid")) & """>" & server.htmlencode(rs_search_1("title")) & "</a></li>"
			search_display_rows_i = search_display_rows_i +1
			rs_search_1.movenext()
		wend
		rs_search_1.close()
		set rs_search_1 = nothing
		search_result = search_result & "</ul>"

		if (rs_search_1_row > search_display_rows) then	'显示更多
			if search_display_mode > 1 then				'简洁
			search_result = search_result & "<a class=""more"" <a class=""more"" href=""javascript:void(null)"" onclick=""searchp("  & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',1" & ",1)"">" & search_display_more & "</a>"
			else										'详细显示，需要分页
				if front__language = "english" then
				search_result = search_result & "<div class=""spnav""><span>Total: " & rs_search_count & "" & page_alt_name
					else
				search_result = search_result & "<div class=""spnav""><span>" & rs_search_count & "条" & page_alt_name
				end if
				search_result = search_result & "</span><ul>" &  page_num_list(rs_search_pagecount,request("p"),search_ajax_pages,"cn","searchp(" & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',#page#" & ",1)","javascript:void(null)") & "</ul></div>"
			end if
		end if

		search_result = search_result & "</li>"
	end if

'///产品（包括产品类与产品）
	'产品类
	if search_procate_on_3 + search_pro_on_3 > 0 then search_result = search_result &  "<li class=""pro"">" & search_display_word(2) & "<ul class=""pro"">" '如果产品与类有结果

	if (search_procate_on_3 = 1) then	'搜索到的产品类结果
		rs_search_3_row = rs_search_3.recordcount

		If search_pro_split = False then'如果产品类与产品不分开，在一起显示
			if search_display_mode >= 1 AND rs_search_3_row > search_display_rows then '如果详细页，rs定位
		'			dim curpage				'当前数据页
					if request.querystring("p")="" then
						curpage = 1
					else
						curpage = cint(request("p"))
					end if
					rs_search_3.pagesize = search_display_rows
					rs_search_pagecount = rs_search_3.pagecount		'总页数
					rs_search_count = rs_search_3_row				'总数量

					if rs_search_3.pagecount < curpage then
						rs_search_3.absolutepage = rs_search_3.pagecount
						curpage=rs_search_3.pagecount
						else
						rs_search_3.absolutepage = curpage
					end if
			end If
		End if

		search_display_rows_i = 1
		while (not rs_search_3.eof) AND (search_display_rows_i <= search_display_rows)
			search_result = search_result & "<li><a class=""" & class_name(rs_search_3("from_table")) & """ href=""" & Replace(search_display_procate_view(search_display_procate_view_str,rs_search_3("from_table")),"#id#",rs_search_3("id")) & """>" & server.htmlencode(rs_search_3("name")) & "</a></li>"
			rs_search_3.movenext()
			If search_pro_split = false Then search_display_rows_i = search_display_rows_i +1
		wend
		rs_search_3.close()
		set rs_search_3 = Nothing

		search_result = search_result & "</ul>"
	end If

	if	 search_pro_split = False And rs_search_pagecount > 0 Then
		if (rs_search_3_row > search_display_rows) then	'显示更多
			if search_display_mode > 1 then				'简洁
			search_result = search_result & "<a class=""more"" <a class=""more"" href=""javascript:void(null)"" onclick=""searchp("  & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',1" & ",2)"">" & search_display_more & "</a>"
			else										'详细显示，需要分页
				if front__language = "english" then
					search_result = search_result & "<div class=""spnav""><span>Total: " & rs_search_count & " " & page_alt_name
					else
					search_result = search_result & "<div class=""spnav""><span>" & rs_search_count & "条" & page_alt_name
				end if
				search_result = search_result & "</span>" & page_num_list(rs_search_pagecount,request("p"),search_ajax_pages,"cn","searchp(" & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',#page#" & ",2)","javascript:void(null)") & "</div>"
			end if
		end if
	End If

	if	 search_pro_split = False Then search_result = search_result & "</li>"

'--产品
	if search_pro_split=True And search_pro_on_3 = 1 then		'搜索到的产品结果
		rs_search_2_row = rs_search_2.recordcount

		if search_display_mode = 1 AND rs_search_2_row > search_display_rows then '如果详细页，rs定位
	'			dim curpage				'当前数据页
				if request.querystring("p")="" then
					curpage = 1
				else
					curpage = cint(request("p"))
				end if
				rs_search_2.pagesize = search_display_rows
				rs_search_pagecount = rs_search_2.pagecount		'总页数
				rs_search_count = rs_search_2_row				'总数量

				if rs_search_2.pagecount < curpage then
					rs_search_2.absolutepage = rs_search_2.pagecount
					curpage=rs_search_2.pagecount
					else
					rs_search_2.absolutepage = curpage
				end if
		end if

		search_display_rows_i = 1
		while (not rs_search_2.eof) AND (search_display_rows_i <= search_display_rows)
			search_result = search_result & "<li><a href=""" & Replace(search_display_pro_view,"#id#",rs_search_2("id")) & """>" & server.htmlencode(rs_search_2("name")) & "</a></li>"
			rs_search_2.movenext()
			search_display_rows_i = search_display_rows_i +1
		wend
		rs_search_2.close()
		set rs_search_2 = Nothing
		
		search_result = search_result & "</ul>"

		if (rs_search_2_row > search_display_rows) then	'显示更多
			if search_display_mode > 1 then				'简洁
			search_result = search_result & "<a class=""more"" <a class=""more"" href=""javascript:void(null)"" onclick=""searchp("  & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',1" & ",2)"">" & search_display_more & "</a>"
			else										'详细显示，需要分页
				if front__language = "english" then
				search_result = search_result & "<div class=""spnav""><span>Total: " & rs_search_count & " " & page_alt_name
					else
				search_result = search_result & "<div class=""spnav""><span>" & rs_search_count & "条" & page_alt_name
				end If
				search_result = search_result & "</span><ul>" & page_num_list(rs_search_pagecount,request("p"),search_ajax_pages,"cn","searchp(" & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',#page#" & ",2)","javascript:void(null)") & "</ul></div>"
			end if
		end if

		search_result = search_result & "</li>"
	end if
'///论坛
	if search_bbs_on_3 = 1 then		'搜索到的论坛帖子结果
		rs_search_4_row = rs_search_4.recordcount
		search_result = search_result &  "<li class=""bbs"">" & search_display_word(3) & "<ul class=""bbs"">"

		if search_display_mode = 1 AND rs_search_4_row > search_display_rows then '如果详细页，rs定位
	'			dim curpage				'当前数据页
				if request.querystring("p")="" then
					curpage = 1
				else
					curpage = cint(request("p"))
				end if
				rs_search_4.pagesize = search_display_rows
				rs_search_pagecount = rs_search_4.pagecount		'总页数
				rs_search_count = rs_search_4_row				'总数量

				if rs_search_4.pagecount < curpage then
					rs_search_4.absolutepage = rs_search_4.pagecount
					curpage=rs_search_4.pagecount
					else
					rs_search_4.absolutepage = curpage
				end if
		end if

		for search_display_rows_i = 1 to search_display_rows
			if not rs_search_4.eof then
				search_result = search_result & "<li style='background-image: url(icon/emot/" & rs_search_4("icon") & ".gif);background-repeat: no-repeat;background-position: center top;padding-left:12px'><a href=""bbsinfo.asp?title_id=" & rs_search_4("id") & """>" & server.htmlencode(rs_search_4("title")) & "</a><li>"
				rs_search_4.movenext()
			end if
		Next
		search_result = search_result & "</ul>"

		if (rs_search_4_row > search_display_rows) then	'显示更多
			if search_display_mode > 1 then				'简洁
			search_result = search_result & "<a class=""more"" <a class=""more"" href=""javascript:void(null)"" onclick=""searchp("  & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',1" & ",3)"">" & search_display_more & "</a>"
			else										'详细显示，需要分页
				if front__language = "english" then
				search_result = search_result & "<div class=""spnav""><span>Total: " & rs_search_count & " " & page_alt_name
					else
				search_result = search_result & "<div class=""spnav""><span>" & rs_search_count & "条" & page_alt_name
				end if
				search_result = search_result & "</span><ul>" & page_num_list(rs_search_pagecount,request("p"),search_ajax_pages,"cn","searchp(" & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',#page#" & ",3)","javascript:void(null)") & "</ul></div>"
			end if
		end if

		search_result = search_result & "</li>"
		rs_search_4.close()
		set rs_search_4 = nothing
	end if

'///下载内容
	if search_download_on_3 = 1 then	'搜索到的页面结果
		rs_search_5_row = rs_search_5.recordcount	'搜索到的结果数
		search_result = search_result & "<li class=""down"">" & search_display_word(4) & "<ul class=""down"">"

		if search_display_mode = 1 AND rs_search_5_row > search_display_rows then '如果详细页，rs定位
	'			dim curpage				'当前数据页
				if request.querystring("p")="" then
					curpage = 1
				else
					curpage = cint(request("p"))
				end if
				rs_search_5.pagesize = search_display_rows
				rs_search_pagecount = rs_search_5.pagecount		'总页数
				rs_search_count = rs_search_5_row				'总数量

				if rs_search_5.pagecount < curpage then
					rs_search_5.absolutepage = rs_search_5.pagecount
					curpage=rs_search_5.pagecount
					else
					rs_search_5.absolutepage = curpage
				end if
		end if

		search_display_rows_i = 1
		while (not rs_search_5.eof) AND (search_display_rows_i <= search_display_rows)
			search_result = search_result & "<li><a href=""img/" & rs_search_5("img") & """>" & server.htmlencode(rs_search_5("title")) & "</a></li>"
			search_display_rows_i = search_display_rows_i +1
			rs_search_5.movenext()
		wend
		rs_search_5.close()
		set rs_search_5 = nothing
		search_result = search_result & "</ul>"

		if (rs_search_5_row > search_display_rows) then	'显示更多
			if search_display_mode > 1 then				'简洁
			search_result = search_result & "<a class=""more"" <a class=""more"" href=""javascript:void(null)"" onclick=""searchp("  & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',1" & ",4)"">" & search_display_more & "</a>"
			else										'详细显示，需要分页
				if front__language = "english" then
				search_result = search_result & "<div class=""spnav""><span>Total: " & rs_search_count & " " & page_alt_name
					else
				search_result = search_result & "<div class=""spnav""><span>" & rs_search_count & "条" & page_alt_name
				end if
				search_result = search_result & "</span><ul>" & page_num_list(rs_search_pagecount,request("p"),search_ajax_pages,"cn","searchp(" & "'" & server.urlencode(request.querystring("keywords")) & "','" & request.querystring(search_range_url)  & "',#page#" & ",4)","javascript:void(null)") & "</ul></div>"
			end if
		end if

		search_result = search_result & "</li>"
	end If
	
end if'输出结束


end if	'如果有关键词

response.write("<ul class=""r"">" & search_result & "</ul>")	'输出搜索结果
%>