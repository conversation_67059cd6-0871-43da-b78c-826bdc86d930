<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<!--#include file="_config.asp" -->
<!--#include file="_extend.asp" --><%
page_title ="招聘职位"

Dim page_position_str
page_position_str = "list," & 18
page_area_lcode = "0106"
%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><%=page_title%></title>
<link rel="stylesheet" type="text/css" href="layout.css" />
<script type="text/javascript" src="common.js"></script>
<script type="text/javascript" src="portalclient.js"></script>
</head>
<body id="P06">
<div class="page_wrapper">
	<div class="logo">
		<a href="default.asp" id="logo"><img src="images/hualongzg_logo.jpg" width="165" height="41" alt="hualongzg logo" /></a>
		<div id="language">
			<a href="#" class="en">English</a><a href="default.asp" class="cn">中文</a>
		</div>
       <form method="get" action="#" id="frmsearch">
          <fieldset>
			 <input type="text" name="keywords" id="searchArea" value="关键词"  />
			 <input id="searchGo" type="submit" class="go" value="" />
          </fieldset>
		</form>
	</div>
	<div class="flash"></div>
	<div class="navigator">
		<ul><li><a href="default.asp">首　　页</a></li><%=nav_tree("01",0)%></ul>
	</div>
	<div class="content_wrapper">
		<div class="sub_navigator">
			<h2>人才招聘</h2>
			<ul><%=nav_tree(page_area_lcode,0)%></ul>
		</div>
		<div class="content">
			<div class="crumb"><p><span>首页</span></p><%=pro_path(page_area_lcode)%></div>
			<h1><span><%=page_title%></span></h1>
			<div class="content_in">
<table class="hrposition">
<thead>
	<tr>
		<td class="td0">职位</td>
		<td class="td1">招聘人数</td>
		<td class="td2">职位要求</td>
		<td class="td3"></td>
	</tr>
</thead>
<tbody><%
Set rs = Server.CreateObject("ADODB.Recordset")
sql = "select name,this_amount,id,complex_descript from jobs where is_cover=false and delete_type=false and stop=false order by id asc"
rs.open sql,MM_conn_STRING,1,1
i=1
While Not rs.eof
	If Trim(rs("complex_descript")) <> "" then
		jobscon = replace(Replace(HTMLEncode(rs("complex_descript")),vbCrLF,"<br />"),"  ","&nbsp;&nbsp;")
	Else
		jobscon = ""
	End if
If i=2 Then i=0
If i=0 Then
	liclstr=" class=""le2"""
Else
	liclstr=""
End If
If rs("this_amount") = 0 Then
	this_amount = "若干"
Else
	this_amount = rs("this_amount")
End if
%>
	<tr<%=liclstr%>><td><%=rs("name")%></td><td><%=this_amount%></td><td><%=jobscon%></td><td><a href="submit.asp?id=<%=rs("id")%>">应聘</a></td></tr>
<%
rs.movenext()
i=i+1
wend
rs.close()
Set rs=nothing
%>
</tbody>
</table>	
			</div>
		</div>
	</div>
	<div class="copyright"><p><%=front_copy%></p></div>
</div>
</body>
</html>