<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">系统发布管理</h1>
<style type="text/css">
/* <![CDATA[ */
#frmrelease {width:620px;border:1px #ccc solid}
#frmrelease p{padding:3px;border-bottom:1px #ccc dotted;}
#frmrelease	label{width:100px;display:block;float:left}
#submit{margin-left:100px;width:150px;height:25px}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
	function reno(obj){
		if(obj.value == 0){
			$('nore').style.display = "block";
			$('submit').style.display = "block";
		}else{
			$('nore').style.display = "block";
			$('submit').style.display = "block";
		}
	}
//]]>
</script>
<%
If request.Form("action") ="ok" Then
set rsRE = Server.CreateObject("ADODB.Recordset")
sql = "SELECT title,content,is_open  FROM other WHERE name = 'this_release'"
rsRE.open sql,MM_conn_String,3,2
rsRE("is_open") = request.Form("re")
rsRE("title") = request.Form("title")
rsRE("content") = request.Form("content")
rsRE.update
rsRE.close()
Set rsRE=Nothing
str= "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Strict//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"">" & vbCrLf &_
"<html xmlns=""http://www.w3.org/1999/xhtml"">" & vbCrLf &_
"<head>" & vbCrLf &_
"<meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />" & vbCrLf &_
"<meta http-equiv=""Content-Language"" content=""zh-CN"" />" & vbCrLf &_
"<meta name=""Author"" content=""www.soundjet.net"" />" & vbCrLf &_
"<title>" & request.Form("title") & "</title>" & vbCrLf &_
"<style type=""text/css"">" & vbCrLf &_
"/* <![CDATA[ */" & vbCrLf &_
"*{padding:0;margin:0;font-size:15px;line-height:120%;font-family: ""黑体"", ""Arial Black"", ""Simhei"";}" & vbCrLf &_
"body{text-align:center;vertical-align:middle;}" & vbCrLf &_
"dl{width:516px;height:160px;border:1px solid #999;margin-top:220px;text-align:left;background:url(images/logo.gif) no-repeat right 10px;}" & vbCrLf &_
"dt{font-size:20px;padding:10px;color:#001f66;letter-spacing:4px;height:40px}" & vbCrLf &_
"dd{padding-left:80px;padding-right:35px;position:relative;height:100px}" & vbCrLf &_
"a{font-family:Arial;display:block;width:120px;height:25px;color:#fff;font-size:14px;line-height:25px;text-align:center;position:absolute;bottom:13px;left:30px;text-decoration:none;background-color:#0e3c69;border:1px #333 solid}" & vbCrLf &_
"a:hover{color:#0e3c69;border:1px #0e3c69 solid;background-color:#fff}" & vbCrLf &_
"/* ]]> */ " & vbCrLf &_
"</style>" & vbCrLf &_
"</head>" & vbCrLf &_
"<body>" & vbCrLf &_
"<dl id=""pre"">" & vbCrLf &_
"	<dt>" & vbCrLf &_
request.Form("title") & vbCrLf &_
"	</dt>" & vbCrLf &_
"	<dd>" & vbCrLf &_
"		<pre>" & request.Form("content") & "</pre>" & "<a href=""default.asp"">点击预览</a>" & vbCrLf &_
"	</dd>" & vbCrLf &_
"</dl>" & vbCrLf &_
"</body>" & vbCrLf &_
"</html>"

If request.Form("re") = "1" Then	'删除index.htm文档
	Call deletefile2("../index.htm")
Else	'写index.htm文档
	Call SaveToFile(str,"../index.htm")	
End if

response.redirect("release.asp")
End If

set rsRE = Server.CreateObject("ADODB.Recordset")
sql = "SELECT title,content,is_open  FROM other WHERE name = 'this_release'"
rsRE.open sql,MM_conn_String,1,1
If rsRE("is_open") = True Then
	str_is = " checked=""checked"""
	str_no = ""
Else
	str_is = ""
	str_no = " checked=""checked"""
End if
%>
<form method="post" action="#" id="frmrelease" name="frmrelease">
<p><input type="radio" name="re" value="1"<%=str_is%> onclick="reno(this)" />发布 <input type="radio" name="re" value="0"<%=str_no%> onclick="reno(this)" />不发布</p>
<div id="nore"<%If rsRE("is_open") = True Then response.write(" style=""display:none""")%>>
	<p><label>不发布原因</label><input type="text" id="title" style="width:500px" name="title" value="<%=rsRE("title")%>" /></p>
	<p><label>详细说明</label><textarea id="content" name="content" style="width:500px;height:100px"><%=rsRE("content")%></textarea></p>
</div>
	<p><input type="submit" id="submit" name="submit" value="提交" /><input type="hidden" name="action" value="ok" /></p>
</form>
<%
rsRE.close()
Set rsRE =Nothing
%>
<!--#include file ="_bottom.asp"-->