<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
Function finddir(filepath)
	finddir=""
	for i=1 to len(filepath)
	if left(right(filepath,i),1)="/" or left(right(filepath,i),1)="\" then
	  abc=i
	  exit for
	end if
	next
	if abc <> 1 then
	finddir=left(filepath,len(filepath)-abc+1)
	end if
end Function
%>
<%
id=Request("id")
error=Request("error")
stat_help_t = "帮助"
select case id
case "a01"
stat_help_t = "日志过滤"
case "a02"
stat_help_t = "数据库减肥"
case "a03"
stat_help_t = "IP数据库更新"
end select
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center><tr><td class=td014><font class=p14><b><%=(stat_help_t)%></b></font></td><%if error <> "" then%><td class=td012 align=right><font color=ff0000><%=(error)%></font></td><%end if%></tr>
</table>
<br>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
<tr><td class=td014><%select case id
case ""%><li><a href="stat_help.asp?id=001">自定义统计条件应该怎样填写呢？</a>
	<li><a href="stat_help.asp?id=002">怎样保存自定义检索分析的检索条件？</a>
	<li><a href="stat_help.asp?id=003">怎样修改已经保存的检索条件？</a>
<%case "001"%>
<u>自定义统计的条件怎样填写？</u><br><br>
　　时 间 段 ：起止日期中的年、月、日都必须是数字；注意截止日期必须在开通统计日期之后。<br><br>
　　IP 地 址：支持模糊查询，例如在IP地址项中输入“202.”，则可查到IP地址是“**************”或者“************”这样的访问记录的统计分析。<br><br>
　　来自地区：与IP地址一样，也支持模糊查询，比如输入“广东”，就可以查询来自广东的访问者的统计信息。<br><br>
　　客 户 端：从下拉表单选操作系统和浏览器，也可以接在文本框中输入，支持模糊查询，比如在文本框中输入win，则可以查到Windows 2000、Win XP等所有Windows系统的统计分析。<br><br>
　　来自网页：特定来路的统计分析，如要查从yahoo里来的，可以输入“yahoo.com”（注意，不要输入www.yahoo.com，因为搜索引擎的结果页面一般不是以www.开头的），支持模糊查询。<br><br>
　　被访网页：目标页的统计分析，支持模糊查询。<br><br>
　　输出类型：至少要选择一项。<br>
<%case "002"%>
<u>怎样保存自定义检索分析的检索条件？</u><br><br>
　　在进行了自定义统计后，在检索结果页面的底部有一个名为“保存本次检索条件”，你可为要保存的条件取名并加入备注来保存，以便以后使用。<br><br>
<%case "003"%>
<u>怎样修改已经保存的检索条件？</u><br><br>
　　自定义检索条件不要太多，要取个适合的名称，并加上备注，以防混淆。在“自定义统计”页面可随时删除自定义检索条件。<br><br>
　　要“修改”已经定义好的检索条件，只要按新的检索条件搜索，再以原有名字保存，并选择“同名时覆盖”即可。<br>
<%case "a01"%>
　　日志过滤功能从访问数据库里过滤指定的记录，比如从访问数据库里过滤自己的访问记录，或者指定时间的访问记录，获得更有商业价值的统计、分析资料。<br><br>
　　过滤的记录并没有从数据库里删除，可以随时取消过滤。<br><br>
　　可以定义、使用多个过滤条件。<br><br>
　　<font class=p12 color=ff0000>如果需要该功能，请与本公司联系。</font>
<%case "a02"%>
　　当网站运行一段时间后，数据库里的记录越来越多，会导致网站性能变差，数据库减肥将从访问数据库里把指定时间段的详细记录删除，但备份被删除记录的统计与分析信息，并重新压缩数据库。这样不但提高网站的性能，同时保证了日志系统的统计信息的真实性。<br><br>
　　删除的记录在详细日志里不再出现。<br><br>
　　<font class=p12 color=ff0000>如果需要该功能，请与本公司联系。</font>
<%case "a03"%>
　　访问者地区的信息都是从IP数据库得到的。本公司不间断的更新IP数据库，最大限度的保证IP数据库的真实性。通过更新IP数据库，流量统计系统的访问者地区信息变得更加准确可靠。<br><br>
　　IP数据库更新是自动进行的。<br><br>
　　<font class=p12 color=ff0000>如果需要该功能，请与本公司联系。</font>
<%case else%>
没有与此相关的帮助信息，如果您是正常操作，请咨询迪博克公司。
<%end select%>
<p class="p1" align="right"><a href='javascript:history.back()'>继续</a></td>
<td class=td014 width="1" class="backs"><img src="images/touming.gif" width="1" height="1"></td>
  </tr>
</table>
<!--#include file="stat__bottom.asp"-->