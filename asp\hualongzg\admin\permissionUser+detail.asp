<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="../_md5.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">用户管理</h1>
<script LANGUAGE="JavaScript">
function check()
{
if (document.Form1.name.value=="")
{
alert("请输入姓名！")
document.Form1.name.focus()
document.Form1.name.select() 
return
}

if (document.Form1.username.value=="")
{
alert("请输入账号！")
document.Form1.username.focus()
document.Form1.username.select()
return
}
if (document.Form1.password.value=="")
{
alert("请输入密码！")
document.Form1.password.focus()
document.Form1.password.select() 
return
}

document.Form1.submit()
}
</SCRIPT>
<%
sub select_groupid(i)	'输出选择用户组
response.write("<select name='group_id' style='width:125px'>")
dim rs,sql
sql="select group_id,name from usergroup order by group_id"
Set rs= Server.CreateObject("ADODB.Recordset")
rs.open sql,MM_conn_STRING,1,1
while not rs.eof
response.write("<option value=" & rs("group_id"))
if rs("group_id") = i then response.write(" selected")
response.write ">" & rs("name") & "</option>"
rs.movenext()
wend
response.write("</select>")
rs.close()
set rs=nothing
end sub

call main()
set rs=nothing

sub main()
%>

<%
if request("action")="editsave" then 
call editsave()
elseif request("action")="add" then 
call add()
elseif request("action")="addsave" then 
call addsave()
else
call manager()
end if
%>
<p><%=body%></p>
<%
end sub

sub manager()
dim sql
dim rs
dim id
id=request("user_id")
sql="select admin.*,usergroup.name as groupname from admin inner join usergroup on usergroup.group_id = admin.group_id where user_id=" & cstr(id)
Set rs= Server.CreateObject("ADODB.Recordset")
rs.open sql,MM_conn_STRING,1,1
%>
<script language="JavaScript">
<!--
function CheckAll(form) {
for (var i=0;i<form.elements.length;i++) {
var e = form.elements[i];
if (e.name != 'chkall') e.checked = form.chkall.checked; 
}
}
//-->
</script>
<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" bordercolorlight=<%=back_menubackcolor0%>>
<FORM action="permissionUser+detail.asp?action=editsave&user_id=<%=id%>" method="post" name="Form1">
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>>&nbsp;&nbsp;用户信息修改</td>
</TR>
<TR> 
<TD align="center" height="28">姓名</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=name size="20" value=<%=rs("name")%>></font></TD>
</TR>
<TR> 
<TD align="center" height="28">账号</td>
<TD><font color="#FF0000">&nbsp;&nbsp;<input type=text name=username value=<%=rs("id")%> size="20"></font>
</TD>
</TR>
<TR>
<TD align="center" height="28">密码</TD>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=password size="20"></font> <font class=p12>*密码使用不可逆算法加密</font></TD>
</TR>
<TR>
<TD align="center" height="28">信箱</TD>
<TD>&nbsp;&nbsp;<input type=text name=email size="20" value=<%=rs("email")%>></TD>
</TR>
<tr>
<TD align="center" height="28">用户组</td>
<TD>&nbsp;&nbsp;<%if rs("this_right") =99 then
response.write("<input type=hidden name=group_id value=1>管理员组")
else
call select_groupid(rs("group_id"))
end if%></TD>
</TR><TR>
<TD colspan=2 height="28" align="center" bgcolor=<%=back_menubackcolor%>> 
<input type="button" value="修 改" onclick=check()>
</TD>
</TR>
</TABLE></FORM>
<%
rs.close
set rs=nothing
end sub
sub editsave()
'''''强制过滤一些常用字符开始
no_id_str = "1,12,123,1234,12345,123456,username,user,name,u_name,administrators,userid,adminuser,adminpass,adminname,user_name,admin_name,usr_n,usr,dw,nc,uid,admin,admin_user,admin_username,user_admin,adminusername"
no_pw_str ="1,12,123,1234,12345,123456,userpass,password,adminpassword,adminpass,user_pass,admin_password,user_password,user_pwd,adminpwd,admin_pass,admin_password"
no_id_str_1 = split(no_id_str,",")
no_pw_str_1 = split(no_pw_str,",")

if (request.form("username") = back_site) or (request.form("pw") = back_site) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?user_id=" & request.querystring("user_id") & "');</script>"
	Response.End
end if

for no_admin_i = lbound(no_id_str_1) to ubound(no_id_str_1)
	if request.form("username") = no_id_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?user_id=" & request.querystring("user_id") & "');</script>"
	Response.End
	end if
next

for no_admin_i = lbound(no_pw_str_1) to ubound(no_pw_str_1)
	if request.form("password") = no_pw_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?user_id=" & request.querystring("user_id") & "');</script>"
	Response.End
	end if
next

'''''强制过滤一些常用字符结束

set rs=server.createobject("adodb.recordset")
sql="select admin.*,usergroup.name as groupname from admin inner join usergroup on usergroup.group_id = admin.group_id where user_id="&request("user_id")
rs.open sql,MM_conn_STRING,3,3
rs("id")=request("username")
rs("pw")=md5(request("password"))
rs("group_id")=request("group_id")
rs("name")=request("name")
rs("email")=request("email")
rs.update
response.write "用户信息修改成功"
end sub
sub add()
%>
<script language="JavaScript">
<!--
function CheckAll(form) {
for (var i=0;i<form.elements.length;i++) {
var e = form.elements[i];
if (e.name != 'chkall') e.checked = form.chkall.checked; 
}
}
//-->
</script>
<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" bordercolorlight=<%=back_menubackcolor0%>>
<FORM action="permissionUser+detail.asp?action=addsave" method="post" name="Form1">
<TR> 
<TD align="left" height="28" colspan=2 bgcolor=<%=back_menubackcolor%>><b>增加用户</b></td>
</TR>
<TR> 
<TD align="center" height="28">姓名</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name="name" size="20"></font></TD>
</TR>
<TR> 
<TD align="center" height="28">账号</td>
<TD><font color="#FFFFFF">&nbsp;&nbsp;<input type=text name=username size="20"></font>
</TD>
</TR>
<TR>
<TD align="center" height="28">密码</TD>
<TD>&nbsp;&nbsp;<input type=text name=password size="20"></TD>
</TR>
<TR>
<TD align="center" height="28">信箱</TD>
<TD>&nbsp;&nbsp;<input type=text name=email size="20"> <font class=p12>*请使用本站信箱</font></TD>
</TR>
<TR> 
<TD align="center" height="28">用户组</td>
<TD>&nbsp;&nbsp;<%call select_groupid(0)%></TD>
</TR>
<TR>
<TD colspan=2 height="28" align="center" bgcolor=<%=back_menubackcolor%>> 
<input type="button" value="添 加" onclick=check()>
</TD>
</TR>
</TABLE></FORM>
<%
end sub
sub addsave()
'''''强制过滤一些常用字符开始
no_id_str = "1,12,123,1234,12345,123456,username,user,name,u_name,administrators,userid,adminuser,adminpass,adminname,user_name,admin_name,usr_n,usr,dw,nc,uid,admin,admin_user,admin_username,user_admin,adminusername"
no_pw_str ="1,12,123,1234,12345,123456,userpass,password,adminpassword,adminpass,user_pass,admin_password,user_password,user_pwd,adminpwd,admin_pass,admin_password"
no_id_str_1 = split(no_id_str,",")
no_pw_str_1 = split(no_pw_str,",")

if (request.form("username") = back_site) or (request.form("pw") = back_site) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?action=add');</script>"
	Response.End
end if

for no_admin_i = lbound(no_id_str_1) to ubound(no_id_str_1)
	if request.form("username") = no_id_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?action=add');</script>"
	Response.End
	end if
next

for no_admin_i = lbound(no_pw_str_1) to ubound(no_pw_str_1)
	if request.form("password") = no_pw_str_1(no_admin_i) then
	Response.Write "<script>alert('账号、密码不安全，不能用以下不安全的账号与密码\n\n账号：" & no_id_str  & "," & back_site & "\n密码：" &  no_pw_str & "," & back_site &"');window.location.href('permissionUser+detail.asp?action=add');</script>"
	Response.End
	end if
next

'''''强制过滤一些常用字符结束

set rs=server.createobject("adodb.recordset")
sql="select * from admin"
rs.open sql,MM_conn_STRING,3,3
rs.addnew
rs("id")=request("username")
rs("pw")=md5(request("password"))
rs("group_id")=request("group_id")
rs("name")=request("name")
rs("email")=request("email")
rs.update
response.redirect("permissionUser.asp")
end sub
%>

 
<!--#include file ="_bottom.asp"-->