<%'这个程序用来写父目录里的_marquee.asp文件，只能包含在同目录的open.asp里执行
dim fso,fdir,fdata, f, ts
'f11 =  & "test.asp"
set  fso = CreateObject("Scripting.FileSystemObject")
fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
set fdir = fso.getfile(fdir)
set fdir = fdir.parentfolder
set fdir = fdir.parentfolder								'获取父目录
fdata = fdir & "\" & "_marquee.asp"
fso.CreateTextFile(fdata)									'建立文件

Set f = fso.GetFile(fdata)
Set ts = f.OpenAsTextStream(2, -2)
%>
<%
rssMAIL__MMColParam = "openwin"
set rssMAIL = Server.CreateObject("ADODB.Recordset")
rssMAIL.ActiveConnection = MM_conn_STRING
rssMAIL.Source = "SELECT content FROM other WHERE name = '" + Replace(rssMAIL__MMColParam, "'", "''") + "'"
rssMAIL.CursorType = 0
rssMAIL.CursorLocation = 2
rssMAIL.LockType = 3
rssMAIL.Open()
rssMAIL_numRows = 0

if trim(rssMAIL("content")) <> "" then rssMAIL_tmp = replace(Replace(HTMLEncode(rssMAIL.Fields.Item("content").Value),vbCrLF,"<br>"),"  ","&nbsp;&nbsp;")

ts.write(rssMAIL_tmp)

rssMAIL.Close()
set rssMAIL = Nothing
ts.Close
set ts = nothing
%>