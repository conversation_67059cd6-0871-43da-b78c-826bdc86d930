<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("../admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<% If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
end if
%>

<%
' *** Update Record: set variables

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + request.form("MM_recordId") + "'"
  MM_editRedirectUrl = "open_2.asp"
  MM_fieldsStr  = "fmtitle|value|content|value|html|value|is_open|value|img|value|fmthumb|value|fmthis_position|value|fmalt|value"
  MM_columnsStr = "title|',none,''|content|',none,''|html|none,none,NULL|is_open|none,none,NULL|img|',none,''|thumb|none,none,NULL|this_position|none,none,NULL|alt|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(request.form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then%>
<!--#include file="contact_w.asp" -->
<%      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsMODPAGE4__MMColParam
rsMODPAGE4__MMColParam = "sdgc_open"
%>
<%
set rsMODPAGE4 = Server.CreateObject("ADODB.Recordset")
rsMODPAGE4.ActiveConnection = MM_conn_STRING
rsMODPAGE4.Source = "SELECT * FROM other WHERE name = '" + Replace(rsMODPAGE4__MMColParam, "'", "''") + "'"
rsMODPAGE4.CursorType = 0
rsMODPAGE4.CursorLocation = 2
rsMODPAGE4.LockType = 3
rsMODPAGE4.Open()
rsMODPAGE4_numRows = 0
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">屏蔽时代工程栏目</h1>
	<form ACTION="<%=MM_editAction%>" METHOD="POST" name="form1">
	  <table border="1" cellspacing="0" cellpadding="4" align=center>
		<tr> 
		  <td width="300"><font color=ff0000>是否打开时代工程栏目</font></td>
		  <td> 
			<% if rsMODPAGE4.Fields.Item("is_open").Value=0 then
response.write "否 <input type='radio' name='is_open' value='0' checked><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_open' value='1'>"
else
response.write "否 <input type='radio' name='is_open' value='0'><img src='/images/spacer.gif' width=40 height=1> 是 <input type='radio' name='is_open' value='1' checked>"
end if
%>
			<img src='/images/spacer.gif' width=40 height=1></td>
		</tr>
	<tr> 
		  <td width="300">&nbsp;</td>
		  <td width="100"> 
			<input type="submit" name="Submit" value="提交" onClick="MM_validateForm('fmtitle','','R');return document.MM_returnValue">
		  </td>
		</tr>

	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("name").Value %>">
	</form>
<!--#include file ="_bottom.asp"-->
<%
rsMODPAGE4.Close()
set rsMODPAGE4 = nothing
%>
