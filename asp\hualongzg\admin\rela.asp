<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">内容关联</h1>
<style type="text/css">
/* <![CDATA[ */
ul.rels{list-style-type:decimal;padding-top:10px;margin-left:40px}
ul.rels li a{padding:0 0 0 3px}
/* ]]> */ 
</style>

选择关联类型：
<ul class="rels"><%
set rs=server.createobject("adodb.recordset")
sql="select rels_name,id from rels_config order by id asc"
rs.open sql,MM_conn_STRING,1,1
while Not rs.eof
%>
	<li><a href="page+pro_rela.asp?id=<%=rs("id")%>"><%=rs("rels_name")%></a></li><%
rs.movenext()
wend
	%>
</ul>

<!--#include file ="_bottom.asp"-->