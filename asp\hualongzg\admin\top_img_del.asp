<% @LANGUAGE="VBSCRIPT" %>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
sub del_page_img(pageids,folder)
	'删除页面里的文件，参数为字符串pageid组,父目录名称（不带\）
	dim rs,sql,imgs,i
	imgs = ""
	set rs=server.createobject("adodb.recordset") 
	sql="select img, img_big from img WHERE id IN (" & pageids & ")"
	rs.open sql,MM_conn_STRING,1,1 
	while not rs.eof
		for i=0 to 1
			if rs(i) <> "" AND not isnull(rs(i)) then
				if imgs <> "" then
				imgs = imgs & "|" & rs(i)
				else
				imgs = rs(i)
				end if
			end if
		next
		rs.movenext()
	wend
	rs.close()
	set rs=nothing

	if imgs <> "" then
		dim File,fdir,ImagePath
		Set File = CreateObject("Scripting.FileSystemObject")
		fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
		set fdir = File.getfile(fdir)
		set fdir = fdir.parentfolder
		set fdir = fdir.parentfolder								'获取父父目录

		dim imgs_str
		imgs_str = split(imgs,"|")
		for i = lbound(imgs_str) to ubound(imgs_str)
			ImagePath = fdir & "\" & folder & "\" & imgs_str(i)
			if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
		next
	end if'删除旧图或者媒体文件
end sub

dim sql
page_id = Request("imgid")
if page_id <>"" then
	call del_page_img(page_id,"img")
	sql="DELETE FROM img WHERE id IN "   
	sql=sql &"(" & page_id & ")"
	set Command1 = Server.CreateObject("ADODB.Command")
	Command1.ActiveConnection = MM_conn_STRING
	Command1.CommandText = sql
	Command1.Execute()
	Response.Redirect("top_img.asp?id=" & request.querystring("id"))
else 
	Response.redirect("top_img.asp?id=" & request.querystring("id"))
end if %>