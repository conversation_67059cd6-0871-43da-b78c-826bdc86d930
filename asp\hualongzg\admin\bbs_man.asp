<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<% if request.querystring("action") = "" then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + Request.Form("MM_recordId") + "'"
  MM_editRedirectUrl = "bbs_man.asp"
  MM_fieldsStr  = "name|value|title|value|this_memo|value"
  MM_columnsStr = "name|',none,''|title|',none,''|this_memo|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsBBSPRO__MMColParam
rsBBSPRO__MMColParam = "bbs_pro"
if (Request("MM_EmptyValue") <> "") then rsBBSPRO__MMColParam = Request("MM_EmptyValue")
%>
<%
set rsBBSPRO = Server.CreateObject("ADODB.Recordset")
rsBBSPRO.ActiveConnection = MM_conn_STRING
rsBBSPRO.Source = "SELECT * FROM other WHERE name = '" + Replace(rsBBSPRO__MMColParam, "'", "''") + "'"
rsBBSPRO.CursorType = 0
rsBBSPRO.CursorLocation = 2
rsBBSPRO.LockType = 3
rsBBSPRO.Open()
rsBBSPRO_numRows = 0
else '更新规则
%>
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "bbs_board"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "bbs_man.asp"
  MM_fieldsStr  = "id|value|board_name|value|descrip|value|board_name_en|value|descrip_en|value|this_right|value"
  MM_columnsStr = "id|',none,''|board_name|',none,''|descrip|',none,''|board_name_en|',none,''|descrip_en|',none,''|this_right|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
set rsBBSBOARD = Server.CreateObject("ADODB.Recordset")
rsBBSBOARD.ActiveConnection = MM_conn_STRING
rsBBSBOARD.Source = "SELECT * FROM bbs_board ORDER BY id ASC"
rsBBSBOARD.CursorType = 0
rsBBSBOARD.CursorLocation = 2
rsBBSBOARD.LockType = 3
rsBBSBOARD.Open()
rsBBSBOARD_numRows = 0
%>
<%
Dim Repeat1__numRows
Repeat1__numRows = 2
Repeat1__numRows_1 = Repeat1__numRows
Dim Repeat1__index
Repeat1__index = 0
rsBBSBOARD_numRows = rsBBSBOARD_numRows + Repeat1__numRows
end if
%>

<!--#include file ="_head.asp"-->
<% if request.querystring("action") ="" then %>
<h1 class="b_ch1">论坛规则</h1>
	<form ACTION="<%=MM_editAction%>" METHOD="POST" name="form1">
	  <input type="hidden" name="name" value="bbs_pro">
	  <table border=0 width=580 align=center>
		<tr> 
		  <td width=100>规则名称</td>
		  <td> 
			<input class=i500 type="text" name="title" value="<%=(rsBBSPRO.Fields.Item("title").Value)%>" size="60">
		  </td>
		</tr>
		<td width=100 valign=top>规则内容</td>
		<td> 
		  <textarea class=i500 name="this_memo" rows="10"><%=(rsBBSPRO.Fields.Item("this_memo").Value)%></textarea>
		</td>
		</tr>
		<tr><td colspan=2>&nbsp;</td></tr>
			<tr> 
		  <td>&nbsp;</td>
		  <td> 
			<input type="submit" value="更新">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsBBSPRO.Fields.Item("name").Value %>">
	</form>
<%else%>
<h1 class="b_ch1">论坛板块</h1>
<table width=500 border=1 cellspacing=0 cellpadding=3 class=table0 bgcolor=cccccc align=center>
<tr bgcolor=<%=(back_menubackcolor)%> align=center><td nowrap class=td0>板块名称</td><td align=center class=td0>板块说明</td><td colspan=2 align=center class=td0>权限</td></tr>
<% 
While ((Repeat1__numRows <> 0) AND (NOT rsBBSBOARD.EOF)) 
%>
<form name="form1" method="POST" action="<%=MM_editAction%>">
  <input type="hidden" name="id" value="<%=(rsBBSBOARD.Fields.Item("id").Value)%>">
<tr bgcolor=cccccc>
  <td width=15 class=td0><input type="text" size=10 name="board_name" value="<%=(rsBBSBOARD.Fields.Item("board_name").Value)%>"></td>
 <td width=300 class=td0><input type="text" class=i300 name="descrip" value="<%=(rsBBSBOARD.Fields.Item("descrip").Value)%>"></td>
<td class=td0 class=p12 rowspan=2 class=td0 nowrap bgcolor=cccccc><font class=p12><!-- 加密<input type="radio" name="this_right" value="1" <% if rsBBSBOARD.Fields.Item("this_right").Value = 1 then response.write "checked"%>><br> -->解密<input type="radio" name="this_right" value="0" <% if rsBBSBOARD.Fields.Item("this_right").Value = 0 then response.write "checked"%>></font>
</td>
  <td class=td0 rowspan=2 bgcolor=cccccc><input type="submit" name="Submit" value="确定"></td>
</tr>
<!-- <tr  bgcolor=cccccc>
  <td class=td0><font class=p12>英文</font></td><td class=td0 width=15><input type="text" size=10 name="board_name_en" value="<%=(rsBBSBOARD.Fields.Item("board_name_en").Value)%>"></td>
 <td class=td0 width=300><input type="text" class=i300 name="descrip_en" value="<%=(rsBBSBOARD.Fields.Item("descrip_en").Value)%>"></td>
</tr> -->
  <input type="hidden" name="MM_update" value="true">
  <input type="hidden" name="MM_recordId" value="<%= rsBBSBOARD.Fields.Item("id").Value %>">
</form>
<tr><td class=td0 colspan=2>&nbsp;</td></tr>
<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsBBSBOARD.MoveNext()
Wend
%>
</table><br><br>
<font class=p12 color=ff0000>注：共 <%=Repeat1__numRows_1%> 个板块</font>
<%end if %>
<!--#include file ="_bottom.asp"--><% if request.querystring("action") = "" then
rsBBSPRO.Close()
end if
%>
<% if request.querystring("action") = "lm" then
rsBBSBOARD.Close()
end if
%>
