<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
marquee_name_on = false	'是否打开说明
marquee_url_on  = true	'是否打开URL

' *** Edit Operations: (Modified for File Upload) declare variables
Dim MM_editAction
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (request.querystring <> "") Then
  MM_editAction = MM_editAction & "?" & request.querystring
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: (Modified for File Upload) set variables

If (CStr(request.form("MM_insert")) = "form1") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "marquee"
  MM_editRedirectUrl = "marquee.asp"
  MM_fieldsStr  = "this_des|value|name|value|this_url|value"
  MM_columnsStr = "this_des|',none,''|name|',none,''|this_url|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(request.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And request.querystring <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And request.querystring <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & request.querystring
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & request.querystring
    End If
  End If

End If
%>
<%
' *** Insert Record: (Modified for File Upload) construct a sql insert statement and execute it

Dim MM_tableValues
Dim MM_dbValues

If (CStr(request.form("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End If
    MM_tableValues = MM_tableValues & MM_columns(MM_i)
    MM_dbValues = MM_dbValues & MM_formVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
If (CStr(Request.form("MM_update")) = "form2" And CStr(Request("del_marquee")) <> "on") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "marquee"
  MM_editColumn = "ID"
  MM_recordId = "" + Request.form("MM_recordId") + ""
  MM_editRedirectUrl = "marquee.asp"
  MM_fieldsStr  = "this_on|value"
  MM_columnsStr = "this_on|none,1,0"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And request.querystring <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And request.querystring <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & request.querystring
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & request.querystring
    End If
  End If

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(MM_i) & " = " & MM_formVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
' *** Delete Record: declare variables

if (CStr(Request("del_marquee")) = "on" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "marquee"
  MM_editColumn = "ID"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "marquee.asp"

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If
%>
<%
' *** Delete Record: construct a sql delete statement and execute it

If (CStr(Request("del_marquee")) = "on" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the delete
'	if request("oldimg") <> "" then'删除旧文件
'     Set File = CreateObject("Scripting.FileSystemObject")
'      ImagePath = Server.MapPath("\img\")
'      ImagePath = ImagePath & "\" & Request("oldimg")
'	  if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
'	end if'删除旧文件

    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsMIDI
Dim rsMIDI_numRows

Set rsMIDI = Server.CreateObject("ADODB.Recordset")
rsMIDI.ActiveConnection = MM_conn_STRING
rsMIDI.Source = "SELECT * FROM marquee ORDER BY ID DESC"
rsMIDI.CursorType = 0
rsMIDI.CursorLocation = 2
rsMIDI.LockType = 1
rsMIDI.Open()

rsMIDI_numRows = 0
%>
<%
Dim Repeat1__numRows
Dim Repeat1__index

Repeat1__numRows = -1
Repeat1__index = 0
rsMIDI_numRows = rsMIDI_numRows + Repeat1__numRows
%>
<html>
<head>
<script language="JavaScript">
<!--

//-->
</script>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">动态滚动条</h1>
<table width=500 border=0 cellpadding=3 align=center>
<script language="JavaScript">
function VerifyInput()
{
	if (document.form1.name.value == "")
	{
	alert("输入说明，方便以后使用！");
	document.form1.name.focus();
	return false;
	}

	if (document.form1.this_des.value == "")
	{
	alert("必须输入内容！");
	document.form1.this_des.focus();
	return false;
	}

}
</script>
<form action="<%=MM_editAction%>" method="POST" name="form1" onsubmit="return VerifyInput()">
<tr align=center bgcolor=<%=(back_menubackcolor)%>><%if marquee_name_on = true then%><td>说明</td><%end if%><td nowrap>滚动条内容</td><%if marquee_url_on = true then%><td>链接</td><%end if%><td>&nbsp;</td></tr>  
<tr><%if marquee_name_on = true then%><td><input name="name" type="text" size=20></td><%end if%><td nowrap>
  <input type="text" name="this_des" size=60%></td><%if marquee_url_on = true then%><td><input name="this_url" type="text" size=20></td><%end if%><td width=20>
  <input type="submit" name="Submit2" value="增加">
  <input type="hidden" name="MM_insert" value="form1"></td></tr>
</form>
</table><br>
<table width=500 border=0 cellpadding=3 align=center>
<tr align=center bgcolor=<%=(back_menubackcolor)%>><%if marquee_name_on = true then%><td>说明</td><%end if%><td>内容</td><%if marquee_url_on = true then%><td>链接</td><%end if%><td nowrap>打开</td><td nowrap>删除</td><td>&nbsp;</td></tr>
<% 
While ((Repeat1__numRows <> 0) AND (NOT rsMIDI.EOF)) 
%>
<form name="form2" method="POST" action="marquee.asp">
<tr><%if marquee_name_on = true then%><td nowrap class=p12 align=center><%=(rsMIDI.Fields.Item("name").Value)%></td><%end if%><td>
  <%=(HTMLEncode(rsMIDI.Fields.Item("this_des").Value))%> </td><%if marquee_url_on = true then%><td><%=rsMIDI.Fields.Item("this_url").Value%></td><%end if%><td width=20>
  <input type="checkbox" name="this_on" value="checkbox" <%if rsMIDI.Fields.Item("this_on").Value=true then response.Write("checked")%>>
</td><td width=20><input type="checkbox" name="del_marquee" onClick="GP_popupConfirmMsg('确定吗？');return document.MM_returnValue"></td><td width=20>
  <input type="submit" name="Submit" value="确认">
  <input type="hidden" name="MM_update" value="form2">
  <input type="hidden" name="MM_recordId" value="<%= rsMIDI.Fields.Item("ID").Value %>">
  <input type="hidden" name="oldimg" value="<%= rsMIDI.Fields.Item("name").Value %>">
</td></tr></form>
<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsMIDI.MoveNext()
Wend
%>
</table>
<!--#include file ="_bottom.asp"-->
<%
rsMIDI.Close()
Set rsMIDI = Nothing
%>