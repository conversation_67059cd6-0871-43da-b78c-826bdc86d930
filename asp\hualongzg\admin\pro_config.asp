<%
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'									产品系统参数
'-------------------------------------------------------------------------------------------
dim pro_trademark_on					
dim cate_nomix							
dim pro_english_on							
dim cate_descript						
dim cate_img_on, cate_img_width, cate_imgwidth	'类特征图，如果true，则图片最大宽、高
dim cate_del_confirm
dim cate_select_size
dim cate_level_max

dim filterjp_on						
dim watermark_on						
dim img_big_on,img_big2_on,thumb_pro_on	
dim is_newest_on						
dim is_new_on							
dim is_top_on
dim is_modify_cate			'是否在编辑类状态，如果在此状态，允许删除有子类和产品的类别

'产品类
cate_top_add = true			'顶级类汪允许添加
pro_trademark_on = 1			'品牌，有则为1，否则为0
cate_nomix = true				'是否严格类，如果true，则不允许同级下类、产品混合
pro_english_on = false			'是否打开英文属性
cate_descript = false		'是否打开详细属性
cate_img_on = false			'类特征图，如果true，则图片最大宽cate_img_width、高cate_imgwidth
cate_del_confirm = true		'删除类前要等无产品才能删除
cate_select_size = 30		'类列表、产品列表高度
cate_level_max = 3			'限制类级别，一般最大4级
'产品
watermark_on = false			'产品图是否打上水印
filterjp_on = true			'产品是否过滤日文片假名，在windows2000上要过滤，否则搜索里会出现内存泄漏。
is_newest_on = false			'产品是否在首页新品里显示
is_new_on = false				'产品是否推荐新品
is_top_on = false				'产品是否在页面顶部显示
img_big_on = true				'产品的三种图片是否打开
img_big2_on =false
thumb_pro_on = false
thumb_maxwidth = 150			'特征图最大宽度（特征图都是由产品代表图片自动生成的）
thumb_maxheight= 150			'特征图最大高度
img_maxwidth= 500				'产品代表图片最大宽度与高度，如果为0，则不限制
img_maxheight= 0

pro_content = false			'是否打开产品简介

is_modify_cate = false		'缺省为false

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
function filterjp(str)
	dim jp
	jp = array("ァ","ア","ィ","イ","ゥ","ウ","ェ","エ","ォ","オ","カ","ガ","キ","ギ","ク","グ","ケ","ゲ","コ","ゴ","サ","ザ","シ","ジ","ス","ズ","セ","ゼ","ソ","ゾ","タ","ダ","チ","ヂ","ッ","ツ","ヅ","テ","デ","ト","ド","ナ","ニ","ヌ","ネ","ノ","ハ","バ","パ","ヒ","ビ","ピ","フ","ブ","プ","ヘ","ベ","ペ","ホ","ボ","ポ","マ","ミ","ム","メ","モ","ャ","ヤ","ュ","ユ","ョ","ヨ","ラ","リ","ル","レ","ロ","ヮ","ワ","ヰ","ヱ","ヲ","ン","ヴ","ヵ","ヶ","ー","ヽ","ヾ")
	for filterjp_i = 0 to 88

	str = replace(str,jp(filterjp_i),"")
	next
	filterjp = str
end function



Sub watermark(image,logoimage,position,opacity,transparence_color)
	'***********************************************************************************
	'因为标志较大，太小的图没有必要打水印，除非另作一水印
	'position: 0为中 1,2,3,4依次为左上、右上，右下，左下
	'opacity: 不透明度 0-1
	'transparence_color: 透明色，为16进制值，格式为 &HFFFFFF
	'图片目录限定于 img

	Set Img = Server.CreateObject("Persits.Jpeg") 
	Set Logo = Server.CreateObject("Persits.Jpeg") 

	ImgPath = Server.MapPath("..\img") & "\" & image 
	Img.Open ImgPath 

	LogoPath = Server.MapPath("..\img") & "\" & logoimage
	Logo.Open LogoPath 

	'大小适应，即确定logo.width,logo.height的值
	if  Logo.OriginalWidth / Logo.OriginalHeight >= Img.OriginalWidth / Img.OriginalHeight then
		if Logo.OriginalWidth > Img.OriginalWidth then
			Logo.Width = Img.OriginalWidth
			Logo.Height = Img.OriginalWidth / Logo.OriginalWidth  * Logo.OriginalHeight
			else
			Logo.Width = Logo.OriginalWidth
			Logo.Height = Logo.OriginalHeight
		end if
	else
		if Logo.OriginalHeight > Img.OriginalHeight then
			Logo.Height = Img.OriginalHeight
			Logo.Width = Img.OriginalHeight / Logo.OriginalHeight  * Logo.OriginalWidth
			else
			Logo.Width = Logo.OriginalWidth
			Logo.Height = Logo.OriginalHeight
		end if
	end if

	'写位置
	dim Logopadding_left,logopadding_height
	select case position
	case 0
	Logopadding_left = cint((Img.OriginalWidth - Logo.Width)/2)
	logopadding_height = cint((Img.OriginalHeight - Logo.Height)/2)
	case 1
	Logopadding_left = 0
	logopadding_height = 0
	case 2
	Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
	logopadding_height = 0
	case 3
	Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
	logopadding_height = cint(Img.OriginalHeight - Logo.Height)
	case 4
	Logopadding_left = 0
	logopadding_height = cint(Img.OriginalHeight - Logo.Height)
	end select

	Img.Canvas.DrawImage Logopadding_left, logopadding_height, Logo,opacity,transparence_color,100
	Img.Save Server.MapPath("../img/" & image)	'保存 
	Img.close()
	Logo.close()
	set Img=nothing
	set Logo = nothing

end Sub
%>