<%
'ver 2.00
'注意：不能在本页面放页面转向判断，因为在stat_in.asp要引用本页面

isGoogleAnalytics = false
isGoogleAnalytics_id = ""
isGoogleAnalytics_pw = ""

mURL		= "http://www.hualongzg.com"			' 站点链接
mNameEn		= "hualongzg.com"						' 站点英文名，写入cookie时使用
connpath	= "d:/vhost/hualongzg.com_stats.asp"	' 数据库存放位置，绝对路径

nav_style	= 2							' 导航风格。0：顶部下拉表单；1：顶部导航条；2：左侧列表；3：左侧表单
chart_style	= 6							' 图表风格。0黄色;1绿色;2红色;3橙色;4灰色;5蓝紫色;6蓝色;7紫色;8桃红;9浅红
mPageSize	= 20						' 每页记录数（需分页的页面）
mPrecision	= 1							' 精确到小数点后多少位

mlevel		= 6							' 权限等级
ssql_view	= 0							' 是否让自定义检索条件可见，0：不可见；1：可见。一般为0
stat_advance =	0						' 高级功能打开否。0为不打开
yesview		= 0							' 是否不用登录也可查看流量统计。0：为不可；1：可以。一般取0
'advance_count = 0						' 高级功能试用次数，0为不试用

CookieExpires=100			' cookie设置时间(天),默认为100天
addtime		= 0				' 服务器时间差，单位小时：如服务器时间是17:00，本地时间19:00，值为2，反之-2
old_count	= 0			' 在使用本统计系统前的访问量

'防刷新和在线统计设置，这二项打开都将降低统计器效率
is_ipcheck	= true			' 是否启用防刷新功能，true表示启用，false表示不启用
is_online	= false			' 是否统计在线人数，true表示启用，false表示不启用

onlythesite1= ""			' 限制嵌入代码位置，保持空白表示不做限制
onlythesite2= ""			' 限制嵌入代码位置，保持空白表示不做限制
'此项不为空时，系统在执行时会检查被访问页面的URL是否以上述文字开头，不是以上述文字开头的网址一律不计数。
'比如 onlythesite1="http://www.soundjet.net" ，则如有人在非www.soundjet.net网页放置嵌入代码，则不计数。

ipconnpath	= "d:/vhost/ip.asp"						' IP库存放位置，绝对路径。


FlashWidth	= 130			' FLASH图标宽度
FlashHeight	= 58			' FLASH图标高度
%>