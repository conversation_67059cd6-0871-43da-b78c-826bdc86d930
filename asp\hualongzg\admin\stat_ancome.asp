<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'计算指定日期的访问量
function vdaycon(theday)
    theday=cdate(theday)
    thetday=cdate(theday+1)
    tmprs=conn.execute("Select count(id) as vdaycon from view where" & _
		" vtime>=datevalue('" & theday & "') and vtime<=datevalue('" & thetday & "')")
    vdaycon=tmprs("vdaycon")
	if isnull(vdaycon) then vdaycon=0
end function
%>
<%
'权限检查
if session.Contents("master")=false and mlevel<2 then Response.Redirect "stat_help.asp?id=004&error=您没有查看来路统计的权限。"
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>来路统计</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指图形柱或者图形柱下的数字可以看到对应的访问量</font>
  </td></tr>
</table>
<br>
<%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")
%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  <tr><td class=td0 width="100%">
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="100%" align=center>
<tr height="10">
	<td class=td0 width="100%"></td><td class=td0 width="230"><img src="stat_images/chart_head.gif"></td>
</tr>
<%
sql="select vcome,count(id) as allcome from view group by vcome order by count(id) DESC"
rs.Open sql,conn,1,1
maxallcome=0
sumallcome=0
do while not rs.EOF
	if clng(rs("allcome"))>maxallcome then maxallcome=clng(rs("allcome"))
	sumallcome=sumallcome+clng(rs("allcome"))
	rs.MoveNext
loop
	'防止除数为0而出错
	if maxallcome=0 then maxallcome=1
	if sumallcome=0 then sumallcome=1
rs.MoveFirst 

j=0
do while not rs.EOF
thecome=rs("vcome")
vallcome=rs("allcome")
	thelen=len(thecome)
	if thelen =0 then
		thecome="#"
		svcome="通过收藏夹或直接输入网址访问"
	end if
	if thelen <= 40 and thelen > 0 then
		svcome=thecome
	end if
	if thelen >= 41 then
		svcome=left(thecome,40) & "..."
	end if
%>
<tr>
<td class=td0 width="100%" align=right><a href="<%=thecome%>" target="_blank"
	title="<%=thecome & vbcrlf%>访问<%=vallcome%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallcome*1000/sumallcome)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12><%=svcome%></font></a>&nbsp;</td>
<td class=td0 width="230" background="stat_images/chart_b2.gif" align=left>
<img style="BORDER-left: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	width="<%=(vallcome/maxallcome)*183%>" height="9"
	alt="<%=thecome & vbcrlf%>访问<%=vallcome%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallcome*1000/sumallcome)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12> <%=vallcome%></font></td>
</tr>
<%
rs.MoveNext
	j=j+1
	'如果记录超过40条，就退出
	if j=40 then exit do
loop
%>
<tr height="10">
	<td class=td0 width="100%"></td><td class=td0 width="230"><img src="stat_images/chart_bottom.gif"></td>
</tr>
</table>
	</td>
    <td class=td0 width="1"></td>
  </tr>
</table>
<%
rs.Close
set rs=nothing
conn.Close 
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->