<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="page_config.asp" -->
<%
Server.ScriptTimeOut = 1800

Dim rsSUBNAME__MMColParam
rsSUBNAME__MMColParam = "1"
if (Request.QueryString("subclass") <> "") then rsSUBNAME__MMColParam = Request.QueryString("subclass")
%>
<%
set rsSUBNAME = Server.CreateObject("ADODB.Recordset")
rsSUBNAME.ActiveConnection = MM_conn_STRING
rsSUBNAME.Source = "SELECT * FROM list WHERE id = " + Replace(rsSUBNAME__MMColParam, "'", "''") + ""
rsSUBNAME.CursorType = 0
rsSUBNAME.CursorLocation = 2
rsSUBNAME.LockType = 3
rsSUBNAME.Open()
rsSUBNAME_numRows = 0

'//必须填写的字段
dim must_str(4),must_submit
must_str(0) = ""	'标题
must_str(1) = ""	'内容
must_str(2) = ""	'详图、软件 img字段
must_str(3) = ""	'流媒体文件
must_str(4) = ""	'流媒体宽高
select case rsSUBNAME("style_id")
case 0				'标准列表
must_str(0) = "<font color=#ff0000>*</font>"
must_str(1) = ""
must_str(2) = ""
must_str(3) = ""
must_str(4) = ""
must_submit = "'fmtitle','','R'"
case 1				'纯图片列表
must_str(0) = "<font color=#ff0000>*</font>"
must_str(1) = ""
must_str(2) = "<font color=#ff0000>*</font>"
must_str(3) = ""
must_str(4) = ""
must_submit = "'fmtitle','','R','fmimg','','R'"
case 2				'展示列表
must_str(0) = "<font color=#ff0000>*</font>"
must_str(1) = ""
must_str(2) = "<font color=#ff0000>*</font>"
must_str(3) = ""
must_str(4) = ""
must_submit = "'fmtitle','','R','fmimg','','R'"
case 3				'流媒体列表
must_str(0) = "<font color=#ff0000>*</font>"
must_str(1) = ""
must_str(2) = "<font color=#ff0000>*</font>"
must_str(3) = "<font color=#ff0000>*</font>"
must_str(4) = "<font color=#ff0000>*</font>"
must_submit = "'fmtitle','','R','fmimg','','R','media','','R','media_alt_1','','R','media_alt_1','','isNaN','media_alt_2','','isNaN','media_alt_2','','R'"
case 4				'软件下载列表
must_str(0) = "<font color=#ff0000>*</font>"
must_str(1) = ""
must_str(2) = "<font color=#ff0000>*</font>"
must_str(3) = ""
must_str(4) = ""
must_submit = "'fmtitle','','R','fmimg','','R'"
end select

%>
<%
'*** File Upload to: ../img, Extensions: "GIF,JPG,JPEG,BMP,PNG", Form: form1, Redirect: "", "file", "", "uniq"
'*** Pure ASP File Upload Modify Version by xPilot-----------------------------------------------------
' Copyright 2000 (c) George Petrov
'
' Script partially based on code from Philippe Collignon 
'              (http://www.asptoday.com/articles/20000316.htm)
'
' New features from GP:
'  * Fast file save with ADO 2.5 stream object
'  * new wrapper functions, extra error checking
'  * UltraDev Server Behavior extension
'
' Copyright 2001-2002 (c) Modify by xPilot
' *** Date: 12/15/2001 ***
' *** 支持所有双字节文件名，而且修复了原函数中遇到空格也会自动截断文件名的错误！ ***
' *** 保证百分百以原文件名保存上传文件！***
' *** Welcome to visite pilothome.yeah.net <NAME_EMAIL> to me！***
'
' Version: 2.0.1 Beta for GB2312,BIG5,Japan,Korea ...
'------------------------------------------------------------------------------
Sub BuildUploadRequest(RequestBin,UploadDirectory,storeType,sizeLimit,nameConflict)
  'Get the boundary
  PosBeg = 1
  PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
  if PosEnd = 0 then
    Response.Write "<b>Form was submitted with no ENCTYPE=""multipart/form-data""</b><br>"
    Response.Write "Please correct the form attributes and try again."
    Response.End
  end if
  'Check ADO Version
	set checkADOConn = Server.CreateObject("ADODB.Connection")
	adoVersion = CSng(checkADOConn.Version)
	set checkADOConn = Nothing
	if adoVersion < 2.5 then
    Response.Write "<b>You don't have ADO 2.5 installed on the server.</b><br>"
    Response.Write "The File Upload extension needs ADO 2.5 or greater to run properly.<br>"
    Response.Write "You can download the latest MDAC (ADO is included) from <a href=""www.microsoft.com/data"">www.microsoft.com/data</a><br>"
    Response.End
	end if		
  'Check content length if needed
	Length = CLng(Request.ServerVariables("HTTP_Content_Length")) 'Get Content-Length header
	If "" & sizeLimit <> "" Then
    sizeLimit = CLng(sizeLimit)
    If Length > sizeLimit Then
      Request.BinaryRead (Length)
      Response.Write "Upload size " & FormatNumber(Length, 0) & "B exceeds limit of " & FormatNumber(sizeLimit, 0) & "B"
      Response.End
    End If
  End If
  boundary = MidB(RequestBin,PosBeg,PosEnd-PosBeg)
  boundaryPos = InstrB(1,RequestBin,boundary)
  'Get all data inside the boundaries
  Do until (boundaryPos=InstrB(RequestBin,boundary & getByteString("--")))
    'Members variable of objects are put in a dictionary object
    Dim UploadControl
    Set UploadControl = CreateObject("Scripting.Dictionary")
    'Get an object name
    Pos = InstrB(BoundaryPos,RequestBin,getByteString("Content-Disposition"))
    Pos = InstrB(Pos,RequestBin,getByteString("name="))
    PosBeg = Pos+6
    PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(34)))
    Name = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
    PosFile = InstrB(BoundaryPos,RequestBin,getByteString("filename="))
    PosBound = InstrB(PosEnd,RequestBin,boundary)
    'Test if object is of file type
    If  PosFile<>0 AND (PosFile<PosBound) Then
      'Get Filename, content-type and content of file
      PosBeg = PosFile + 10
      PosEnd =  InstrB(PosBeg,RequestBin,getByteString(chr(34)))
      FileName = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      FileName = Mid(FileName,InStrRev(FileName,"\")+1)
      'Add filename to dictionary object
      UploadControl.Add "FileName", FileName
      Pos = InstrB(PosEnd,RequestBin,getByteString("Content-Type:"))
      PosBeg = Pos+14
      PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
      'Add content-type to dictionary object
      ContentType = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      UploadControl.Add "ContentType",ContentType
      'Get content of object
      PosBeg = PosEnd+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = FileName
      ValueBeg = PosBeg-1
      ValueLen = PosEnd-Posbeg
    Else
      'Get content of object
      Pos = InstrB(Pos,RequestBin,getByteString(chr(13)))
      PosBeg = Pos+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      ValueBeg = 0
      ValueEnd = 0
    End If
    'Add content to dictionary object
    UploadControl.Add "Value" , Value	
    UploadControl.Add "ValueBeg" , ValueBeg
    UploadControl.Add "ValueLen" , ValueLen	
    'Add dictionary object to main dictionary
    UploadRequest.Add name, UploadControl	
    'Loop to next object
    BoundaryPos=InstrB(BoundaryPos+LenB(boundary),RequestBin,boundary)
  Loop

  GP_keys = UploadRequest.Keys
  for GP_i = 0 to UploadRequest.Count - 1
    GP_curKey = GP_keys(GP_i)
    'Save all uploaded files
    if UploadRequest.Item(GP_curKey).Item("FileName") <> "" then
      GP_value = UploadRequest.Item(GP_curKey).Item("Value")
      GP_valueBeg = UploadRequest.Item(GP_curKey).Item("ValueBeg")
      GP_valueLen = UploadRequest.Item(GP_curKey).Item("ValueLen")

      if GP_valueLen = 0 then
        Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
        Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
        Response.Write "File does not exists or is empty.<br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
	  	  response.End
	    end if
      
      'Create a Stream instance
      Dim GP_strm1, GP_strm2
      Set GP_strm1 = Server.CreateObject("ADODB.Stream")
      Set GP_strm2 = Server.CreateObject("ADODB.Stream")
      
      'Open the stream
      GP_strm1.Open
      GP_strm1.Type = 1 'Binary
      GP_strm2.Open
      GP_strm2.Type = 1 'Binary
        
      GP_strm1.Write RequestBin
      GP_strm1.Position = GP_ValueBeg
      GP_strm1.CopyTo GP_strm2,GP_ValueLen
    
      'Create and Write to a File
      GP_curPath = Request.ServerVariables("PATH_INFO")
      GP_curPath = Trim(Mid(GP_curPath,1,InStrRev(GP_curPath,"/")) & UploadDirectory)
      if Mid(GP_curPath,Len(GP_curPath),1)  <> "/" then
        GP_curPath = GP_curPath & "/"
      end if 
      GP_CurFileName = UploadRequest.Item(GP_curKey).Item("FileName")
      GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & GP_CurFileName
      'Check if the file alreadu exist
      GP_FileExist = false
      Set fso = CreateObject("Scripting.FileSystemObject")
      If (fso.FileExists(GP_FullFileName)) Then
        GP_FileExist = true
      End If      
      if nameConflict = "error" and GP_FileExist then
        Response.Write "<B>File already exists!</B><br><br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
				GP_strm1.Close
				GP_strm2.Close
	  	  response.End
      end if
      if ((nameConflict = "over" or nameConflict = "uniq") and GP_FileExist) or (NOT GP_FileExist) then
        if nameConflict = "uniq" and GP_FileExist then
          Begin_Name_Num = 0
          while GP_FileExist    
            Begin_Name_Num = Begin_Name_Num + 1
            GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
            GP_FileExist = fso.FileExists(GP_FullFileName)
          wend  
          UploadRequest.Item(GP_curKey).Item("FileName") = fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
					UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
        end if
        on error resume next
        GP_strm2.SaveToFile GP_FullFileName,2
        if err then
          Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
          Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
          Response.Write "Maybe the destination directory does not exist, or you don't have write permission.<br>"
          Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
    		  err.clear
  				GP_strm1.Close
  				GP_strm2.Close
  	  	  response.End
  	    end if
  			GP_strm1.Close
  			GP_strm2.Close
  			if storeType = "path" then
  				UploadRequest.Item(GP_curKey).Item("Value") = GP_curPath & UploadRequest.Item(GP_curKey).Item("Value")
  			end if
        on error goto 0
      end if
    end if
  next

End Sub

'把普通字符串转成二进制字符串函数
Function getByteString(StringStr)
    getByteString=""
  For i = 1 To Len(StringStr) 
    XP_varchar = mid(StringStr,i,1)
    XP_varasc = Asc(XP_varchar) 
    If XP_varasc < 0 Then 
       XP_varasc = XP_varasc + 65535 
    End If 

    If XP_varasc > 255 Then 
       XP_varlow = Left(Hex(Asc(XP_varchar)),2) 
       XP_varhigh = right(Hex(Asc(XP_varchar)),2) 
       getByteString = getByteString & chrB("&H" & XP_varlow) & chrB("&H" & XP_varhigh) 
    Else 
       getByteString = getByteString & chrB(AscB(XP_varchar)) 
    End If 
  Next 
End Function

'把二进制字符串转换成普通字符串函数 
Function getString(StringBin)
   getString =""
   Dim XP_varlen,XP_vargetstr,XP_string,XP_skip
   XP_skip = 0 
   XP_string = "" 
 If Not IsNull(StringBin) Then 
      XP_varlen = LenB(StringBin) 
    For i = 1 To XP_varlen 
      If XP_skip = 0 Then
         XP_vargetstr = MidB(StringBin,i,1) 
         If AscB(XP_vargetstr) > 127 Then 
           XP_string = XP_string & Chr(AscW(MidB(StringBin,i+1,1) & XP_vargetstr)) 
           XP_skip = 1 
         Else 
           XP_string = XP_string & Chr(AscB(XP_vargetstr)) 
         End If 
      Else 
      XP_skip = 0
   End If 
    Next 
 End If 
      getString = XP_string 
End Function 

Function UploadFormRequest(name)
  on error resume next
  if UploadRequest.Item(name) then
    UploadFormRequest = UploadRequest.Item(name).Item("Value")
  end if  
End Function

'Process the upload
UploadQueryString = Replace(Request.QueryString,"GP_upload=true","")
if mid(UploadQueryString,1,1) = "&" then
	UploadQueryString = Mid(UploadQueryString,2)
end if

GP_uploadAction = CStr(Request.ServerVariables("URL")) & "?GP_upload=true"
If (Request.QueryString <> "") Then  
  if UploadQueryString <> "" then
	  GP_uploadAction = GP_uploadAction & "&" & UploadQueryString
  end if 
End If

If (CStr(Request.QueryString("GP_upload")) <> "") Then
  GP_redirectPage = ""
  If (GP_redirectPage = "") Then
    GP_redirectPage = CStr(Request.ServerVariables("URL"))
  end if
    
  RequestBin = Request.BinaryRead(Request.TotalBytes)
  Dim UploadRequest
  Set UploadRequest = CreateObject("Scripting.Dictionary")  

if rsSUBNAME("style_id")= 4 then '如果是软件下载列表
  BuildUploadRequest RequestBin, "../img", "file", "", "uniq"
  else
  BuildUploadRequest RequestBin, "../img", "file", "", "uniq"
end if

  '*** GP NO REDIRECT
end if  
if UploadQueryString <> "" then
  UploadQueryString = UploadQueryString & "&GP_upload=true"
else  
  UploadQueryString = "GP_upload=true"
end if  
%>
<%'添加页面
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction = CStr(Request.ServerVariables("URL")) 'MM_editAction = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction = MM_editAction & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: (Modified for File Upload) set variables

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "page"
  MM_editRedirectUrl = "page_ok.asp?url=" & server.urlencode("page_add.asp?action=add&subclass=" & UploadFormRequest("fmsubid"))
  MM_fieldsStr  = "top_submit|value|intro|value|media|value|media_alt|value|imgwidth|value|imgheight|value|fmsubid|value|fmtitle|value|fmcontent|value|fmhtml|value|fmimg|value|fmthumb|value|fmthis_position|value|fmalt|value|top_style|value"
  MM_columnsStr = "top_submit|none,none,NULL|intro|',none,''|media|',none,''|media_alt|',none,''|imgwidth|none,none,NULL|imgheight|none,none,NULL|subid|none,none,NULL|title|',none,''|content|',none,''|html|none,none,NULL|img|',none,''|thumb|none,none,NULL|this_position|none,none,NULL|alt|',none,''|top_style|none,none,NULL"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & UploadQueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & UploadQueryString
    End If
  End If

End If
%>
<%'发送email到管理员
If CStr(UploadFormRequest("fmSubmit")) <>"" then
set rsREALTIME = Server.CreateObject("ADODB.Recordset")
rsREALTIME.ActiveConnection = MM_conn_STRING
rsREALTIME.Source = "SELECT item_c, zhi  FROM realtime  WHERE item_c = 'page_add'"
rsREALTIME.CursorType = 0
rsREALTIME.CursorLocation = 2
rsREALTIME.LockType = 3
rsREALTIME.Open()
rsREALTIME_numRows = 0
	if rsREALTIME.Fields.Item("zhi").Value = true then '如果要发送
%>
<%'得到管理员信箱
set rsADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsADMINMAIL.ActiveConnection = MM_conn_STRING
rsADMINMAIL.Source = "SELECT email FROM admin"
rsADMINMAIL.CursorType = 0
rsADMINMAIL.CursorLocation = 2
rsADMINMAIL.LockType = 3
rsADMINMAIL.Open()
rsADMINMAIL_numRows = 0
%>
<%'使用CDONTS发送email
real_subject= "网站管理员在列表" & rsSUBNAME.Fields.Item("name").Value & "增加页面"
real_content="页面主题：<font color=ff0000>" & UploadFormRequest("fmtitle") & "</font>"
if UploadFormRequest("fmcontent") <> "" then real_content= real_content & "<br><br>" & "以下是该页面的内容：" & "<br><hr>"  & Replace(HTMLEncode(UploadFormRequest("fmcontent")),vbCrLF,"<br>")
real_body= real_content
if UploadFormRequest("fmimg") <> "" then 
	real_body = real_body & "<br><hr><img src=http://www.cnflyer.com/img/" & UploadFormRequest("fmimg") & ">"
	if UploadFormRequest("fmalt") <> "" then real_body =real_body & "<br><br>" & "图片说明：" & UploadFormRequest("fmalt")
end if
real_mailfrom=rsADMINMAIL.Fields.Item("email").Value
real_email=rsADMINMAIL.Fields.Item("email").Value

Set SendMail = Server.CreateObject("CDONTS.NewMail")
SendMail.From= real_mailfrom
SendMail.To = real_email
SendMail.Subject = real_subject
SendMail.Bodyformat = 0
SendMail.mailformat = 0
SendMail.Body = real_Body
SendMail.Importance= 2
SendMail.Send
Set SendMail = Nothing

'response.write real_subject & "<br>" & "<br>" & real_mailfrom & "<br>" & real_email & "<br>" & real_body
'response.end
%>
<%'关闭rs
rsADMINMAIL.Close()
	end if'如果要发送
rsREALTIME.Close()
end if'发送email到管理员
%>
<%
' *** Insert Record: (Modified for File Upload) construct a sql insert statement and execute it

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End if
    MM_tableValues = MM_tableValues & MM_columns(i)
    MM_dbValues = MM_dbValues & FormVal
  Next

if rsSUBNAME("style_id") < 3 AND UploadFormRequest("fmimg")<>"" then '如果是标准列表、图片列表、展示列表，生成缩略图
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ",img_small,imgwidth_small,imgheight_small" & ") values (" & MM_dbValues & "," & made_thumb("..\img",UploadFormRequest("fmimg"),img_max_dimension,img_thumb_side) &")"
else
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"
end if

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
function made_thumb(path,img,dimension,side)	
	'生成缩略图，源图相对路径（最后不带\），源图文件名，缩略图尺寸，尺寸边
	'side的值 "height","width"
	'输出缩略图名称, 宽度, 高度
	dim bigimg,bigimgpath,thumbpath
	bigimgpath = Server.MapPath(path) & "\" & img
	thumbpath = Server.MapPath(path) & "\" & "thumb__"  & img
	Set bigimg = Server.CreateObject("Persits.Jpeg")
	bigimg.Open bigimgpath

	if side = "height" then
		if bigimg.OriginalHeight <= dimension then	'如果原图太小
			made_thumb = "'" & img & "'," & cstr(bigimg.OriginalWidth) & ","  & cstr(bigimg.OriginalHeight)
		else
			bigimg.Width = dimension * bigimg.OriginalWidth/bigimg.OriginalHeight
			bigimg.Height = dimension
			bigimg.Save thumbpath
			made_thumb = "'thumb__"  & img & "'," & cstr(bigimg.Width) & "," & cstr(bigimg.Height)
		end if
	else
		if bigimg.OriginalWidth <= dimension then	'如果原图太小
			made_thumb = "'" & img & "'," & cstr(bigimg.OriginalWidth) & ","  & cstr(bigimg.OriginalHeight)
		else
			bigimg.Width = dimension
			bigimg.Height = dimension * bigimg.OriginalHeight/bigimg.OriginalWidth
			bigimg.Save thumbpath
			made_thumb ="'thumb__"  & img & "'," & cstr(bigimg.Width) & "," & cstr(bigimg.Height)
		end if
	end if
	bigimg.close()
	set bigimg = nothing
end function
%>
<% '给可编辑子列表命名，如刊物、产品等
if cint(request.querystring("subclass")) <120 then
	this_subname = "列表"
	this_subname_2 = "页面"
else
	this_subname = "产品"
	this_subname_2 = "内容"
end if
%>

<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
	  <h1 class="b_ch1">增加<%=this_subname_2%><%'判断语言
if rsSUBNAME("this_lan") <>"" then response.write(" <font color=ff0000>[ "& rsSUBNAME("this_lan") & "网站 ]</font>")
%></h1>
<script language="JavaScript">
<!--
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='* 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- 必须输入数字.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '* 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
    document.MM_returnValue = (errors == '');


}

//-->
</script>
	  第二步：在<%=this_subname%> <font color=ff0000><%=(rsSUBNAME.Fields.Item("name").Value)%></font> 添加<%=this_subname_2%><br>
<script language="JavaScript">
<!--

function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} 
	document.form1.media_alt.value = document.form1.media_alt_1.value + "-" + document.form1.media_alt_2.value;
}

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;<%if rsSUBNAME("style_id")= 3 then		'如果是流媒体%>
    re = new RegExp(".(gif|jpg|png|bmp|jpeg|wmv|rm)$","i");<%else%>
    re = new RegExp(".(gif|jpg|png|bmp|jpeg)$","i");
	 <%end if%>
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('上传文件太小!\n应大于 ' + img.minWidth + ' x ' + img.minHeight); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('上传文件太大!\n不超过 ' + img.maxWidth + ' x ' + img.maxHeight); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('上传文件太大!\n不超过 ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}
//-->
</script>

	  <form METHOD="POST" name="form1" enctype="multipart/form-data" action="<%=MM_editAction%>" <%if rsSUBNAME("style_id")= 4 then
	  else%> onSubmit="checkFileUpload(this,'GIF,JPG,JPEG,WMV,RM',false,200,'','','','','imgwidth','imgheight');return document.MM_returnValue"<%end if%>>
		<input type="hidden" name="fmsubid" value="<%=Request.QueryString("subclass")%>">
		<table border="0" cellspacing="0" cellpadding="4" align=center>
		  <tr> 
			<td width="126"><%=must_str(0)%>标题</td>
			<td width="500"> 
			  <input type="text" name="fmtitle" size="90" class=i550>
			</td>
		  </tr><%if rsSUBNAME("style_id")= 1 then
		  else%>
<!-- 		  <tr> 
			<td valign="top" width="126">内容摘要（限120字，如作为首页图片新闻必须输入）</td>
			<td><textarea class=i550 name=intro cols=80 rows=5></textarea></td>
		  </tr> -->
		  <tr> 
			<td valign="top" width="126"><%=must_str(1)%>详细内容<br>
			  <font size="-1"><br>
			  注：请将文字排好。<br>
			  段中不要回车；<br>
			  段前空格用全角；<br>
			  段与段之间空一行。<br></font></td>
			<td width="400" id="tdcon">
<script>


tdcon.innerHTML = "<textarea class=i550 name=fmcontent cols=80 rows=15></textarea>";

function check(){// 单选的点击事件
	imgshow = event.srcElement.value;

if (imgshow == "1") {
		tdcon.innerHTML = "<input type=hidden name=fmcontent><IFRAME ID=htmlbuilder1 SRC='htmlbuilder/htmlbuilder.asp?id=fmcontent&style=<%=hb_css%>' FRAMEBORDER=0 SCROLLING=no WIDTH=550 HEIGHT=350></IFRAME>";
<%if rsSUBNAME("style_id") =0 or rsSUBNAME("style_id") >= 3 then%>
		content_img.style.display = "none";
		content_img2.style.display = "none";
		content_img3.style.display = "none";
		content_img4.style.display = "none";
<%end if%>
	}else{
		tdcon.innerHTML ="<textarea class=i550 name=fmcontent cols=80 rows=15></textarea>";
		content_img.style.display = "";
		content_img2.style.display = "";
		content_img3.style.display = "";
		content_img4.style.display = "";
	}

}

</script>

			</td>
		  </tr>
		  <tr> 
			<td width="126">是否html</td>
			<td width="400"> 否 
			  <input type="radio" name="fmhtml" value="0" checked onclick="check()">
			  <img src="/images/spacer.gif" width=40 height=1> 是 
			  <input type="radio" name="fmhtml" value="1" onclick="check()"></td>
		  </tr><%end if%>
		  <tr id="content_img"> 
			<td width="126"><%=must_str(2)%><%if rsSUBNAME("style_id")= 4 then
			response.write("附件（软件或文档）")
			else%>详图<%end if%></td>
			<td width="400"><%if rsSUBNAME("style_id")= 4 then
			else%>
			<input type=hidden name="imgwidth">
			<input type=hidden name="imgheight"><%end if%>
			  <input type="file" name="fmimg" size="40"<%if rsSUBNAME("style_id")= 4 then
			  else%> onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,200,'','','','','imgwidth','imgheight')"<%end if%>>
			  <br><%if rsSUBNAME("style_id")= 4 or rsSUBNAME("style_id")= 1 then
			  else%><font size="-1">请用jpg或gif格式，为了保证网站的兼容性，图片名称请用英文</font><%end if%></td>
		  </tr><%if rsSUBNAME("style_id")= 2  then
		  response.write("<input type=hidden name='fmthumb' value=1>")
		  response.write("<input type=hidden name='fmthis_position' value=2>")
		  else%>
		  <%if rsSUBNAME("style_id")= 4  or rsSUBNAME("style_id")= 1 then
		  else%>
		  <tr id="content_img2"> 
			<td width="126">是否生成缩略图<br>
			  <font size=-1><br>
			  如生成缩略图，则在页面显示较小的图，点击图片则弹出大图</font></td>
			<td width="400" nowrap> 否 
			  <input type="radio" name="fmthumb" value="0">
			  <img src="/images/spacer.gif" width=40 height=1> 是 
			  <input type="radio" name="fmthumb" value="1" checked>
			  <br><font size=-1>（为了版面美观，当图片大于<%=img_maxwidth%>×<%=img_maxheight%>个像素时建议选“是”）</font></td>
		  </tr>
		  <tr id="content_img3"> 
			<td width="126">图片放置位置</td>
			<td width=400> 左上 
			  <input type="radio" name="fmthis_position" value="0">
			  　　 右上 
			  <input type="radio" name="fmthis_position" value="1">　　 中 
			  <input type="radio" name="fmthis_position" value="2" checked>
			</td>
		  </tr>
		  <tr id="content_img4"> 
			<td width="126">图片说明<font size=-1><br>
			  <br>
			  <font size=-1>注：当鼠标放在图片上时出现</font></font></td>
			<td width="400"> 
			  <input type="text" name="fmalt" size="90" class=i550>
			</td>
		  </tr>
		  <%end if%><%end if%>
<!-- 			  <%if (rsSUBNAME("style_id")<> 4) then%>
			  <tr><td colspan=2 class=p12><font color=ff0000>如果对图片质量要求高，添加页面后请在“修改页面”里增加小图，小图将忽略缩略图选项，取代自动缩略图</font></td></tr><%end if%> -->

		<tr bgcolor=<%=(back_menubackcolor)%>> 
		  <td width="126">首页显示</td>
		  <td width="400"> 
		  <table class=table0 border=0>
		  <tr>
		  	<td class=td0><input type="radio" name="top_style" value="0"> 不处理</td>
<%if request.querystring("subclass")<>"2" then%><td class=td0><input type="radio" name="top_style" value="1"> 作为置顶新闻</td><%end if%>
<%if request.querystring("subclass")<>"2" then%>		  	<td class=td0><input type="radio" name="top_style" value="3"> 在首页左上角显示</td><%end if%>
<%if request.querystring("subclass")="2" then%>		  	<td class=td0><input type="radio" name="top_style" value="2"> 作为置顶新闻</td><%end if%>
		  </tr>
		  </table>
		  </td>
		</tr>

<%if rsSUBNAME("style_id")= 3 then		'如果是流媒体%>
		<tr bgcolor=<%=(back_menubackcolor)%>> 
		  <td width="126">流媒体文件</td>
		  <td width="400" class=p12> 
			<input type="text" name="media" class=i550 value="http://www.hnsyyz.cn/img/">流媒体文件用FTP上传后，填入文件名<br /> (URI为http://www.hnsyyz.cn/img/文件名)，支持wmv/rm二种格式
		  </td>
		</tr>
		<tr bgcolor=<%=(back_menubackcolor)%>> 
		  <td width="126"><%=must_str(4)%>流媒体高宽(像素)</td>
		  <td width="400" class=p12>
		  宽: <input type="text" name="media_alt_1" size="15"> 高: <input type="text" name="media_alt_2" size="15">
		  </td>
		</tr>
<input type=hidden name=media_alt>
<%else%>
<input type=hidden name=media value="">
<input type=hidden name=media_alt value="">
<%end if%>
		  <tr bgcolor="<%=(back_menubackcolor)%>"> 
			<td width="126">&nbsp;</td>
			<td width="400">
			  <input type="submit" name="fmSubmit" value=" OK "onClick="MM_validateForm(<%=must_submit%>);return document.MM_returnValue">
			</td>
		  </tr>
		</table>
		<input type="hidden" name="MM_insert" value="true">
<%if Session("isadmin")=true then
response.write("<input type=hidden name=top_submit value=true>")
end if
%>
	  </form>
<!--#include file ="_bottom.asp"-->  
<%
rsSUBNAME.Close()
%>