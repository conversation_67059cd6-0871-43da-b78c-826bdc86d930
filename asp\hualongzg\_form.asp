<!--#include file="_slib/sub_email.asp" -->
<!--#include file="_form_config.asp" -->
<%
Sub delete_buycookie()
	For i = 0 To 14
		cookie_str = "buy" & i
		Response.Cookies(cookie_str).Expires = #July 4, 1997#
	next
End sub

sub front_form(form_style,form_id)
dim rsGUEST__MMColParam
dim label_title,label_title2,label_title3,label_title5,label_title6,label_title7,label_title9,label_title10,labe1_title11 ,label_title8,label_title4,label_submit,label_thanks
dim rsADMINMAIL,rsADMINMAIL_numRows,rsCONTACTADMINMAIL,rsCONTACTADMINMAIL_numRows,guest__catename
dim MM_abortEdit,MM_editQuery,this_lan2,front__language
dim label_title1,lable_title40
rsGUEST__MMColParam = form_id

dim MM_editAction, MM_editAction_1

MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (Request.QueryString <> "") Then
	MM_editAction = MM_editAction & "?" & Request.QueryString
	MM_editAction_1 = MM_editAction  & "&"
	else
	MM_editAction_1 = MM_editAction  & "?"
End If
%>
<%if this_lan = "en" then'如果是英文界面
this_lan2 = this_than
select case form_style
case 0	'订购
	label_title= "<span class=""needs"">Product</span>"
	label_title2="Comment"
	label_title4="Your company"
	label_title8="Website"
	label_submit="Submit"
	label_thanks= "OK, Thanks! We'll contact you soon."
case 1	'产品咨询
	label_title= "<span class=""needs"">Product</span>"
	label_title2="Comment"
	label_title4="Your company"
	label_title8="Website"
	label_submit="Submit"
	label_thanks= "OK, Thanks! We'll contact you soon."
case 2	'应聘
	label_title= "<span class=""needs"">应聘职位</span>"
	label_title2="具体情况"
	label_title4="就职企业"
	label_title8="URL"
	label_submit="Submit"
	label_thanks= "Thanks!"
case 3	'投诉
	label_title= "<span class=""needs"">投诉对象</span>"
	label_title2="具体说明"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="投诉"
	label_thanks= "提交成功，谢谢！我们会很快回复您！"
case 4	'咨询问题
	label_title= "<span class=""needs"">Your Question</span>"
	label_title2="Comment"
	label_title4="Your Company"
	label_title8="Website"
	label_submit="Submit"
	label_thanks= "OK, Thanks!"
case 5	'服务请求
	label_title= "<span class=""needs"">Title</span>"
	label_title2="Comment"
	label_title4="Your Company"
	label_title8="Website"
	label_submit="Submit"
	label_thanks= "OK, Thanks!"
case 6	'留言
	label_title= "<span class=""needs"">Subject</span>"
	label_title2="Message"
	label_title4="Your Company"
	label_title8="Website"
	label_submit="Submit"
	label_thanks= "OK. Thanks!"
case else	'其他
	label_title= "<span class=""needs"">主　　题</span>"
	label_title2="内　　容"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="确定"
	label_thanks= "提交成功，谢谢！"
end select
label_title3="<span class=""needs"">Your name</span>"
label_title5="<span class=""needs"">Phone</span>"
label_title6="Fax"
label_title7="<span class=""needs"">Email</span>"
label_title9=""
label_title10="Remember my contact information."
labe1_title11="Code"
label_titlel2 = "submitting..."
label_titlel3 = "Mr"
label_titlel4 = "Madam"
label_titlel5 = "Miss"
ElseIf this_lan = "jp" Then
	select case form_style
		case 0	'订购
			label_title= "<span class=""needs"">発注</span>"
			label_title2="具体的な要求"
			label_title4="会社名"
			label_title8="ネット址"
			label_submit="submit"
			label_thanks= "OK, Thank you!"
		Case else
			label_title= "<span class=""needs"">テーマ</span>"
			label_title2="内容"
			label_title4="会社名"
			label_title8="ネット址"
			label_submit="submit"
			label_thanks= "Ok, Thank YOU!"
	End select
label_title3="<span class=""needs"">성명</span>"
label_title5="<span class=""needs"">お電話番号</span>"
label_title6="ファックス"
label_title7="<span>メールアドレス</span>"
label_title9=""
label_title10="入記したものを確認"
labe1_title11="検証コード"
label_titlel2 = "submitting..."
label_titlel3 = "男性"
label_titlel4 = "女性"
label_titlel5 = ""
ElseIf this_lan = "kr" Then
	select case form_style
		case 0	'订购
			label_title= "<span class=""needs"">제품 구매</span>"
			label_title2="구체 요구"
			label_title4="회사 명칭"
			label_title8="사이트"
			label_submit="submit"
			label_thanks= "OK, Thank you!"
		Case else
			label_title= "<span class=""needs"">주제</span>"
			label_title2="내용"
			label_title4="회사 명칭"
			label_title8="사이트"
			label_submit="submit"
			label_thanks= "OK, Thanks!"
	End select
label_title3="<span class=""needs"">성명</span>"
label_title5="<span class=""needs"">련계전화</span>"
label_title6="팩스"
label_title7="<span>e-Mail</span>"
label_title9=""
label_title10="련계정보 기억하기"
labe1_title11="검증 번호"
label_titlel2 = "submitting..."
label_titlel3 = "남"
label_titlel4 = "녀"
label_titlel5 = ""
else
select case form_style
case 0	'订购
	label_title= "<span class=""needs"">订购产品</span>"
	label_title2="其他说明"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="发送"
	label_thanks= "订购成功，谢谢！我们很快与您联系"
case 1	'产品咨询
	label_title= "<span class=""needs"">产　　品</span>"
	label_title2="具体要求"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="咨询"
	label_thanks= "提交成功，谢谢！我们的相关负责人会很快与您联系"
case 2	'应聘
	label_title= "<span class=""needs"">应聘职位</span>"
	label_title2="具体情况"
	label_title4="就职企业"
	label_title8="相关链接"
	label_submit="提交"
	label_thanks= "在线应聘提交成功，谢谢您的厚爱！"
case 3	'投诉
	label_title= "<span class=""needs"">投诉对象</span>"
	label_title2="具体说明"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="投诉"
	label_thanks= "提交成功，谢谢！我们会很快回复您！"
case 4	'咨询问题
	label_title= "<span class=""needs"">您的问题</span>"
	label_title2="描　　述"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="提交"
	label_thanks= "表单提交成功，谢谢！"
case 5	'服务请求
	label_title= "<span class=""needs"">主　　题</span>"
	label_title2="服务描述"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="确定"
	label_thanks= "提交成功，谢谢！"
case 6	'留言
	label_title= "<span class=""needs"">主　　题</span>"
	label_title2="内　　容"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="留言"
	label_thanks= "留言成功，谢谢！"
case else	'其他
	label_title= "<span class=""needs"">主　　题</span>"
	label_title2="内　　容"
	label_title4="公司名称"
	label_title8="网　　站"
	label_submit="确定"
	label_thanks= "提交成功，谢谢！"
end select
label_title3="<span class=""needs"">您的姓名</span>"
label_title5="<span class=""needs"">联系电话</span>"
label_title6="传　　真"
label_title7="<span>电子信箱</span>"
label_title9="如果您不是在公共场合或者使用公用电脑上网，请选上，下次留言时就不用重新填写联系信息"
label_title10="记住我的联系信息"
labe1_title11="验证码"
label_titlel2 = "正在提交，请稍侯"
label_titlel3 = "先生"
label_titlel4 = "女士"
label_titlel5 = "小姐"

end if
%>
<%
''''''验证码开始
if request.form("action") ="ok" then
identify=trim(request.form("identify"))

MM_editAction_3 = replace(MM_editAction,"&amp;action=ok","")	'如果提交URL里action=ok不在第一个位置
MM_editAction_3 = replace(MM_editAction,"action=ok","")		'如果提交URL里action=ok在第一个位置
if InStrRev(MM_editAction_3,"&") = len(MM_editAction_3) then
	MM_editAction_3 = left(MM_editAction_3,(len(MM_editAction_3)-1))
end if
if InStrRev(MM_editAction_3,"?") = len(MM_editAction_3) then
	MM_editAction_3 = left(MM_editAction_3,(len(MM_editAction_3)-1))
end if

if identify="" then
	if this_lan="en" then
	response.write "<script>alert('Please Enter the code shown');window.location.href('" & MM_editAction_3 & "');</script>"
	response.end 	
	else
	response.write "<script>alert('请输入验证码');window.location.href('" & MM_editAction_3 & "');</script>"
	response.end 
	end if
else
	if this_lan="en" then
	if cstr(session("identify")) <> identify then
	response.write "<script>alert('Code error!');history.back();</script>"
	response.end 
	end if
	else
	if cstr(session("identify")) <> identify then
	response.write "<script>alert('验证码错误，请重新输入');history.back();;</script>"
	response.end 
	end if
	end if
end if

end if
''''''验证码结束
%>
<%
dim contact_adminmail_0,contact_adminmail
set rsADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsADMINMAIL.ActiveConnection = MM_conn_STRING
rsADMINMAIL.Source = "SELECT email FROM admin"
rsADMINMAIL.CursorType = 0
rsADMINMAIL.CursorLocation = 2
rsADMINMAIL.LockType = 3
rsADMINMAIL.Open()
rsADMINMAIL_numRows = 0

contact_adminmail_0 = rsADMINMAIL.Fields.Item("email").Value
rsADMINMAIL.Close()
set rsADMINMAIL=nothing

set rsCONTACTADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsCONTACTADMINMAIL.ActiveConnection = MM_conn_STRING
rsCONTACTADMINMAIL.Source = "SELECT email,name,this_type FROM guestbook_cate WHERE id = " & rsGUEST__MMColParam & " "
rsCONTACTADMINMAIL.CursorType = 0
rsCONTACTADMINMAIL.CursorLocation = 2
rsCONTACTADMINMAIL.LockType = 3
rsCONTACTADMINMAIL.Open()
rsCONTACTADMINMAIL_numRows = 0

contact_adminmail = trim(rsCONTACTADMINMAIL("email"))
guest__catename = rsCONTACTADMINMAIL("name")
guest__thistype = rsCONTACTADMINMAIL("this_type")
rsCONTACTADMINMAIL.close()
set rsCONTACTADMINMAIL = nothing

if contact_adminmail = "" or isnull(contact_adminmail) then contact_adminmail =contact_adminmail_0
%>
<% if request.form("action") ="ok" then
'写cookie联系信息
if request.form("remember") ="true" then
Response.Cookies(front_mainserver & ".contact")("name") = request.form("name")
Response.Cookies(front_mainserver & ".contact")("chenghu") = request.form("chenghu")
Response.Cookies(front_mainserver & ".contact")("co") = request.form("co")
Response.Cookies(front_mainserver & ".contact")("tel") = request.form("tel")
Response.Cookies(front_mainserver & ".contact")("fax") = request.form("fax")
Response.Cookies(front_mainserver & ".contact")("EMail") = request.form("EMail")
Response.Cookies(front_mainserver & ".contact")("url") = request.form("url")
Response.Cookies(front_mainserver & ".contact").expires = #31/12/2010#
else
Response.Cookies(front_mainserver & ".contact")("name") = ""
Response.Cookies(front_mainserver & ".contact")("chenghu") = ""
Response.Cookies(front_mainserver & ".contact")("co") = ""
Response.Cookies(front_mainserver & ".contact")("tel") = ""
Response.Cookies(front_mainserver & ".contact")("fax") = ""
Response.Cookies(front_mainserver & ".contact")("EMail") = ""
Response.Cookies(front_mainserver & ".contact")("url") = ""
Response.Cookies(front_mainserver & ".contact").expires = #31/12/2010#
end if

Set JMail = Server.CreateObject("JMail.SMTPMail") 
JMail.Priority = 1 ' 1 - highest priority (Urgent)  3 - normal  5 - lowest 
JMail.Sender = request.form("email")
If Trim(JMail.Sender) = "" Then JMail.Sender = contact_adminmail
JMail.SenderName = request.form("name")
JMail.replyto = request.form("email")
If Trim(request.form("email")) = "" Then JMail.replyto = ""
JMail.Subject = guest__catename & "：" & request.form("subject")
JMail.AddRecipient contact_adminmail
JMail.ServerAddress = front_mailserver
JMail.Charset = "gb2312"
' The body property is both read and write. 
' If you want to append text to the body you can 
' use JMail.Body = JMail.Body & "Hello world!" 
' or you can use JMail.AppendText "Hello World!" 
' which in many cases is easier to use. 
JMail.Body = "您好，这是客户通过公司网站提交的，内容如下：" & vbCrLf & vbCrLf &_
request.form("content") & vbCrLf & vbCrLf &_ 
"联系人：" & request.form("name") & " 的联系信息：" & vbCrLf &_
"称呼：" & request.form("chenghu") & vbCrLf &_
"公司名称：" & request.form("co") & vbCrLf &_
"电话：" & request.form("tel") & vbCrLf &_
"传真：" & request.form("fax")
if (request.form("url") <> "") AND (request.form("url") <> "http://") then
	JMail.Body = JMail.Body  & vbCrLf & "主页：" & request.form("url")
end If
JMail.Body  = JMail.Body & vbcrlf & vbcrlf & "来自：http://www." & front_mainserver

On Error Resume Next

JMail.Execute 
end if
%>
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: set variables

If (CStr(Request("MM_insert")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "guestbook"
'  MM_editRedirectUrl = ""
  MM_fieldsStr  = "html|value|this_language|value|cate_id|value|subject|value|content|value|name|value|chenghu|value|co|value|tel|value|fax|value|EMail|value|url|value"
  MM_columnsStr = "html|',none,''|this_language|',none,''|cate_id|',none,''|subject|',none,''|content|',none,''|name|',none,''|chenghu|',none,''|co|',none,''|tel|',none,''|fax|',none,''|email|',none,''|url|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Insert Record: construct a sql insert statement and execute it

If (CStr(Request("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End if
    MM_tableValues = MM_tableValues & MM_columns(i)
    MM_dbValues = MM_dbValues & FormVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If guest__thistype = True Then delete_buycookie '删除cookie信息
	 If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If
End If
%>
<%
dim this_pro_title,para_s
para_s = 0
this_pro_title = ""
if request.querystring("pro")<> "" then this_pro_title = request.querystring("pro") '从产品介绍订购

'从性能参数表订购
if request("para_id") <> "" then
	para_s = 1
	para_id = SafeRequest("para_id",1)

Dim rsPRO
Dim rsPRO_numRows

Set rsPRO = Server.CreateObject("ADODB.Recordset")
sql = "SELECT para_class.para_class_id,para_class.id,products.name as name,para_class.table_item,para_class.table_items,para_class.table_head,para_class.table_bottom,para_class.table_rows,para_class.table_rows_items,para_class.digits_items FROM (para inner join para_class on para.id=para_class.para_class_id) inner join products on products.id=para_class.id WHERE para_id = " & para_id
rsPRO.open sql,MM_conn_STRING,1,1
this_pro_title = rsPRO("name")
rsPRO__MMColParam = rsPRO("para_class_id")
this_pro_head = replace(rsPRO("table_head"),"""","")
'参数表

dim para_string,para_s_s,para_string_s,para_i,para_ii,para_iii
para_s_s = rsPRO("table_items")
para_string_s = rsPRO("table_item")

para_i = split(para_string_s,",")							'字段数组

j2=-1

if j1 >0 then												'把每个字段序号值加1
	for i= LBound(para_ii) to UBound(para_ii)
	para_ii(i)=para_ii(i) +1								'para_ii这个数组包含了rsPROPARA数据库里需要跨行显示的字段序号
	next
end if

if j2 >0 then												'把每个字段序号值加1
	for i= LBound(para_iii) to UBound(para_iii)
	para_iii(i)=para_iii(i) +1								'para_iii这个数组包含了rsPROPARA数据库里需要格式化的小数
	next
end if

para_string = "SELECT para_id, " & para_string_s & " FROM para WHERE para_id = " & para_id


Dim rsPROPARA
Dim rsPROPARA_numRows

Set rsPROPARA = Server.CreateObject("ADODB.Recordset")
rsPROPARA.open para_string, MM_conn_STRING, 1, 1

end if
'插入完毕
%>
<%'以下为表单
dim mode
mode = request.querystring("action")
if mode = "ok" then '提交在线联系
Response.Write ("<h1 class=""atten"">" & label_thanks & "</h1>")
else
%>
<div class="frmsub">
					<form action="<%=MM_editAction_1%>action=ok<%=(this_lan2)%>" method="post" name="form1" onsubmit="return noerror(form1)">
					<input type="hidden" name="this_language" value="<%=(front__language)%>" />
					<input type="hidden" name="cate_id" value="<%=(rsGUEST__MMColParam)%>" />
 					<p><label><%=(label_title)%></label>
						  <%if this_pro_title <>"" then
						  response.write(this_pro_title)
						  response.write("<input type=""hidden"" name=""subject"" value=""" & this_pro_title & """ />")
						  elseif request.querystring("pro")<> "" then
						  response.write(request.querystring("pro"))
						  response.write("<input type=""hidden"" name=""subject"" value=""" & request.querystring("pro") & """ />")
						  else%>
							<input type="text" name="subject" size="46" class="iL" style="behavior:url(#default#savehistory)" /><%end if%>
					</p>
<%if para_s =1 then				'如果是从性能参数表里来的
para_buy_s = "<table><thead>" & this_pro_head & "</thead><tbody><tr>"
	for j = 1 to para_s_s						'数据集字段序号
		para_buy_s = para_buy_s & "<td>" & rsPROPARA(j) & "</td>"
	next
para_buy_s = para_buy_s & "</tr></tbody></table>"
rsPRO.close()
set rsPRO = nothing
rsPROPARA.CLOSE()
set rsPROPARA = nothing
response.write("<div class=""buypara""><h3>性能参数</h3>" & para_buy_s & "</div>")
end if'如果是从性能参数表里来的%>
					<p class="htextarea"><label><%=(label_title2)%></label>
						<textarea name="content" rows="7" class="iL" style="behavior:url(#default#savehistory)"></textarea>
					</p>
					<p><label><%=(label_title3)%></label>
						<input type="text" name="name" size="18" class="iM" value="<%=request.Cookies(front_mainserver & ".contact")("name")%>" style="behavior:url(#default#savehistory)" />
						<%=label_titlel3%> <input type="radio" name="chenghu" value="<%=label_titlel3%>" <%if request.Cookies(front_mainserver & ".contact")("chenghu") = label_titlel3 then response.write(" selected")%> /> <%=label_titlel4%><input type="radio" name="chenghu"  value="<%=label_titlel4%>" <%if request.Cookies(front_mainserver & ".contact")("chenghu") = label_title14 then response.write(" selected")%> /> <%If label_titlel5 <> "" then%><%=label_titlel5%><input type="radio" name="chenghu" value="<%=label_titlel5%>"<%if request.Cookies(front_mainserver & ".contact")("chenghu") = label_titlel5 then response.write(" selected")%> /><%End if%>
					</p>
					<p><label><%=(label_title4)%></label>
							<input type="text" name="co" style="behavior:url(#default#savehistory)" size="46" class="iL" value="<%=request.Cookies(front_mainserver & ".contact")("co")%>" />
					</p>
					<p><label><%=(label_title5)%></label>
						<input type="text" name="tel" size="18" class="iM"  value="<%=request.Cookies(front_mainserver & ".contact")("tel")%>" style="ime-mode:disabled;behavior:url(#default#savehistory)" />
					</p>
					<p><label><%=(label_title6)%></label>
						<input type="text" name="fax" size="18" class="iM"  value="<%=request.Cookies(front_mainserver & ".contact")("fax")%>" style="ime-mode:disabled;behavior:url(#default#savehistory)" />
					</p>
					<p><label><%=(label_title7)%></label>
						<input type="text" name="EMail" size="18" class="iMM"  value="<%=request.Cookies(front_mainserver & ".contact")("EMail")%>" style="ime-mode:disabled;behavior:url(#default#savehistory)" />
							<span class="needs"><%=labe1_title11%> </span><input type="text" name="identify" maxlength="5" class="iS" style="ime-mode:disabled" /> <img src="/_identify.asp" style="margin-bottom:-1px" class="iSS" />
					</p>
					<p><label><%=(label_title8)%></label>
						<input type="text" name="url" size="46" class="iL" style="ime-mode:disabled;behavior:url(#default#savehistory)" value="<%if request.cookies(front_mainserver & ".contact")("url") <> "" then
							response.write(request.cookies(front_mainserver & ".contact")("url"))
							else
							response.write("http://")
							end if%>" />
					</p>
					<p class="sbutton"><%if form_submit_style =1 then
						  response.write("<input class=""frmSub"" type=""submit"" name=""sbutton"" value=""" & label_submit & """ />" )
						  else
						  response.write("<input type=""image"" name=""sbutton"" src=""images/submit_" & form_style & ".gif"" />")
						  end if%>&nbsp; &nbsp;<input title="<%=label_title9%>" type="checkbox" name="remember" value="true" checked="checked" /><cite><%=label_title10%></cite>
					</p>
					  <input type="hidden" name="action" value="ok" />
					  <input type="hidden" name="MM_insert" value="true" />
					  <input type="hidden" name="html" value="<%=Replace(para_buy_s,vbcrlf,"")%>">
					</form>
</div>
<%
if this_lan ="en" then
select case form_style
case 0
	label_title1 ="Please input product"
case 1
	label_title1 ="Please input product"
case 2
	label_title1 ="请输入您要应聘的职位"
case 3
	label_title1 ="请输入您投诉的对象"
case 4
	label_title1 ="Please input your question"
case 5
	label_title1 ="Please input subject"
case 6
	label_title1 ="Please input subject"
case else
	label_title1 ="Please input subject"
end select
label_title2 ="Please input your name"
label_title3 ="Please input your phone"
label_title4 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
label_title5 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
lable_title40 ="Enter the code shown"	'验证码添加
elseif this_lan ="jp" Then
select case form_style
case 0
	label_title1 ="Please input product"
case 1
	label_title1 ="Please input product"
case 2
	label_title1 ="请输入您要应聘的职位"
case 3
	label_title1 ="请输入您投诉的对象"
case 4
	label_title1 ="Please input your question"
case 5
	label_title1 ="Please input subject"
case 6
	label_title1 ="Please input subject"
case else
	label_title1 ="Please input subject"
end select
label_title2 ="Please input your name"
label_title3 ="Please input your phone"
label_title4 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
label_title5 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
lable_title40 ="Enter the code shown"	'验证码添加
elseif this_lan ="kr" Then
select case form_style
case 0
	label_title1 ="Please input product"
case 1
	label_title1 ="Please input product"
case 2
	label_title1 ="请输入您要应聘的职位"
case 3
	label_title1 ="请输入您投诉的对象"
case 4
	label_title1 ="Please input your question"
case 5
	label_title1 ="Please input subject"
case 6
	label_title1 ="Please input subject"
case else
	label_title1 ="Please input subject"
end select
label_title2 ="Please input your name"
label_title3 ="Please input your phone"
label_title4 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
label_title5 ="Your email box in incorrect.\nYour email box will not display in the public webpage"
lable_title40 ="Enter the code shown"	'验证码添加
else
select case form_style
case 0
	label_title1 ="请输入要购买的产品"
case 1
	label_title1 ="请输入要咨询的产品名称"
case 2
	label_title1 ="请输入您要应聘的职位"
case 3
	label_title1 ="请输入您投诉的对象"
case 4
	label_title1 ="请输入您要咨询的问题"
case 5
	label_title1 ="请输入您需要的服务"
case 6
	label_title1 ="请输入留言主题"
case else
	label_title1 ="请输入主题"
end select
label_title2 ="请输入您的姓名"
label_title3 ="请输入您的联系电话"
label_title4 ="请输入正确的 email，不用担心垃圾邮件，因为您的Email不会出现在公开的网页上。\n您提交的信息将自动发给相应部门的负责人。"
label_title5 ="您输入的 email 地址不正确，不用担心垃圾邮件，因为您的Email不会出现在公开的网页上。\n您提交的信息将自动发给相应部门的负责人。"
lable_title40 ="请输入验证码，验证码是右边图形的数字"	'验证码添加
end if


%>
<script type="text/javascript">
//<![CDATA[
function chklogin(form1)
{
    if (document.form1.subject.value == "")
		{
		alert("<%=(label_title1)%>");
		document.form1.subject.focus();
		return false;
		}
    if (document.form1.name.value == "")
		{
		alert("<%=(label_title2)%>");
		document.form1.name.focus();
		return false;
		}
	if (document.form1.tel.value == "")
    {
        alert("<%=(label_title3)%>");
        document.form1.tel.focus();
        return false;
    }
<% if this_lan = "en" then%>
	if (document.form1.EMail.value==""){
		alert("<%=(label_title4)%>");
		document.form1.EMail.focus();
		return false;
	}
<%end if%>
	if (document.form1.identify.value==""){
		alert("<%=(lable_title40)%>");
		document.form1.identify.focus();
		return false;
	}
	return true;
}

function chkmail(aa, graf)
{
   var pos = 0;
   var num = -1;
   var i = -1;
   var email = new Array()
   
   while (pos != -1)
   {
      pos = graf.indexOf(";",i+1);
      num += 1;
      if (pos == -1) { email[num] = graf.substring(i+1,graf.length); }
      else { email[num] = graf.substring(i+1,pos); }
      i = pos;
   }
   for ( i = 0 ; i <= num ; i++ )
   {
     if (email[i].length > 0)
     {
       l=email[i].indexOf("@");
       j=email[i].indexOf(".",l);
       k=email[i].indexOf(",");
       kk=email[i].indexOf(" ");
       jj=email[i].lastIndexOf(".") + 1;
       ll=email[i].indexOf(":");
       mm=email[i].indexOf("(");
       nn=email[i].indexOf(")");
       oo=email[i].indexOf("");
       len=email[i].length;

       if ((l <= 0) || (j <= (1+1)) || (k != -1) || (kk != -1) || (len-jj < 2) || (len-jj > 3) || (ll != -1) || (mm != -1 ) || (nn != -1) || (oo != -1))
       {
       	   if ( aa == "" ) { alert("<%=(label_title5)%>"); }
       	   else { alert("<%=(label_title5)%>"); }
           return false;
       }
     }
   }
   return true;
}

function noerror(form1)
{
    if ( !chkmail('',form1.EMail.value)){ return( false ) ; }
    if ( !chklogin(form1)){ return( false ) ; }
	 if (document.getElementById("buylist")){form1.html.value = trimbuyhtml(document.getElementById("buylist").innerHTML)}
	 DisableButton(form1.sbutton);
    return( true ) ;
}

function DisableButton(b)
{
	b.disabled = true;
	if (b.type != 'image')b.value = '<%=label_titlel2%>';
}

function trimbuyhtml(data){
	data = data.replace(/<\/form>|<form[^>]*>|<div class=dvhidden[^\n]*<\/div>| onmouseover[^> ]*| onmouseout[^> ]*| onclick[^> ]*|<div id=\"buy1_s\" class=\"dvhidden\">[^\n]*<\/div>|<DIV class=b_l_s>[^\n]*<\/div>/gmi,"");
	data = data.replace(/<input(\S*)/gmi,"<input disabled ")
	return data;
}
//]]>
</script>
<%End if '提交在线联系
end sub%>