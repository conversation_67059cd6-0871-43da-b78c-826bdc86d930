<%
sub sql_command(ByVal str)
	Dim conn
	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	conn.execute str
	set conn=nothing
End sub

Function nav_str
'--通过菜单权限和菜单生成导航html
	nav_str = "<ul id=""b_nav"">"
	Dim navarray1,rightarray,i,j,class_s
	reDim navarray(nav_max_i,2)
	navarray1 = Split(nav,"|")
	rightarray = Split(right_str,",")
	For i = LBound(navarray1) To UBound(navarray1)
		navarray(i,0)= Split(navarray1(i),",")(0)
		navarray(i,1)= Split(navarray1(i),",")(1)
		navarray(i,2)= Split(navarray1(i),",")(2)
	next

	For i = LBound(rightarray) To UBound(rightarray)
		If i = LBound(rightarray) Then			'主菜单li样式
			class_s = "class=""b_navstart"""
		ElseIf i = UBound(rightarray) Then
			class_s = "class=""b_navend"""
		Else
			class_s = "class=""b_nav"""
		End if

		For j = LBound(navarray1) To UBound(navarray1)
			If rightarray(i) = navarray(j,0) Then
				nav_str = nav_str & "<li " & class_s & "><a href=""" & navarray(j,2) & """><span>" & navarray(j,1) & "</span></a>" & subnav(rightarray(i)) & "</li>"
				Exit For
			End if
		next
	Next
	nav_str = nav_str & "</ul>"
End Function

function inint2(str,i)	'查以,号分割的二维数据（字符串）里第一维是否包括 i,i为数字，返回权限值
	inint2 = 0
	dim str_tmp,j
	if not isnull(str) then 
	str_tmp = split(str,",")
		for j = LBound(str_tmp) to ubound(str_tmp) Step 2
			if (not isnull(str_tmp(j)) AND (str_tmp(j) <>"")) then
				if cint(str_tmp(j)) = i Or str_tmp(j) = i then
					inint2 = cint(str_tmp(j+1))
					exit for
				end if
			end if
		next
	end if
end Function

Function flag_bid(str)	'取出flag中的板面id值，输出字符串
	flag_bid = ""
	dim str_tmp,j
	if not isnull(str) then 
	str_tmp = split(str,",")
		for j = LBound(str_tmp) to ubound(str_tmp) Step 2
			flag_bid = flag_bid & str_tmp(j) & ","
		Next
		If Right(flag_bid,1)="," Then flag_bid = left(flag_bid,Len(flag_bid)-1)
	end If
	If flag_bid = "" Then flag_bid = "0"
End Function

'Dim back_menuin, back_menu2() ,back_menu_0
dim back_menuin_0, back_menu_1, back_menu_active, back_url, back_url_1	'获取主菜单数组，当前主菜单
back_url= Request.ServerVariables("URL")
back_url_1 = len(back_url) - InStrRev(back_url,"/")				'back_url的长度
back_url = Right(back_url,back_url_1)							'获取页面文件名
back_menuin_0 = split(back_menuin,"|")
for back_i = LBound(back_menuin_0) to UBound(back_menuin_0)
 if back_menuin_0(back_i) <> "" then
	back_menuin_1 = split(back_menuin_0(back_i),",")
		for back_i_1 = LBound(back_menuin_1) to UBound(back_menuin_1)
			if instr(back_url,back_menuin_1(back_i_1)) = 1 then
			back_menu_active = back_i										'当前主菜单序号
			exit for
			end if
		Next
	if back_menu_active <> "" then exit for
	back_menuin_1 = ""
 end if
Next

function inint(str,i)	'查以,号分割的字符串里是否包括 i,i为字符
	inint = false
	dim str_tmp,j
	if not isnull(str) then 
	str_tmp = split(str,",")
		for j = LBound(str_tmp) to ubound(str_tmp)
			if (not isnull(str_tmp(j)) AND (str_tmp(j) <>"")) Then
				if Trim(str_tmp(j)) = i then
					inint = true
					exit for
				end if
			end if
		next
	end if
end function

Function inint_classid(str)	'检查权限中是否有栏目
inint_classid = False
if not isnull(str) then 
str_tmp = split(str,",")
	for j = LBound(str_tmp) to ubound(str_tmp)
		if IsNumeric(Trim(str_tmp(j))) then
			inint_classid = true
			exit for
		end if
	next
end if
End Function

Function trim_lesson(str)	'从串中去掉lesson1等
	trim_lesson = str
	if not isnull(str) then 
		If InStr(str,"lesson") > 0 then
		trim_lesson_2 = ""
		str_tmp = split(str,", ")
			for j = LBound(str_tmp) to ubound(str_tmp)
				if InStr(str_tmp(j),"lesson") <=0  Then
					trim_lesson_2 = trim_lesson_2 & str_tmp(j) & ", "
				End if
			Next
		trim_lesson = trim_lesson_2
		end If
	End If
	If trim_lesson = "" Then trim_lesson = "100000"	'如果没有任何
End Function

Function str_lesson(str)	'在串中加上单引号
	if not isnull(str) then 
		str_lesson_2 = ""
		str_tmp = split(str,", ")
			for j = LBound(str_tmp) to ubound(str_tmp)
				str_lesson_2 = str_lesson_2 & "'" &  str_tmp(j) & "', "
			Next
		str_lesson = str_lesson_2
		end If
End Function

Function first_lesson(str)	'session("flag") 中第一个课程区ID
	if not isnull(str) then 
		str_tmp = split(str,", ")
			for j = LBound(str_tmp) to ubound(str_tmp)
				If InStr(str_tmp(j),"lesson") > 0 Then
					first_lesson = Right(Trim(str_tmp(j)),Len(Trim(str_tmp(j)))-6)
					Exit for				
				End If
			Next
	end If
End Function

sub deletefile(filename,filedir)			'删除 admin/. 只能用于 admin 文件夹下的文件里包含
	dim File,fdir,Path
	Set File = CreateObject("Scripting.FileSystemObject")
	fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
	set fdir = File.getfile(fdir)
	set fdir = fdir.parentfolder
	set fdir = fdir.parentfolder								'获取父父目录
	If filedir  = "" Then
		Path = fdir & "\"  & filename
	else
		Path = fdir & "\" & filedir & "\" & filename
	End if
	if file.FileExists(Path) then File.DeleteFile(Path)
	set File = nothing
End Sub

Sub SaveToFile(strBody,File_path)
    Dim objStream
    Set objStream = Server.CreateObject("ADODB.Stream")
    With objStream
        .Type = 2
        .Open
        .Position = 0
        .Charset = "UTF-8"
        .WriteText strBody
		  .Position = 2
        .SaveToFile Server.MapPath(File_path),2
        .Close
    End With
    Set objStream = Nothing
End Sub

Sub deletefile2(File_path)
	dim File
	Set File = CreateObject("Scripting.FileSystemObject")
	if file.FileExists(Server.MapPath(File_path)) then File.DeleteFile(Server.MapPath(File_path))
	set File = nothing
End Sub

Function HTMLEncode(Str)
 If Isnull(Str) Then
     HTMLEncode = ""
     Exit Function 
 End If
 Str = Replace(Str,Chr(0),"", 1, -1, 1)
 Str = Replace(Str, """", "&quot;", 1, -1, 1)
 Str = Replace(Str,"<","&lt;", 1, -1, 1)
 Str = Replace(Str,">","&gt;", 1, -1, 1) 
 Str = Replace(Str, "script", "&#115;cript", 1, -1, 0)
 Str = Replace(Str, "SCRIPT", "&#083;CRIPT", 1, -1, 0)
 Str = Replace(Str, "Script", "&#083;cript", 1, -1, 0)
 Str = Replace(Str, "script", "&#083;cript", 1, -1, 1)
 Str = Replace(Str, "object", "&#111;bject", 1, -1, 0)
 Str = Replace(Str, "OBJECT", "&#079;BJECT", 1, -1, 0)
 Str = Replace(Str, "Object", "&#079;bject", 1, -1, 0)
 Str = Replace(Str, "object", "&#079;bject", 1, -1, 1)
 Str = Replace(Str, "applet", "&#097;pplet", 1, -1, 0)
 Str = Replace(Str, "APPLET", "&#065;PPLET", 1, -1, 0)
 Str = Replace(Str, "Applet", "&#065;pplet", 1, -1, 0)
 Str = Replace(Str, "applet", "&#065;pplet", 1, -1, 1)
 Str = Replace(Str, "[", "&#091;")
 Str = Replace(Str, "]", "&#093;")
 Str = Replace(Str, """", "", 1, -1, 1)
 Str = Replace(Str, "=", "&#061;", 1, -1, 1)
 Str = Replace(Str, "'", "''", 1, -1, 1)
 Str = Replace(Str, "select", "sel&#101;ct", 1, -1, 1)
 Str = Replace(Str, "execute", "&#101xecute", 1, -1, 1)
 Str = Replace(Str, "exec", "&#101xec", 1, -1, 1)
 Str = Replace(Str, "join", "jo&#105;n", 1, -1, 1)
 Str = Replace(Str, "union", "un&#105;on", 1, -1, 1)
 Str = Replace(Str, "where", "wh&#101;re", 1, -1, 1)
 Str = Replace(Str, "insert", "ins&#101;rt", 1, -1, 1)
 Str = Replace(Str, "delete", "del&#101;te", 1, -1, 1)
 Str = Replace(Str, "update", "up&#100;ate", 1, -1, 1)
 Str = Replace(Str, "like", "lik&#101;", 1, -1, 1)
 Str = Replace(Str, "drop", "dro&#112;", 1, -1, 1)
 Str = Replace(Str, "create", "cr&#101;ate", 1, -1, 1)
 Str = Replace(Str, "rename", "ren&#097;me", 1, -1, 1)
 Str = Replace(Str, "count", "co&#117;nt", 1, -1, 1)
 Str = Replace(Str, "chr", "c&#104;r", 1, -1, 1)
 Str = Replace(Str, "mid", "m&#105;d", 1, -1, 1)
 Str = Replace(Str, "truncate", "trunc&#097;te", 1, -1, 1)
 Str = Replace(Str, "nchar", "nch&#097;r", 1, -1, 1)
 Str = Replace(Str, "char", "ch&#097;r", 1, -1, 1)
 Str = Replace(Str, "alter", "alt&#101;r", 1, -1, 1)
 Str = Replace(Str, "cast", "ca&#115;t", 1, -1, 1)
 Str = Replace(Str, "exists", "e&#120;ists", 1, -1, 1)
 Str = Replace(Str,Chr(13),"<br>", 1, -1, 1)
 HTMLEncode = Replace(Str,"'","''", 1, -1, 1)
End Function
%>