<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%'移到最上方的函数要修改
page_style_id = 0	'产品页面类型，对于非产品页面，都是0
if (Request.QueryString("subclass") <> "") then rsSUBNAME__MMColParam = Request.QueryString("subclass")
Set rslist = Server.CreateObject("ADODB.Recordset")
sql = "select list.*,area.cate_type_id from list inner join area on list.cate_id=area.cate_id where list.id=" & rsSUBNAME__MMColParam
rslist.open sql,MM_conn_STRING,1,1
If Not rslist.eof Then
	pro_name_str = "列表"
	page_name_str = "页面"
	If rslist("cate_type_id") = 1 Then
		pro_name_str = "产品"
		page_name_str = "详细内容"
	ElseIf rslist("cate_type_id") = 2 then
		pro_name_str = "版块"
		page_name_str = "帖子"
	End If
	list_name = rslist("name")
End If
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<style type="text/css">
/* <![CDATA[ */
div.b_content_wapper{min-height:600px;}
/* ]]> */ 
</style>
<h1 class="b_ch1"><%=pro_name_str%>页面管理 - <%=list_name%></h1>
<div class="b_back"><a href="page.asp?class=<%=rslist("cate_id")%>">&lt;&lt; 选择其他<%=pro_name_str%></a></div>
<script type="text/javascript">
//<![CDATA[
var list_id=<%=rslist("id")%>,list_sort=<%=rslist("date_sort")%>,list_pro='<%=pro_name_str%>',list_page='<%=page_name_str%>',list_style_id=<%=rslist("style_id")%>,page_style_id=<%=page_style_id%>;
Event.onDOMReady(page2);
function page2(){
	if($("list")){gotoPage("");}
	if($("list1")){gotoPro(list_id)}
}
function clsp(obj){
	obj.parentNode.parentNode.style.display = "none";
	$A($$('ul.ulmain a.view','div.navnum a.add')).each(function(node){if(node.hasClassName('ac')){node.removeClassName('ac')}});
}
function ShowDialog(url, width, height) {
	var arr = showModalDialog(url, window, "dialogWidth:" + width + "px;dialogHeight:" + height + "px;help:no;scroll:no;status:no");
}

function modimg(pageid){
	ShowDialog('img.htm', 350, 100, true);
}
function addimg(pageid){
	ShowDialog('img.htm', 350, 100, true);
}
<!-- 0 -->
function gotoPage(p){
	var url = 'page_ajax_area0.asp';
	var pars = 'id=' + list_id + '&sort='+ list_sort + '&p='+ p + '&pro=' + list_pro +  '&page=' + list_page + '&page_style_id=' + page_style_id;
	var myAjax = new Ajax.Updater($("list").down('dd'), url, {method: 'get', parameters: pars});
}
function screen(id){
	var url = 'page_ajax_area0_screen.asp';
	var pars = 'pageid=' + id;
	var myAjax = new Ajax.Updater($("p__" + id), url, {method: 'get', parameters: pars});
}
function del(id){
	if(confirm("真的删除这个" + list_page + "吗？")){
		var url = 'page_ajax_area0_del.asp';
		var pars = 'pageid=' + id;
		var myAjax = new Ajax.Request(url, {method: 'get', parameters: pars,asynchronous:false});
		gotoPage(($$('div.navnum a.ac')[0])?$$('div.navnum a.ac')[0].innerHTML:"");
	}
}
function sort(id){
	$('sortmenu' + id).style.display = "block";
	Event.observe($('sortmenu' + id),'mouseover',function(){$('sortmenu' + id).style.display='block';});
	Event.observe($('sortmenu' + id),'mouseout',function(){$('sortmenu' + id).style.display='none';});
}
function sorthide(id){
	$('sortmenu' + id).style.display = "none";
}
function sortmenu(id,po){
	var url = 'page_ajax_area0_sort.asp';
	var pars = 'pageid=' + id + '&po=' + po + '&list_id=' + list_id + '&sort='+ list_sort ;
	var myAjax = new Ajax.Request(url, {method: 'get', parameters: pars,onSuccess:function(){gotoPage(($$('div.navnum a.ac')[0])?$$('div.navnum a.ac')[0].innerHTML:"")}});
}
function addPage(list_id){//增加页面时，显示加入的空表单
	$A($$('ul.ulmain a.view','div.navnum a.add')).each(function(node){if(node.hasClassName('ac')){node.removeClassName('ac')}});
	if($$('div.navnum')[0].firstChild.className = 'add'){$$('div.navnum')[0].firstChild.className = 'add ac'}
	var url = 'page_ajax_area0_detail.asp';
	var pars = 'list_id=' + list_id + '&action=add' + '&style=' + '<%=hb_css%>' + '&list_style_id=' + list_style_id;
	var myAjax = new Ajax.Updater($("pdetail").down('dd'), url, {method: 'get', parameters: pars});
	if($('pdetail').style.display!="block"){$('pdetail').style.display ="block";}
	$('pdetail').style.top = "190px";$('pdetail').style.left="80px";$('pdetail').style.right = "auto";
	if($("list1") && ($("list1").down('dd').style.display != "none")){//收起产品详细
		$("list1").down('dd').style.display = "none";
		$("list1").down('dt',0).addClassName("down");
		$("list1").down('dt',0).down('a.pucker').addClassName("down");			
	}
}
function view(pageid){
	$A($$('ul.ulmain a.view','div.navnum a.add')).each(function(node){if(node.hasClassName('ac')){node.removeClassName('ac')}});
	var url = 'page_ajax_area0_detail.asp';
	var pars = 'pageid=' + pageid + '&action=view' + '&style=' + '<%=hb_css%>'+ '&list_style_id=' + list_style_id;
	var myAjax = new Ajax.Updater($("pdetail").down('dd'), url, {method: 'get', parameters: pars});
	if($('pdetail').style.display!="block"){$('pdetail').style.display = "block";}
	$('pdetail').style.top = "220px";$('pdetail').style.left = "auto";$('pdetail').style.right = "60px";
	if (!$('p__' + pageid).down(1).hasClassName('ac')){$('p__' + pageid).down(1).addClassName('ac')}

	if($("list1") && ($("list1").down('dd').style.display != "none")){//收起产品详细
		$("list1").down('dd').style.display = "none";
		$("list1").down('dt',0).addClassName("down");
		$("list1").down('dt',0).down('a.pucker').addClassName("down");			
	}

}
function Pageok (){//增加页面，提交
	if (document.frmpdetail.title.value == ''){alert("输入标题");document.frmpdetail.title.focus;return false;}
	var url = 'page_ajax_area0_detail.asp';
	url = url + '?actionok=' + document.frmpdetail.actionok.value + '&list_id=' + document.frmpdetail.list_id.value + '&pageid=' + document.frmpdetail.pageid.value + '&keywords=' + encodeURIComponent(escape(document.frmpdetail.keywords.value)) + '&title=' + encodeURIComponent(escape(document.frmpdetail.title.value));
	if (document.frmpdetail.img != undefined){
		url = url + '&img=' + escape(document.frmpdetail.img.value) + '&imgsmall=' + escape(document.frmpdetail.imgsmall.value);
	}
	if (document.frmpdetail.data_mod != undefined){
		url = url + '&data_mod=' + encodeURIComponent(escape(document.frmpdetail.data_mod.value));
	}

	var postBodystr = ($('htmlbuilder0'))?((htmlbuilder0.htmlbuilder.document.body.innerHTML=="")?document.frmpdetail.content.value:htmlbuilder0.htmlbuilder.document.body.innerHTML):"";
	postBodystr = escape(postBodystr);
	var myAjax = new Ajax.Request(url,{postBody: postBodystr,onSuccess:function(){
	$('pdetail').style.display = "none";
	gotoPage((document.frmpdetail.actionok.value == "add")?"":($$('div.navnum a.ac')[0])?$$('div.navnum a.ac')[0].innerHTML:"")}
	});
	return false;
}
<!-- 0  end -->

<!-- 1  -->
function gotoPro(p){
	var url = 'page_ajax_area1_0.asp';
	var pars = 'id=' + list_id + '&style=' + '<%=hb_css%>';
	var myAjax = new Ajax.Updater($("list1").down('dd'), url, {method: 'get', parameters: pars});
}
function Prook (){
//	if (document.prodetail.content.value == ''){alert("输入名称");document.prodetail.p_name.focus;return false;}
	var url = 'page_ajax_area1_0.asp';
	url = url + '?actionok=' + document.prodetail.actionok.value + '&id=' + document.prodetail.id.value + '&content=' + encodeURIComponent(escape(document.prodetail.p_content.value));
	var postBodystr = (htmlbuilder1.htmlbuilder.document.body.innerHTML=="")?document.prodetail.p_content2.value:htmlbuilder1.htmlbuilder.document.body.innerHTML;
	postBodystr = escape(postBodystr);
	var myAjax = new Ajax.Request(url,{postBody:postBodystr,onSuccess:function(){gotoPro(list_id)}});
	return false;
}
function modlistimg(str){
var arr = showModalDialog("img.htm?type=image&sImgArgu=" + escape(str), window, "dialogWidth:350px;dialogHeight:130px;help:no;scroll:no;status:no");
}
<!-- 1  end -->

//]]>
</script>

<%
If rslist("cate_type_id") = 1 Then		'产品属性

If  UBound(pro_content_array)= 1 And  pro_content_array(1) <> "page+2.asp?subclass=" Then		'如果没有概述，则转向
	response.redirect(pro_content_array(i+1) & rsSUBNAME__MMColParam )
	response.end
End if

%>
<div class="pronav"><%
For i = LBound(pro_content_array) To UBound(pro_content_array) Step 2%>
<a href="<%=pro_content_array(i+1)%><%=rsSUBNAME__MMColParam%>"<%If Left(pro_content_array(i+1),Len(back_url))=back_url Then response.write(" class=ac")%>><%=pro_content_array(i)%></a>	
<%Next%></div>

<dl id="list1" class="horizontal">
	<dd></dd>
</dl>
<%End if%>
<%If rslist("cate_type_id") = 0  Or rslist("cate_type_id") = 99 Then		'对于普通区域与次区域%>
<dl id="list" class="horizontal">
	<dt<%if rslist("cate_type_id") <> 1 then response.write(" style=""display:none""")%>><p><%=list_name%><%=page_name_str%></p><a href="javascript:void(null)" class="pucker"></a></dt>
	<dd></dd>
</dl>
<dl id="pdetail">
	<dt><a href="javascript:void(null)" class="pclose" id="pclose" title="关闭" onclick="clsp(this)"></a></dt>
	<dd></dd>
</dl>
<%End if%>
<!--#include file ="_bottom.asp"-->
<%
rslist.close()
Set rslist = nothing
%>