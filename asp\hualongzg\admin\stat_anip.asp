<%@LANGUAGE="VBSCRIPT" codepage="65001"%>

<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel<2 then Response.Redirect "stat_help.asp?id=004&error=您没有查看IP统计的权限。"

%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>IP统计</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指图形柱或者图形柱下的数字可以看到对应的访问量</font>
  </td></tr>
</table>
<br>
<%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")
%>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="350" align=center>
<tr height="10">
	<td class=td0 width="120"></td><td class=td0 width="230"><img src="stat_images/chart_head.gif"></td>
</tr>
<%

sql="select vip,count(id) as allip from view group by vip order by count(id) DESC"
rs.Open sql,conn,1,1

maxallip=0
sumallip=0
do while not rs.EOF
	if clng(rs("allip"))>maxallip then maxallip=clng(rs("allip"))
	sumallip=sumallip+clng(rs("allip"))
	rs.MoveNext
loop
'防止除数为0出错
if maxallip=0 then maxallip=1
if sumallip=0 then sumallip=1

rs.MoveFirst 

j=0
do while not rs.EOF
theip=rs("vip")
vallip=rs("allip")
	thelen=len(theip)
	if thelen =0 then
		theip="main.asp"
		svip="通过收藏或直接输入网址访问"
	end if
	if thelen <= 33 and thelen > 0 then
		svip=theip
	end if
	if thelen >= 34 then
		svip=left(theip,31) & "..."
	end if
%>
<tr>
<td class=td0 width="120" align=right><a title="<%=theip%>，访问<%=vallip%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallip*1000/sumallip)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12><%=svip%>&nbsp;</font></a> </td>
<td class=td0 width="230" background="stat_images/chart_b2.gif" align=left>
<img style="BORDER-left: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	width="<%=(vallip/maxallip)*183%>" height="9"
	alt="<%=theip%>，访问<%=vallip%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallip*1000/sumallip)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><font class=p12> <%=vallip%></font></td>
</tr>
<%
rs.MoveNext
	j=j+1
	'如果记录超过40条，就退出
	if j=40 then exit do
loop
%>
<tr height="10">
	<td class=td0 width="120"></td><td class=td0 width="230"><img src="stat_images/chart_bottom.gif"></td>
</tr>

<tr height="5"><td class=td0 colspan=29></td></tr>
</table>
<%
rs.Close

set rs=nothing
conn.Close 
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->
<%
'计算指定日期的访问量
function vdaycon(theday)
    theday=cdate(theday)
    thetday=cdate(theday+1)
    tmprs=conn.execute("Select count(id) as vdaycon from view where" & _
		" vtime>=datevalue('" & theday & "') and vtime<=datevalue('" & thetday & "')")
    vdaycon=tmprs("vdaycon")
	if isnull(vdaycon) then vdaycon=0
end function
%>