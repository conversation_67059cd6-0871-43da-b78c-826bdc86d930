<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
dim table_sql(1),table_sql_pro(1)				'数据库表修改sql
table_sql(0) = ""
table_sql(1) = ""
table_sql_pro(0) = ""
table_sql_pro(1) = ""
img_small_kuang = ""
img_maxwidth = 140
img_maxheight = 105
'*****************************************************************************************

'*** File Upload to: ../img, Extensions: "GIF,JPG,JPEG", Form: form2, Redirect: "", "file", "500000", "uniq"
'*** Pure ASP File Upload Modify Version by xPilot-----------------------------------------------------
' Copyright 2000 (c) George Petrov
'
' Script partially based on code from Philippe Collignon 
'              (http://www.asptoday.com/articles/20000316.htm)
'
' New features from GP:
'  * Fast file save with ADO 2.5 stream object
'  * new wrapper functions, extra error checking
'  * UltraDev Server Behavior extension
'
' Copyright 2001-2002 (c) Modify by xPilot
' *** Date: 12/15/2001 ***
' *** 支持所有双字节文件名，而且修复了原函数中遇到空格也会自动截断文件名的错误！ ***
' *** 保证百分百以原文件名保存上传文件！***
' *** Welcome to visite pilothome.yeah.net <NAME_EMAIL> to me！***
'
' Version: 2.0.1 Beta for GB2312,BIG5,Japan,Korea ...
'------------------------------------------------------------------------------
Sub BuildUploadRequest(RequestBin,UploadDirectory,storeType,sizeLimit,nameConflict)
  'Get the boundary
  PosBeg = 1
  PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
  if PosEnd = 0 then
    Response.Write "<b>Form was submitted with no ENCTYPE=""multipart/form-data""</b><br>"
    Response.Write "Please correct the form attributes and try again."
    Response.End
  end if
  'Check ADO Version
	set checkADOConn = Server.CreateObject("ADODB.Connection")
	adoVersion = CSng(checkADOConn.Version)
	set checkADOConn = Nothing
	if adoVersion < 2.5 then
    Response.Write "<b>You don't have ADO 2.5 installed on the server.</b><br>"
    Response.Write "The File Upload extension needs ADO 2.5 or greater to run properly.<br>"
    Response.Write "You can download the latest MDAC (ADO is included) from <a href=""www.microsoft.com/data"">www.microsoft.com/data</a><br>"
    Response.End
	end if		
  'Check content length if needed
	Length = CLng(Request.ServerVariables("HTTP_Content_Length")) 'Get Content-Length header
	If "" & sizeLimit <> "" Then
    sizeLimit = CLng(sizeLimit)
    If Length > sizeLimit Then
      Request.BinaryRead (Length)
      Response.Write "Upload size " & FormatNumber(Length, 0) & "B exceeds limit of " & FormatNumber(sizeLimit, 0) & "B"
      Response.End
    End If
  End If
  boundary = MidB(RequestBin,PosBeg,PosEnd-PosBeg)
  boundaryPos = InstrB(1,RequestBin,boundary)
  'Get all data inside the boundaries
  Do until (boundaryPos=InstrB(RequestBin,boundary & getByteString("--")))
    'Members variable of objects are put in a dictionary object
    Dim UploadControl
    Set UploadControl = CreateObject("Scripting.Dictionary")
    'Get an object name
    Pos = InstrB(BoundaryPos,RequestBin,getByteString("Content-Disposition"))
    Pos = InstrB(Pos,RequestBin,getByteString("name="))
    PosBeg = Pos+6
    PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(34)))
    Name = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
    PosFile = InstrB(BoundaryPos,RequestBin,getByteString("filename="))
    PosBound = InstrB(PosEnd,RequestBin,boundary)
    'Test if object is of file type
    If  PosFile<>0 AND (PosFile<PosBound) Then
      'Get Filename, content-type and content of file
      PosBeg = PosFile + 10
      PosEnd =  InstrB(PosBeg,RequestBin,getByteString(chr(34)))
      FileName = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      FileName = Mid(FileName,InStrRev(FileName,"\")+1)
      'Add filename to dictionary object
      UploadControl.Add "FileName", FileName
      Pos = InstrB(PosEnd,RequestBin,getByteString("Content-Type:"))
      PosBeg = Pos+14
      PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
      'Add content-type to dictionary object
      ContentType = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      UploadControl.Add "ContentType",ContentType
      'Get content of object
      PosBeg = PosEnd+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = FileName
      ValueBeg = PosBeg-1
      ValueLen = PosEnd-Posbeg
    Else
      'Get content of object
      Pos = InstrB(Pos,RequestBin,getByteString(chr(13)))
      PosBeg = Pos+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      ValueBeg = 0
      ValueEnd = 0
    End If
    'Add content to dictionary object
    UploadControl.Add "Value" , Value	
    UploadControl.Add "ValueBeg" , ValueBeg
    UploadControl.Add "ValueLen" , ValueLen	
    'Add dictionary object to main dictionary
    UploadRequest.Add name, UploadControl	
    'Loop to next object
    BoundaryPos=InstrB(BoundaryPos+LenB(boundary),RequestBin,boundary)
  Loop

  GP_keys = UploadRequest.Keys
  for GP_i = 0 to UploadRequest.Count - 1
    GP_curKey = GP_keys(GP_i)
    'Save all uploaded files
    if UploadRequest.Item(GP_curKey).Item("FileName") <> "" then
      GP_value = UploadRequest.Item(GP_curKey).Item("Value")
      GP_valueBeg = UploadRequest.Item(GP_curKey).Item("ValueBeg")
      GP_valueLen = UploadRequest.Item(GP_curKey).Item("ValueLen")

      if GP_valueLen = 0 then
        Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
        Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
        Response.Write "File does not exists or is empty.<br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
	  	  response.End
	    end if
      
      'Create a Stream instance
      Dim GP_strm1, GP_strm2
      Set GP_strm1 = Server.CreateObject("ADODB.Stream")
      Set GP_strm2 = Server.CreateObject("ADODB.Stream")
      
      'Open the stream
      GP_strm1.Open
      GP_strm1.Type = 1 'Binary
      GP_strm2.Open
      GP_strm2.Type = 1 'Binary
        
      GP_strm1.Write RequestBin
      GP_strm1.Position = GP_ValueBeg
      GP_strm1.CopyTo GP_strm2,GP_ValueLen
    
      'Create and Write to a File
      GP_curPath = Request.ServerVariables("PATH_INFO")
      GP_curPath = Trim(Mid(GP_curPath,1,InStrRev(GP_curPath,"/")) & UploadDirectory)
      if Mid(GP_curPath,Len(GP_curPath),1)  <> "/" then
        GP_curPath = GP_curPath & "/"
      end if 
      GP_CurFileName = UploadRequest.Item(GP_curKey).Item("FileName")
      GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & GP_CurFileName
      'Check if the file alreadu exist
      GP_FileExist = false
      Set fso = CreateObject("Scripting.FileSystemObject")
      If (fso.FileExists(GP_FullFileName)) Then
        GP_FileExist = true
      End If      
      if nameConflict = "error" and GP_FileExist then
        Response.Write "<B>File already exists!</B><br><br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
				GP_strm1.Close
				GP_strm2.Close
	  	  response.End
      end if
      if ((nameConflict = "over" or nameConflict = "uniq") and GP_FileExist) or (NOT GP_FileExist) then
        if nameConflict = "uniq" and GP_FileExist then
          Begin_Name_Num = 0
          while GP_FileExist    
            Begin_Name_Num = Begin_Name_Num + 1
            GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
            GP_FileExist = fso.FileExists(GP_FullFileName)
          wend  
          UploadRequest.Item(GP_curKey).Item("FileName") = fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
				UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
		  end if
        on error resume next

'改文件名 modify 2005.10
		Randomize
		RanNum = Int(90000*rnd)+10000
		TempStr = Year(now) & Month(now) & Day(now) & Hour(now) & Minute(now) & Second(now) & RanNum & "." & fso.GetExtensionName(GP_CurFileName)
      GP_FullFileName = Trim(Server.mappath(GP_curPath)) & "\" & TempStr
		UploadRequest.Item(GP_curKey).Item("FileName") = TempStr
		UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
'改文件名


        GP_strm2.SaveToFile GP_FullFileName,2
        if err then
          Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
          Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
          Response.Write "Maybe the destination directory does not exist, or you don't have write permission.<br>"
          Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
    		  err.clear
  				GP_strm1.Close
  				GP_strm2.Close
  	  	  response.End
  	    end if
  			GP_strm1.Close
  			GP_strm2.Close
  			if storeType = "path" then
  				UploadRequest.Item(GP_curKey).Item("Value") = GP_curPath & UploadRequest.Item(GP_curKey).Item("Value")
  			end if
        on error goto 0
      end if
    end if
  next

End Sub

'把普通字符串转成二进制字符串函数
Function getByteString(StringStr)
    getByteString=""
  For i = 1 To Len(StringStr) 
    XP_varchar = mid(StringStr,i,1)
    XP_varasc = Asc(XP_varchar) 
    If XP_varasc < 0 Then 
       XP_varasc = XP_varasc + 65535 
    End If 

    If XP_varasc > 255 Then 
       XP_varlow = Left(Hex(Asc(XP_varchar)),2) 
       XP_varhigh = right(Hex(Asc(XP_varchar)),2) 
       getByteString = getByteString & chrB("&H" & XP_varlow) & chrB("&H" & XP_varhigh) 
    Else 
       getByteString = getByteString & chrB(AscB(XP_varchar)) 
    End If 
  Next 
End Function

'把二进制字符串转换成普通字符串函数 
Function getString(StringBin)
   getString =""
   Dim XP_varlen,XP_vargetstr,XP_string,XP_skip
   XP_skip = 0 
   XP_string = "" 
 If Not IsNull(StringBin) Then 
      XP_varlen = LenB(StringBin) 
    For i = 1 To XP_varlen 
      If XP_skip = 0 Then
         XP_vargetstr = MidB(StringBin,i,1) 
         If AscB(XP_vargetstr) > 127 Then 
           XP_string = XP_string & Chr(AscW(MidB(StringBin,i+1,1) & XP_vargetstr)) 
           XP_skip = 1 
         Else 
           XP_string = XP_string & Chr(AscB(XP_vargetstr)) 
         End If 
      Else 
      XP_skip = 0
   End If 
    Next 
 End If 
      getString = XP_string 
End Function 

Function UploadFormRequest(name)
  on error resume next
  if UploadRequest.Item(name) then
    UploadFormRequest = UploadRequest.Item(name).Item("Value")
  end if  
End Function

'Process the upload
UploadQueryString = Replace(Request.QueryString,"GP_upload=true","")
if mid(UploadQueryString,1,1) = "&" then
	UploadQueryString = Mid(UploadQueryString,2)
end if

GP_uploadAction = CStr(Request.ServerVariables("URL")) & "?GP_upload=true"
If (Request.QueryString <> "") Then  
  if UploadQueryString <> "" then
	  GP_uploadAction = GP_uploadAction & "&" & UploadQueryString
  end if 
End If

If (CStr(Request.QueryString("GP_upload")) <> "") Then
  GP_redirectPage = ""
  If (GP_redirectPage = "") Then
    GP_redirectPage = CStr(Request.ServerVariables("URL"))
  end if
    
  RequestBin = Request.BinaryRead(Request.TotalBytes)
  Dim UploadRequest
  Set UploadRequest = CreateObject("Scripting.Dictionary")  
  BuildUploadRequest RequestBin, "../img", "file", "500000", "uniq"

 '*** GP NO REDIRECT
end if  
if UploadQueryString <> "" then
  UploadQueryString = UploadQueryString & "&GP_upload=true"
else  
  UploadQueryString = "GP_upload=true"
end if  
%>
<%'添加
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction = CStr(Request.ServerVariables("URL")) 'MM_editAction = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction = MM_editAction & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: (Modified for File Upload) set variables

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "img"
  MM_editRedirectUrl = "top_img.asp?url=" & server.urlencode("top_img.asp?action=add&id=" & UploadFormRequest("fmsubid"))
  MM_fieldsStr  = "fmimg|value|imgwidth|value|imgheight|value|p_id|value|img_alt|value|p_type|value"
  MM_columnsStr = "img|',none,''|imgwidth|none,none,NULL|imgheight|none,none,NULL|p_id|none,none,NULL|img_alt|',none,''|p_type|',none,''|"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & UploadQueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & UploadQueryString
    End If
  End If

End If
%>
<%
' *** Insert Record: (Modified for File Upload) construct a sql insert statement and execute it

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End if
    MM_tableValues = MM_tableValues & MM_columns(i)
    MM_dbValues = MM_dbValues & FormVal
  Next

if UploadFormRequest("fmimg")<>"" then '如果是标准栏目、图片栏目、展示栏目，生成缩略图，水印
	call made_thumb("..\img",UploadFormRequest("fmimg"),"img_big",img_maxwidth,img_maxheight,false,img_small_kuang,false,"add")
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & "," & table_sql(0) & ") values (" & MM_dbValues & "," & table_sql(1) &")"

	if UploadFormRequest("imgwatermark") = "1" then call watermark(UploadFormRequest("fmimg"),"logo.gif",0,0.3,"&HFFFFFF")
else
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"
end if

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
	  <h1 class="b_ch1">首页底部滚动图片</h1>
注意：最多显示12张图
<%
Const p_type="class"
Const p_id = "1,2"
Const p_name = "首页滚动"
Const direct = "v"	'排列方向 v为竖直，h为水平

must_submit = "'img_alt','','R','fmimg','','R'"
%>

<script language="JavaScript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='* 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- 必须输入数字.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '* 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
    document.MM_returnValue = (errors == '');


}
function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} 
}

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(gif|jpg|png|bmp|jpeg)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('上传文件太小!\n应大于 ' + img.minWidth + ' x ' + img.minHeight); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('上传文件太大!\n不超过 ' + img.maxWidth + ' x ' + img.maxHeight); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('上传文件太大!\n不超过 ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}
//-->
</script>
<table width = 700 class=table0 border=0>
<tr><td width=250 valign=top align=center class=td0>
<table width=250 class=table0 cellpadding=0 cellspacing=0>
<%
set rs=server.createobject("adodb.recordset")
sql = "select * from img"
rs.open sql,MM_conn_STRING
while not rs.eof%>
<tr align=center><td class=p12 class=td0><a onClick="MM_openBrWindow('../img.asp?img_dir=img&img=<%=(rs("img"))%>&title=<%= HTMLEncode(rs("img_alt"))%>&imgwidth=<%=(rs.Fields.Item("imgwidth").Value)%>&imgheight=<%=(rs.Fields.Item("imgheight").Value)%>','','scrollbars=yes,resizable=yes,width=300,height=200')" href="javascript:void(null)" onmouseout="window.status='';return true;" 
onmouseover="window.status='单击打开详图';return true;"><img src="../img/<%=rs("img_big")%>" width=<%=rs("img_bigwidth")%> height=<%=rs("img_bigheight")%> border=0></a>
<br><%=rs("img_alt")%><br><a href=top_img_del.asp?id=<%=request.querystring("id")%>&imgid=<%=rs("id")%> onClick="GP_popupConfirmMsg('确定删除这个图？');return document.MM_returnValue"><u>删除</u></td></tr>
<%
rs.movenext()
wend
rs.close()
set rs=nothing
%>
</table>
</td>
<td class=td0 valign=top>
<table width=450 align=center>
<form METHOD="POST" name="form1" enctype="multipart/form-data" action="<%=MM_editAction%>" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG',false,200,'','','','','imgwidth','imgheight');return document.MM_returnValue">
<input type=hidden name=p_id value="<%=request.querystring("id")%>">
<tr>
<td width="60" nowrap class=p12>*图形简介</td>
<td> 
  <input type="text" name="img_alt" maxlength=20 size="90" class=i400>
</td>
</tr>
  <tr id="content_img"> 
	<td width="126" class=p12>*详图</td>
	<td width="400" class=p12>
	<input type=hidden name="imgwidth">
	<input type=hidden name="imgheight">
	  <input type="file" name="fmimg" size="40" onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,200,'','','','','imgwidth','imgheight')">
	  <br>请用jpg或gif格式，为了保证网站的兼容性，图片名称请用英文</td>
  </tr>
		  <tr id="content_img2"> 
			<td width="50" class=p12>图形选项</td>
			<td width="400" nowrap class=p12>
<table border=0 class=table0><tr><td class=td0>
<font color=#ff0000>水印:</font> 否 
			  <input type="radio" name="imgwatermark" value="0" checked>
			  <img src="/images/spacer.gif" width=40 height=1> 是 
			  <input type="radio" name="imgwatermark" value="1"></td>
</tr><tr><td colspan=3 class="td0 p12">（水印是在大图片上加上<%=back_coname%>的标志与链接，保护版权）</td>
			  </tr>
</table>
			  </td>
		  </tr>
  <tr bgcolor="<%=(back_menubackcolor)%>"> 
	<td>&nbsp;</td>
	<td width="400">
	  <input type="submit" name="fmSubmit" value=" 增加 "onClick="MM_validateForm(<%=must_submit%>);return document.MM_returnValue">
	</td>
  </tr>
</table>
<input type="hidden" name="MM_insert" value="true">
</form>
</td>
</tr>
</table>
<%
'///////////////////////////////////////////////////////////////////////////////////
sub img_thumb_pro(path,imgname,str,add_mod)
	'对多张图生成缩略图的函数,并生成相应的sql查询语句 其中 logo.gif 为 ..\images\目录下的 logo.gif
	'path 源图相对路径（最后不带\）
	'img 源图名称
	'str的格式 "col,imgwidth,imgheight,watermark,background" 6个一组
	'img_thumb_pro("..\img","do.gif","img,450,450,false,k.gif,thumb,150,150,false,xk.gif,true")
	'col 为该缩略图所保存的表列名称；
	'imgwidth为保存时的最大宽度，数值，0不限制
	'imgheight为保存时的最大高度，数值，0不限制
	'watermark为是否加水印，逻辑；
	'background 装饰框图形，如果为''，则不加装饰框，装饰框图位于 ../images 目录
	'crop_on 是否切图与指定宽高一样
	'add_mod 生成的sql是用于update 还是add
	'如img_thumb_pro("..\img","banner_ditu_01.jpg","img2,210,138,false,kuang.gif,true,img_small,150,50,false,kuang.gif,false","update")
dim thumb_i,thumb_array,img_thumb_i
thumb_i = split(str,",")
redim thumb_array((ubound(thumb_i)+1)/6-1,5)

'数据进数组
img_thumb_j=0
img_thumb_j2=0
for img_thumb_i = lbound(thumb_i) to ubound(thumb_i)
	thumb_array(img_thumb_j,img_thumb_j2) = thumb_i(img_thumb_i)
	img_thumb_j2 = img_thumb_j2 + 1
	if img_thumb_j2 = 6 then
		img_thumb_j = img_thumb_j + 1
		img_thumb_j2 = 0
	end if
next

'生成缩略图与sql
for img_thumb_i = 0 to (ubound(thumb_i)+1)/6-1
	call made_thumb(path,imgname,thumb_array(img_thumb_i,0),cint(thumb_array(img_thumb_i,1)),cint(thumb_array(img_thumb_i,2)),cbool(thumb_array(img_thumb_i,3)),thumb_array(img_thumb_i,4),cbool(thumb_array(img_thumb_i,5)),add_mod)

	table_sql_pro(0) = table_sql_pro(0) & table_sql(0)
	table_sql_pro(1) = table_sql_pro(1) & table_sql(1)

	if img_thumb_i < (ubound(thumb_i)+1)/6-1 then 
	table_sql_pro(0) = table_sql_pro(0) & ","
		if add_mod <> "update" then table_sql_pro(1) = table_sql_pro(1) & ","
	end if
next
end sub

sub made_thumb(path,img,col,imgwidth,imgheight,watermark_on,background,crop_on,up_add)	
	'生成缩略图，源图相对路径（最后不带\，也是目录图相对路径），源图文件名，缩略图尺寸，尺寸中不能有0
	'输出缩略图名称, 宽度, 高度
	'如果要写框，则图的宽高为框图的宽高
	'crop_on是否剪切img图，使其合上尺寸
	'up_add 为生成的sql是update还是添加
	'如 made_thumb("..\img\","banner_ditu_01.jpg","img2",210,138,false,"kuang.gif",true,"update")
	dim bigimg,bigimgpath,thumbpath,bgpath
	bigimgpath = Server.MapPath(path) & "\" & img
	thumbpath = Server.MapPath(path) & "\" & col & "__"  & img
	if background <> "" then
		bgpath = Server.MapPath("..\images") & "\" & background
		set kuangimg = Server.CreateObject("Persits.Jpeg")
		kuangimg.open bgpath
	else
		bgpath = ""
	end if

	Set bigimg = Server.CreateObject("Persits.Jpeg")
	bigimg.Open bigimgpath

	if bigimg.OriginalWidth/bigimg.OriginalHeight < imgwidth/imgheight then	'如果原图比较高
		if bigimg.OriginalHeight <= imgheight then	'如果原图太小
			img_col_name = col
			img_width_name = col & "width"
			img_height_name = col & "height"
			img_col_value = img
			img_width_value = cstr(bigimg.OriginalWidth) 
			img_height_value = cstr(bigimg.OriginalHeight)
		else
			if crop_on = true then	'如果要求剪切
				if bigimg.OriginalWidth <= imgwidth then	'源图较小
						bigimg.Width = bigimg.OriginalWidth
					else
						bigimg.Width = imgwidth
				end if
				bigimg.Height = bigimg.Width * bigimg.OriginalHeight/bigimg.OriginalWidth
				bigimg.Crop 0,cint((bigimg.Height-imgheight)/2),bigimg.Width,bigimg.Height-cint((bigimg.Height-imgheight)/2)
				bigimg.Save thumbpath
			else
				bigimg.Width = imgheight * bigimg.OriginalWidth/bigimg.OriginalHeight
				bigimg.Height = imgheight
				bigimg.Save thumbpath
			end if
			if watermark_on = true then call watermark(col & "__"  & img,"logo.gif",0,0.3,"&HFFFFFF")
			if background <> "" then
				call img_inimg(background,col & "__"  & img,0)
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(kuangimg.OriginalWidth) 
				img_height_value = cstr(kuangimg.OriginalHeight)
			else
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(bigimg.Width) 
				img_height_value = cstr(bigimg.Height)
			end if
		end if
	else	'如果原图比较宽
		if bigimg.OriginalWidth <= imgwidth then	'如果原图太小
			img_col_name = col
			img_width_name = col & "width"
			img_height_name = col & "height"
			img_col_value = img
			img_width_value = cstr(bigimg.OriginalWidth) 
			img_height_value = cstr(bigimg.OriginalHeight)
		else
			if crop_on = true then	'如果要求剪切
				if bigimg.OriginalHeight <= imgheight then	'源图较小
						bigimg.Height = bigimg.OriginalHeight
					else
						bigimg.Height = imgheight
				end if
				bigimg.Width = bigimg.Height * bigimg.OriginalWidth/bigimg.OriginalHeight
				bigimg.Crop cint((bigimg.Width-imgwidth)/2),0,bigimg.Width-cint((bigimg.Width-imgwidth)/2),bigimg.Height
				bigimg.Save thumbpath
			else
				bigimg.Width = imgwidth
				bigimg.Height = imgwidth * bigimg.OriginalHeight/bigimg.OriginalWidth
				bigimg.Save thumbpath
			end if
			if watermark_on = true then call watermark(col & "__"  & img,"logo.gif",0,0.3,"&HFFFFFF")
			if background <> "" then
				call img_inimg(background,col & "__"  & img,0)
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(kuangimg.OriginalWidth) 
				img_height_value = cstr(kuangimg.OriginalHeight)
			else
				img_col_name = col
				img_width_name = col & "width"
				img_height_name = col & "height"
				img_col_value = col & "__"  & img
				img_width_value = cstr(bigimg.Width) 
				img_height_value = cstr(bigimg.Height)
			end if
		end if
	end if

	if up_add = "update" then
		table_sql(0) = img_col_name & "='" & img_col_value & "'," & img_width_name & "=" & img_width_value & "," & img_height_name & "="  & img_height_value
		table_sql(1) = ""
	else
		table_sql(0) = img_col_name & "," & img_width_name & "," & img_height_name
		table_sql(1) = "'" & img_col_value & "'," & img_width_value & "," & img_height_value
	end if

	bigimg.close()
	set bigimg = nothing
	if background <> "" then
		kuangimg.close()
		set kuangimg = nothing
	end if

end sub

'***********************************************************************************
'因为标志较大，太小的图没有必要打水印，除非另作一水印
'position: 0为中 1,2,3,4依次为左上、右上，右下，左下
'opacity: 不透明度 0-1
'transparence_color: 透明色，为16进制值，格式为 &HFFFFFF

Sub watermark(image,logoimage,position,opacity,transparence_color)

dim Img,Logo,ImgPath,LogoPath
Set Img = Server.CreateObject("Persits.Jpeg") 
Set Logo = Server.CreateObject("Persits.Jpeg") 
'Call OutScript("parent.UploadError('指示: " & sUploadDir & " | "  & sSaveFileName & "')")

ImgPath = Server.MapPath("..\img") & "\" & image 
Img.Open ImgPath 

LogoPath = Server.MapPath("..\images") & "\" & logoimage
Logo.Open LogoPath 

'大小适应，即确定logo.width,logo.height的值
if  Logo.OriginalWidth / Logo.OriginalHeight >= Img.OriginalWidth / Img.OriginalHeight then
	if Logo.OriginalWidth > Img.OriginalWidth then
		Logo.Width = Img.OriginalWidth
		Logo.Height = Img.OriginalWidth / Logo.OriginalWidth  * Logo.OriginalHeight
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
else
	if Logo.OriginalHeight > Img.OriginalHeight then
		Logo.Height = Img.OriginalHeight
		Logo.Width = Img.OriginalHeight / Logo.OriginalHeight  * Logo.OriginalWidth
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
end if

'写位置
dim Logopadding_left,logopadding_height
select case position
case 0
Logopadding_left = cint((Img.OriginalWidth - Logo.Width)/2)
logopadding_height = cint((Img.OriginalHeight - Logo.Height)/2)
case 1
Logopadding_left = 0
logopadding_height = 0
case 2
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = 0
case 3
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
case 4
Logopadding_left = 0
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
end select

Img.Canvas.DrawImage Logopadding_left, logopadding_height, Logo,opacity,transparence_color,100
Img.Save Server.MapPath("../img/" & image)	'保存 
Img.close()
Logo.close()
set Img=nothing
set Logo = nothing

end Sub

'********************************************************
Sub img_inimg(bgimg,fimg,position)

dim Img,Logo,ImgPath,LogoPath
Set Img = Server.CreateObject("Persits.Jpeg") 
Set Logo = Server.CreateObject("Persits.Jpeg") 
'Call OutScript("parent.UploadError('指示: " & sUploadDir & " | "  & sSaveFileName & "')")

ImgPath = Server.MapPath("..\images") & "\" & bgimg 
Img.Open ImgPath 

LogoPath = Server.MapPath("..\img") & "\" & fimg
Logo.Open LogoPath 

'大小适应，即确定fimg.width,logo.height的值
if  Logo.OriginalWidth / Logo.OriginalHeight >= Img.OriginalWidth / Img.OriginalHeight then
	if Logo.OriginalWidth > Img.OriginalWidth then
		Logo.Width = Img.OriginalWidth
		Logo.Height = Img.OriginalWidth / Logo.OriginalWidth  * Logo.OriginalHeight
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
else
	if Logo.OriginalHeight > Img.OriginalHeight then
		Logo.Height = Img.OriginalHeight
		Logo.Width = Img.OriginalHeight / Logo.OriginalHeight  * Logo.OriginalWidth
		else
		Logo.Width = Logo.OriginalWidth
		Logo.Height = Logo.OriginalHeight
	end if
end if

'写位置
dim Logopadding_left,logopadding_height
select case position
case 0
Logopadding_left = cint((Img.OriginalWidth - Logo.Width)/2)
logopadding_height = cint((Img.OriginalHeight - Logo.Height)/2)
case 1
Logopadding_left = 0
logopadding_height = 0
case 2
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = 0
case 3
Logopadding_left = cint(Img.OriginalWidth - Logo.Width)
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
case 4
Logopadding_left = 0
logopadding_height = cint(Img.OriginalHeight - Logo.Height)
end select

Img.Canvas.DrawImage Logopadding_left, logopadding_height, Logo
Img.Save Server.MapPath("../img/" & fimg)	'保存 
Img.close()
Logo.close()
set Img=nothing
set Logo = nothing

end Sub%>
<!--#include file ="_bottom.asp"-->