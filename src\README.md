# 华隆重工企业官网

这是长沙华隆重型机器制造有限公司的现代化企业官网，采用响应式设计，支持多语言，具有良好的用户体验和SEO优化。

## 项目概述

华隆重工是一家专业的重型机械制造商，主要致力于钢铁冶金、电力、矿山、建材等行业各种机械配套设备的设计、研发、制造、销售和服务。

## 技术栈

- **HTML5**: 语义化标记
- **TailwindCSS**: 响应式CSS框架
- **JavaScript**: 原生JavaScript，无依赖
- **FontAwesome**: 图标库
- **响应式设计**: 移动端优先

## 网站结构

```
src/
├── index.html              # 首页
├── css/
│   └── style.css          # 自定义样式
├── js/
│   └── main.js            # 主要JavaScript功能
├── images/                # 图片资源
│   ├── logo.png          # 公司Logo
│   └── products/         # 产品图片
├── products/             # 产品页面
│   ├── index.html        # 产品中心主页
│   ├── bucket-wheel-stacker.html    # 斗轮堆取料机
│   └── plate-straightener.html     # 板材矫直机
├── services/             # 服务页面
│   └── technical-renovation.html   # 技术改造
├── about/               # 关于我们
│   ├── company.html     # 华隆简介
│   └── contact.html     # 联系我们
├── cases.html           # 工程案例
└── README.md           # 项目说明
```

## 主要功能

### 1. 响应式设计
- 移动端优先的设计理念
- 支持桌面、平板、手机等各种设备
- 使用CSS Grid和Flexbox布局

### 2. 导航系统
- 固定顶部导航栏
- 下拉菜单支持
- 移动端汉堡菜单
- 面包屑导航

### 3. 多语言支持
- 支持联合国6种官方语言
- 语言切换功能
- 本地化存储用户语言偏好

### 4. 产品展示
- 产品列表页面
- 详细的产品介绍页面
- 技术参数表格
- 产品图片画廊

### 5. 交互功能
- 图片灯箱效果
- 表单验证和提交
- 搜索功能
- 返回顶部按钮
- Cookie同意横幅

### 6. SEO优化
- 语义化HTML结构
- Meta标签优化
- 结构化数据
- 图片Alt属性
- 页面加载优化

### 7. 可访问性
- ARIA标签支持
- 键盘导航
- 焦点管理
- 屏幕阅读器友好

## 页面说明

### 首页 (index.html)
- 英雄区展示
- 核心产品展示
- 服务优势介绍
- 公司简介

### 产品中心 (products/)
- 产品分类展示
- 产品详情页面
- 技术参数表格
- 相关产品推荐

### 客户服务 (services/)
- 技术改造服务
- 产品备件服务
- 服务流程介绍

### 走进华隆 (about/)
- 公司简介
- 企业理念
- 联系方式
- 在线咨询表单

### 工程案例 (cases.html)
- 项目案例展示
- 行业分类筛选
- 项目成果统计

## 样式系统

### 配色方案
- **主色调**: #1E3A8A (工业蓝)
- **辅助色**: #6B7280 (中灰)
- **强调色**: #F97316 (活力橙)
- **背景色**: #F3F4F6 (浅灰)
- **文本色**: #111827 (深灰)

### 字体系统
- **中文**: Noto Sans SC, PingFang SC, Source Han Sans, Microsoft YaHei
- **英文**: Montserrat (标题), Roboto (正文)

### 组件样式
- 卡片组件 (.product-card, .service-card)
- 按钮样式 (.btn-primary, .btn-accent)
- 表格样式 (.tech-table)
- 动画效果 (.animate-fade-in-up)

## JavaScript功能

### 核心功能
- 导航菜单控制
- 语言切换
- 图片灯箱
- 表单处理
- 通知系统

### 增强功能
- 懒加载图片
- 搜索功能
- 粘性导航
- 返回顶部
- Cookie同意

### 可访问性
- 键盘导航支持
- 焦点管理
- ARIA属性
- 屏幕阅读器支持

## 部署说明

### 本地开发
1. 直接在浏览器中打开 `index.html`
2. 或使用本地服务器（推荐）：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

### 生产部署
1. 将src目录下的所有文件上传到Web服务器
2. 确保服务器支持HTML5 History API（用于单页应用路由）
3. 配置HTTPS（推荐）
4. 设置适当的缓存策略

### CDN配置
- TailwindCSS: 通过CDN加载
- FontAwesome: 通过CDN加载
- 图片资源: 建议使用CDN加速

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 性能优化

### 已实现
- 图片懒加载
- CSS/JS压缩（生产环境）
- 响应式图片
- 缓存策略

### 建议优化
- 图片WebP格式转换
- 服务端渲染（SSR）
- 代码分割
- 预加载关键资源

## 维护说明

### 内容更新
- 产品信息：修改对应的HTML文件
- 图片资源：替换images目录下的文件
- 联系信息：更新contact.html中的信息

### 样式修改
- 全局样式：修改css/style.css
- 颜色主题：更新TailwindCSS配置
- 响应式断点：调整CSS媒体查询

### 功能扩展
- 新增页面：按照现有结构创建HTML文件
- JavaScript功能：在js/main.js中添加
- 第三方集成：在相应页面中添加脚本

## 联系信息

- **公司**: 长沙华隆重型机器制造有限公司
- **地址**: 湖南长沙环保科技产业园
- **电话**: +86-731-XXXXXXX
- **邮箱**: <EMAIL>
- **网站**: www.hualongzg.com

## 版权信息

© 2024 长沙华隆重型机器制造有限公司. 保留所有权利.

---

*本网站采用现代Web技术构建，注重用户体验、性能优化和可访问性。*
