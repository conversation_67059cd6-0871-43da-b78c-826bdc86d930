<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel<6 then Response.Redirect "stat_help.asp?id=004&error=您没有管理自定义检索条件库的权限。"

'获取数据
name		=trim(Request("name"))
content		=trim(Request("content"))
wherestr	=trim(Request("wherestr"))
outtype		=trim(Request("outtype"))
overwrite	=cbool(trim(Request("overwrite")))

'过滤单引号
name=replace(name,"'","")

'检查数据
if name="" then Response.Redirect "stat_help.asp?id=002&error=请为保存储的检索条件取名。"
if outtype="" then Response.Redirect "stat_help.asp?id=002&error=没有指明要输出的内容，请确认是在检索结果页点保存进入本页的。"
%>
<!--#include file="stat__head.asp"-->
<%
'打开数据库
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")

sql="select * from save where name='" & name & "'"
rs.Open sql,conn,3,2

if (not rs.EOF) and (not overwrite) then
%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  
  <form action="stat_an_save.asp" method=post id=form1 name=form1>
  <tr height="30">
    <td class=td012 width="100%"><font class=p14><u>保存检索条件</u><br></font><br><font color=ff0000 class=p12>注意
	数据库中已经存在一条名为“<%=name%>”的检索条件记录</font><br><br><% if ssql_view =1 then%>
	<font class=p12>检索条件</font>&nbsp;<%if rs("wherestr")="" then%>没有检索条件<%else%><%=rs("wherestr")%><%end if%><br><%end if'ssql_view=1%>
	<font class=p12>查询项目</font>&nbsp; <%=rs("outtype")%><br>
	<font class=p12>说　　明</font>&nbsp; <%=rs("content")%><br><br>
	您要保存的检索条件：<br><% if ssql_view =1 then%>
	<font class=p12>检索条件</font>&nbsp;<%if wherestr="" then%>没有检索条件<%else%><%=wherestr%><%end if%><br><%end if'ssql_view=1%>
	<font class=p12>查询项目</font>&nbsp; <%=outtype%><br>
	<font class=p12>说　　明</font>&nbsp; <%=content%><br><br>

	<INPUT type="radio" name="overwrite" value="0" checked> 将名字改为&nbsp;
	<input name="name" size="25" class="input" value="<%=name%>">
	<INPUT type="radio" name="overwrite" value="1"> 覆盖。以本次为准<br><br>
</td></tr>
<tr><td class=td014 align=right>
	<a href='javascript:history.back()'>取消</a> 
	<a href='javascript:document.form1.submit();'>保存</a>
	<input name="content" size="50" type="hidden" value="<%=content%>"><input type="hidden" name="wherestr" value="<%=wherestr%>"><input type="hidden" name="outtype" value="<%=outtype%>"></td>
  </tr>
  </form>

</table>
<br>
<%
rs.Close

else

	if rs.EOF then rs.AddNew

	rs("wherestr")=wherestr
	rs("outtype")=outtype
	rs("name")=name
	rs("content")=content
	
	rs.Update
	rs.Close
%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  
  <form action="stat_an_save.asp" method=post id=form1 name=form1>
  <tr height="30">
    <td class=td012 width="100%"><font class=p14><u>保存检索条件成功</u></font><br><br>
	<font class=p12>条件名称</font>&nbsp;<%=name%><br><% if ssql_view =1 then%>
	<font class=p12>检索条件</font>&nbsp;
	<%if wherestr="" then%>没有检索条件<%else%><%=wherestr%><%end if%><br><%end if'ssql_view=1%>
	<font class=p12>查询项目</font>&nbsp; <%=outtype%><br>
	<font class=p12>说　　明</font>&nbsp; <%=content%><br>
	<td>
	</tr>
<tr><td class=td014 align=right><a href='stat_an_search.asp'>继续</a></td>
  </tr>
  </form>
</table>
<%
end if

set rs=nothing
conn.Close
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->