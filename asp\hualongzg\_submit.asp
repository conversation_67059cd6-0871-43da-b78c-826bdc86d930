<!--#include file="ScriptLibrary/upload.asp" -->
<%
'把文件上传 
'*** Form: job_form, attachment 字段为文件名

Function GetRndFileName(sExt)
	Dim sRnd
	Randomize
	sRnd = Int(900 * Rnd) + 100
	GetRndFileName = year(now) & month(now) & day(now) & hour(now) & minute(now) & second(now) & sRnd & "." & sExt
End Function

Dim MM_editAction
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i


MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (request.querystring <> "") Then
  MM_editAction = MM_editAction & "?" & request.querystring
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: (Modified for File Upload) set variables

If (CStr(Request.QueryString("action")) <> "") Then

	Server.ScriptTimeout = 600
	Set upfile = new UpFile_Class
	upfile.AllowExt = "gif;jpg;jpeg;doc;zip;rar"
	upfile.IsDebug = true
	upfile.GetData (3200000)

	set thisfile = upfile.file("attachment")
	if thisfile.FileSize > 0  then
		sFileExt = LCase(thisfile.FileExt)
		thisfile.filename = "jobs_" & GetRndFileName(sFileExt)
		upfile.Form("attachment") = upfile.AutoSave("attachment",Server.MapPath("upload") & "\" & thisfile.FileName)
	End if
	set thisfile = nothing

  MM_editConnection = MM_conn_STRING
  MM_editTable = "jobs_human"
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "job_id|value|job_name|value|surname|value|f_name|value|birth|value|sex|value|jiguan|value|tel|value|mobile|value|email|value|qq|value|address|value|xueli|value|biyeshijian|value|xuexiao1|value|zhuanye1|value|xuexiao2|value|zhuanye2|value|jingli|value|ziwo|value|dongji|value|nextyear|value|attachment|value|attachment_description|value|links|value|whereknow|value|aihao|value"
  MM_columnsStr = "job_id|none,none,NULL|job_name|',none,''|surname|',none,''|f_name|',none,''|birth|',none,NULL|sex|',none,''|jiguan|',none,''|telphone|',none,''|mobile|',none,''|email|',none,''|QQ_icq_msn|',none,''|address|',none,''|xueli|',none,''|biye_date|',none,''|school|',none,''|zhuangye|',none,''|school2|',none,''|zhuanye2|',none,''|jingli|',none,''|ziwojieshao|',none,''|what_want|',none,''|jihua|',none,''|attachment|',none,''|attachment_descript|',none,''|links|',none,''|this_where|',none,''|aihao|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(upfile.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And request.querystring <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And request.querystring <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & request.querystring
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & request.querystring
    End If
  End If

  set upfile=Nothing
End If
%>
<%
' *** Insert Record: (Modified for File Upload) construct a sql insert statement and execute it

Dim MM_tableValues
Dim MM_dbValues

If (CStr(Request.QueryString("action")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End If
    MM_tableValues = MM_tableValues & MM_columns(MM_i)
    MM_dbValues = MM_dbValues & MM_formVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"
  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
	 MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
	     Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%'初始设置
'submit_jobs_id,submit_jobs_name的值为职位ID和职位名称
If request.querystring("id") <> "" Then
	submit_jobs_id = cint(request.querystring("id"))
	Set rs1 = Server.CreateObject("ADODB.Recordset")
	sql = "select id,name from jobs where id=" & submit_jobs_id
	rs1.Open sql,MM_conn_STRING,1,1	
	submit_jobs_name = rs1("name")
	rs1.close()
	Set rs1 = nothing
End if

dim submit_must_color

submit_must_color = "#0507A4"		'必填项目色彩

%>
<%'以下为表单
dim mode
mode = request.querystring("action")
if mode = "ok" then '提交在线联系
Response.Write ("<br><br><h1>您的简历提交成功，谢谢您的关心！<br><br>请留意您的邮箱，我们将通过邮件与您联系。</h1><br><br>")
else
%><form action="<%=MM_editAction%>&action=ok" method="post" enctype="multipart/form-data" name="job_form" onsubmit="return noerror();checkFileUpload(this,'GIF,JPG,JPEG,DOC,ZIP,RAR',false,3200,'','','','','','');return document.MM_returnValue">
<table class="jobsSubmit">
<thead><tr><th colspan="8">基本信息</th></tr></thead>
<tbody>
<tr>
	<td class="nr"><span class="needs">姓</span></td><td colspan="3"><input type="text" name="surname" class="iS"><span class="needs">名</span><input type="text" name="f_name" class="iS"></td>
	<td class="nr">性别</td>
	<td colspan="3"><select name="sex"><option value="male" selected="selected">男</option><option value="female">女</option></select></td>
</tr>
<tr>
	<td class="nr"><span class="needs">生日</span></td>
	<td colspan="3"><input type="text" name="year" size="4" maxlength="4" class="iS" style="ime-mode:disabled">年<input type="text" name="month" size="2" maxlength="2" class="iSS" style="ime-mode:disabled">月<input type="text" name="day" size="2" maxlength="2" class="iSS" style="ime-mode:disabled">日</td>
	<td class="nr">籍贯</td>
	<td colspan="3"><input type="text" name="jiguan" class="iM"></td>
</tr>
</tbody>

<thead><tr><th colspan="8">联系信息</th></tr></thead>
<tbody>
<tr>
	<td class="nr"><span class="needs">联系电话</span></td>
	<td colspan="3"><input type="text" name="tel" class="iM" style="ime-mode:disabled"></td>
	<td class="nr">手机</td>
	<td colspan="3"><input type="text" name="mobile" class="iM" style="ime-mode:disabled"></td>
</tr>
<tr>
	<td class="name jobstd1 nr"><span class="needs">电子信箱</span></td>
	<td colspan="3" class="jobstd2"><input type="text" name="email" class="iM" style="ime-mode:disabled"></td>
	<td class="name nr jobstd3">即时通讯</td>
	<td colspan="3" class="jobstd4"><input type="text" name="qq" class="iM" style="ime-mode:disabled;" title="注明类型，如QQ/MSN/ICQ等"></td>
</tr>
<tr>
	<td class="nr">通讯地址</td>
	<td colspan="7"><input type="text" name="address" class="iL"></td>
</tr>
</tbody>

<thead><tr><th colspan="8">教育背景</th></tr></thead>
<tbody>
<tr>
	<td class="nr">学历</td>
	<td colspan="3"><select name="xueli"><option value="初中">初中</option><option value="高中">高中</option><option value="职高">职高</option><option value="中专">中专</option><option value="大专" selected="selected">大专</option><option value="本科">本科</option><option value="硕士">硕士</option><option value="博士">博士</option><option value="博士后">博士后</option><option value="其他">其他</option></select></td>
	<td class="nr">毕业时间</td>
	<td colspan="3"><input type="text" name="biyeshijian" class="iM" style="ime-mode:disabled"></td>
</tr>
<tr>
	<td class="nr"><span class="needs">毕业学校</span></td>
	<td colspan="3"><input type="text" name="xuexiao1" class="iM"></td>
	<td class="nr"><span class="needs">专业一</span></td>
	<td colspan="3"><input type="text" name="zhuanye1" class="iM"></td>
</tr>
<tr>
	<td class="nr">毕业学校</td>
	<td colspan="3"><input type="text" name="xuexiao2" class="iM"></td>
	<td class="nr">专业二</td>
	<td colspan="3"><input type="text" name="zhuanye2" class="iM"></td>
</tr>
</tbody>

<thead><tr><th colspan="8">工作经验</th></tr></thead>
<tbody>
<tr>
	<td class="nr"><span class="needs">工作经历</span></td>
	<td colspan="7"><textarea name="jingli" rows="5" class="iL"></textarea></td>
</tr>
<tr>
	<td class="nr">自我评价</td>
	<td colspan="7"><textarea name="ziwo" rows="5" class="iL"></textarea></td>
</tr>
</tbody>

<thead><tr><th colspan="8">其他</th></tr></thead>
<tbody>
<tr>
	<td class="nr"><span class="needs">胜任该工作的理由</span></td>
	<td colspan="7"><textarea name="dongji" rows="5" class="iL"></textarea></td>
</tr>
<tr>
	<td class="nr"><span class="needs">未来规划</span></td><td colspan="7"><textarea name="nextyear" rows="5" class="iL" title="您在未来一年的工作规划"></textarea></td>
</tr>
<tr>
	<td class="nr">兴趣爱好</td><td colspan="7"><textarea name="aihao" rows="5" class="iL"></textarea></td>
</tr>
<tr>
	<td colspan="8">从何处获得本公司招聘信息<input type="text" name="whereknow" class="iMM"></td>
</tr>
</tbody>

<thead><tr><th colspan="8">附件<span>（附件限DOC/ZIP/RAR/JPG/GIF格式，大小不超过3MB）</span></th></tr></thead>
<tbody>
<tr>
	<td class="nr">相关附件</td>
	<td colspan="7"><input name="attachment" type="file" class="iMM ifile" onchange="checkOneFileUpload(this,'GIF,JPG,JPEG,DOC,ZIP,RAR',false,3200,'','','','','','')"></td>
</tr>
<tr>
	<td class="nr">附件说明</td><td colspan="7"><input type="text" name="attachment_description" class="iL"></td>
</tr>
<tr>
	<td class="nr">相关链接</td><td colspan="7"><input type="text" name="links" class="iL" style="ime-mode:disabled"></td>
</tr>
</tbody></table>
<input type="hidden" name="job_name" value="<%=submit_jobs_name%>">
<input type="hidden" name="job_id" value="<%=submit_jobs_id%>">
<input type="submit" value="提交简历" class="buJobsSub">
<input type="hidden" name="birth">
<input type="hidden" name="MM_insert" value="job_form">
</form>
<%end if%>