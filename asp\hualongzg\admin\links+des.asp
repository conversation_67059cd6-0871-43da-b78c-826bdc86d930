<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="../Connections/conn.asp" -->
<%
function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function									
%>
<%
' *** Edit Operations: declare variables

Dim MM_editAction
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) = "form_links2" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "link_exchang"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "site_name|value|site_url|value|site_decript|value|site_contact|value|site_email|value|my_url|value"
  MM_columnsStr = "site_name|',none,''|site_url|',none,''|site_decript|',none,''|site_contact|',none,''|site_email|',none,''|my_url|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(MM_i) & " = " & MM_formVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<!--#include file ="_config.asp"-->
<%
Dim rsLINKSDES__MMColParam
rsLINKSDES__MMColParam = "1"
If (Request.QueryString("id") <> "") Then 
  rsLINKSDES__MMColParam = Request.QueryString("id")
End If
%>
<%
Dim rsLINKSDES
Dim rsLINKSDES_numRows

Set rsLINKSDES = Server.CreateObject("ADODB.Recordset")
rsLINKSDES.ActiveConnection = MM_conn_STRING
rsLINKSDES.Source = "SELECT agree, id, my_url, site_contact, site_data, site_decript, site_email, site_name, site_url, img,imgwidth,imgheight FROM link_exchang WHERE id = " + Replace(rsLINKSDES__MMColParam, "'", "''") + ""
rsLINKSDES.CursorType = 0
rsLINKSDES.CursorLocation = 2
rsLINKSDES.LockType = 1
rsLINKSDES.Open()

rsLINKSDES_numRows = 0
%>
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">友情链接列表</h1>
<a href="links2.asp">← 返回</a>
						 <form ACTION="<%=MM_editAction%>" method="POST" name="form_links2" onsubmit="return noerror(form_links2)">
							<table align="center" width=460 cellpadding=1 border=0 style="border-collapse:collapse">
							 <tr> 
								<td bgcolor=<%=(back_menubackcolor)%> colspan=2>&nbsp;<b>网站信息</b>（对方网站的信息）</td>
							 </tr>
							 <tr> 
								<td align="right" width=60 nowrap>网站名称</td>
								<td> 
								 		<input name="site_name" type="text" class=i400 value="<%=(rsLINKSDES.Fields.Item("site_name").Value)%>">
								</td>
							 </tr>
							 <tr> 
								<td align="right">网　　址</td>
								<td> 
								 <input type="text" name="site_url" class=i400 value="<%=(rsLINKSDES.Fields.Item("site_url").Value)%>">
								</td>
							 </tr><%if trim(rsLINKSDES.Fields.Item("img").Value) <> "" then%>
							 <tr> 
								<td align="right">网站图标</td>
								<td><img src="../img/<%=rsLINKSDES.Fields.Item("img").Value%>" border=0 width=<%=rsLINKSDES("imgwidth")%> height=<%=rsLINKSDES("imgheight")%>>
								</td>
							 </tr><%end if%>
							 <tr> 
								<td align="right" valign=top>网站简介</td>
								<td> 
								 <textarea type="text" name="site_decript" cols="60" rows="6" class=i400><%=(rsLINKSDES.Fields.Item("site_decript").Value)%></textarea>
								</td>
							 </tr>
								<td bgcolor=<%=(back_menubackcolor)%> colspan=2>&nbsp;<b>联系信息</b>（对方网管的联系信息）</td>
							 </tr>
							 <tr> 
								<td align="right">联 系 人</td>
								<td> 
								 <input name="site_contact" type="text" class=i150 value="<%=(rsLINKSDES.Fields.Item("site_contact").Value)%>">
								</td>
							 </tr>
							 <tr> 
								<td align="right">电子信箱</td>
								<td> 
								 <input name="site_email" type="text" class=i400 value="<%=(rsLINKSDES.Fields.Item("site_email").Value)%>">
								</td>
							 </tr>
							 <tr> 
								<td bgcolor=<%=(back_menubackcolor)%> colspan=2><b>&nbsp;验证信息</b><font class=p12>（请填对方网站里包含 http://www.<%=(back_site)%> 链接的页面）</font></td>
							 </tr>
							 <tr> 
								<td align="right">&nbsp;</td>
								<td> 
								 <input type="text" name="my_url" class=i400 value="<%=(rsLINKSDES.Fields.Item("my_url").Value)%>">
								</td>
							 </tr>
							 <tr> 
								<td nowrap align="right">&nbsp;</td>
								<td> 
								 <input type="submit" value="确定">
								</td>
							 </tr>
							 <tr><td nowrap>申请时间</td><td><%=(rsLINKSDES.Fields.Item("site_data").Value)%></td></tr>
							</table>
							<input type="hidden" name="MM_insert" value="true">
<input type="hidden" name="action" value="ok">
<input type="hidden" name="MM_update" value="form_links2">
<input type="hidden" name="MM_recordId" value="<%= rsLINKSDES.Fields.Item("id").Value %>">
</form>
<SCRIPT language=javascript>

function chklogin(form_links2)
{
    if (document.form_links2.site_name.value == "")
		{
		alert("请输入对方网站名称");
		document.form_links2.site_name.focus();
		return false;
		}
    if (document.form_links2.site_url.value == "" || document.form_links2.site_url.value == "http://")
		{
		alert("请输入对方网站的网址");
		document.form_links2.site_url.focus();
		return false;
		}
    if (document.form_links2.site_contact.value == "")
		{
		alert("对方网站的联系人呢？");
		document.form_links2.site_contact.focus();
		return false;
		}
	if (form_links2.site_email.value==""){
		alert("请输入真实的电子信箱");
		form_links2.site_email.focus();
		return false;
	}

	return true;
}

function chkmail(aa, graf)
{
   var pos = 0;
   var num = -1;
   var i = -1;
   var email = new Array()
   
   while (pos != -1)
   {
      pos = graf.indexOf(";",i+1);
      num += 1;
      if (pos == -1) { email[num] = graf.substring(i+1,graf.length); }
      else { email[num] = graf.substring(i+1,pos); }
      i = pos;
   }
   for ( i = 0 ; i <= num ; i++ )
   {
     if (email[i].length > 0)
     {
       l=email[i].indexOf("@");
       j=email[i].indexOf(".",l);
       k=email[i].indexOf(",");
       kk=email[i].indexOf(" ");
       jj=email[i].lastIndexOf(".") + 1;
       ll=email[i].indexOf(":");
       mm=email[i].indexOf("(");
       nn=email[i].indexOf(")");
       oo=email[i].indexOf("");
       len=email[i].length;

       if ((l <= 0) || (j <= (1+1)) || (k != -1) || (kk != -1) || (len-jj < 2) || (len-jj > 3) || (ll != -1) || (mm != -1 ) || (nn != -1) || (oo != -1))
       {
       	   if ( aa == "" ) { alert("您输入的 email 地址不正确"); }
       	   else { alert("您输入的 email 地址不正确"); }
           return false;
       }
     }
   }
   return true;
}

function noerror(form_links2)
{
    if ( !chkmail('',form_links2.site_email.value)){ return( false ) ; }
    if ( !chklogin(form_links2)){ return( false ) ; }
    return( true ) ;
}

//-->
</SCRIPT>
<%
rsLINKSDES.Close()
Set rsLINKSDES = Nothing
%>
<!--#include file ="_bottom.asp"-->