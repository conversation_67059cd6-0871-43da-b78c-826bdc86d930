/*
 * jquery.tools 1.1.0 - The missing UI library for the Web
 * 
 * [tools.tooltip-1.1.0, tools.tooltip.dynamic-1.0.0]
 * 
 * Copyright (c) 2009 <PERSON><PERSON>
 * http://flowplayer.org/tools/
 *
 * Dual licensed under MIT and GPL 2+ licenses
 * http://www.opensource.org/licenses
 * 
 * -----
 * 
 * File generated: Thu Sep 03 10:53:05 GMT+00:00 2009
 */
(function(c){c.tools=c.tools||{};c.tools.tooltip={version:"1.1.0",conf:{effect:"slide",direction:"up",bounce:false,slideOffset:10,slideInSpeed:200,slideOutSpeed:200,slideFade:!c.browser.msie,fadeOutSpeed:"fast",tip:null,predelay:0,delay:30,opacity:1,lazy:undefined,position:["top","center"],cancelDefault:true,offset:[0,0],api:false,events:{def:"mouseover,mouseout",input:"focus,blur",widget:"focus mouseover,blur mouseout"}},addEffect:function(e,g,f){b[e]=[g,f]}};var b={toggle:[function(e){var f=this.getConf();this.getTip().css({opacity:f.opacity}).show();e.call()},function(e){this.getTip().hide();e.call()}],fade:[function(e){this.getTip().fadeIn(this.getConf().fadeInSpeed,e)},function(e){this.getTip().fadeOut(this.getConf().fadeOutSpeed,e)}]};var d={up:["-","top"],down:["+","top"],left:["-","left"],right:["+","left"]};c.tools.tooltip.addEffect("slide",function(e){var g=this.getConf(),h=this.getTip(),i=g.slideFade?{opacity:g.opacity}:{},f=d[g.direction]||d.up;i[f[1]]=f[0]+"="+g.slideOffset;if(g.slideFade){h.css({opacity:0})}h.show().animate(i,g.slideInSpeed,e)},function(f){var h=this.getConf(),j=h.slideOffset,i=h.slideFade?{opacity:0}:{},g=d[h.direction]||d.up;var e=""+g[0];if(h.bounce){e=e=="+"?"-":"+"}i[g[1]]=e+"="+j;this.getTip().animate(i,h.slideOutSpeed,function(){c(this).hide();f.call()})});function a(f,g){var p=this;f.data("tooltip",p);var l=f.next();if(g.tip){l=c(g.tip);if(l.length>1){l=f.nextAll(g.tip).eq(0);if(!l.length){l=f.parent().nextAll(g.tip).eq(0)}}}function h(q,r){c(p).bind(q,function(t,s){if(r&&r.call(this,s?s.position:undefined)===false&&s){s.proceed=false}});return p}function o(){var t=f.position().top-l.outerHeight();var q=l.outerHeight()+f.outerHeight();var u=g.position[0];if(u=="center"){t+=q/2}if(u=="bottom"){t+=q}var r=f.outerWidth()+l.outerWidth();var s=f.position().left+f.outerWidth();u=g.position[1];if(u=="center"){s-=r/2}if(u=="left"){s-=r}t+=g.offset[0];s+=g.offset[1];return{top:t,left:s}}c.each(g,function(q,r){if(c.isFunction(r)){h(q,r)}});var j=f.is(":input"),e=j&&f.is(":checkbox, :radio, select, :button"),i=f.attr("type"),n=g.events[i]||g.events[j?(e?"widget":"input"):"def"];n=n.split(/,\s*/);f.bind(n[0],function(r){var q=l.data("trigger");if(q&&q[0]!=this){l.hide()}r.target=this;p.show(r);l.hover(p.show,function(s){p.hide()})});f.bind(n[1],function(){p.hide()});if(!c.browser.msie&&!j){f.mousemove(function(){if(!p.isShown()){f.triggerHandler("mouseover")}})}if(g.opacity<1){l.css("opacity",g.opacity)}var m=0,k=f.attr("title");if(k&&g.cancelDefault){f.removeAttr("title");f.data("title",k)}c.extend(p,{show:function(r){if(r){f=c(r.target)}clearTimeout(l.data("timer"));if(l.is(":animated")||l.is(":visible")){return p}function q(){l.data("trigger",f);var t=o();if(g.tip&&k){l.html(k)}var s={proceed:true,position:t};c(p).trigger("onBeforeShow",s);if(s.proceed===false){return p}t=o();l.css({position:"absolute",top:t.top,left:t.left});b[g.effect][0].call(p,function(){c(p).trigger("onShow")})}if(g.predelay){clearTimeout(m);m=setTimeout(q,g.predelay)}else{q()}return p},hide:function(){clearTimeout(l.data("timer"));clearTimeout(m);if(!l.is(":visible")){return}function q(){var r={proceed:true};c(p).trigger("onBeforeHide",r);if(r.proceed===false){return}b[g.effect][1].call(p,function(){c(p).trigger("onHide")})}if(g.delay){l.data("timer",setTimeout(q,g.delay))}else{q()}return p},isShown:function(){return l.is(":visible, :animated")},getConf:function(){return g},getTip:function(){return l},getTrigger:function(){return f},onBeforeShow:function(q){return h("onBeforeShow",q)},onShow:function(q){return h("onShow",q)},onBeforeHide:function(q){return h("onBeforeHide",q)},onHide:function(q){return h("onHide",q)}})}c.prototype.tooltip=function(e){var f=this.eq(typeof e=="number"?e:0).data("tooltip");if(f){return f}var g=c.extend(true,{},c.tools.tooltip.conf);if(c.isFunction(e)){e={onBeforeShow:e}}else{if(typeof e=="string"){e={tip:e}}}c.extend(true,g,e);if(typeof g.position=="string"){g.position=g.position.split(/,?\s/)}if(g.lazy!==false&&(g.lazy===true||this.length>20)){this.one("mouseover",function(){f=new a(c(this),g);f.show()})}else{this.each(function(){f=new a(c(this),g)})}return g.api?f:this}})(jQuery);
(function(d){var c=d.tools.tooltip;c.plugins=c.plugins||{};c.plugins.dynamic={version:"1.0.0",conf:{api:false,classNames:"top right bottom left"}};function b(h){var e=d(window);var g=e.width()+e.scrollLeft();var f=e.height()+e.scrollTop();return[h.offset().top<=e.scrollTop(),g<=h.offset().left+h.width(),f<=h.offset().top+h.height(),e.scrollLeft()>=h.offset().left]}function a(f){var e=f.length;while(e--){if(f[e]){return false}}return true}d.fn.dynamic=function(g){var h=d.extend({},c.plugins.dynamic.conf),f;if(typeof h=="number"){g={speed:g}}d.extend(h,g);var e=h.classNames.split(/\s/),i;this.each(function(){if(d(this).tooltip().jquery){throw"Lazy feature not supported by dynamic plugin. set lazy: false for tooltip"}var j=d(this).tooltip().onBeforeShow(function(n){var m=this.getTip(),k=this.getConf();if(!i){i=[k.position[0],k.position[1],k.offset[0],k.offset[1],d.extend({},k)]}d.extend(k,i[4]);k.position=[i[0],i[1]];k.offset=[i[2],i[3]];m.css({visibility:"hidden",position:"absolute",top:n.top,left:n.left}).show();var l=b(m);if(!a(l)){if(l[2]){d.extend(k,h.top);k.position[0]="top";m.addClass(e[0])}if(l[3]){d.extend(k,h.right);k.position[1]="right";m.addClass(e[1])}if(l[0]){d.extend(k,h.bottom);k.position[0]="bottom";m.addClass(e[2])}if(l[1]){d.extend(k,h.left);k.position[1]="left";m.addClass(e[3])}if(l[0]||l[2]){k.offset[0]*=-1}if(l[1]||l[3]){k.offset[1]*=-1}}m.css({visibility:"visible"})});j.onShow(function(){var l=this.getConf(),k=this.getTip();l.position=[i[0],i[1]];l.offset=[i[2],i[3]]});j.onHide(function(){var k=this.getTip();k.removeClass(h.classNames);d.extend(g,i[4])});f=j});return h.api?f:this}})(jQuery);
