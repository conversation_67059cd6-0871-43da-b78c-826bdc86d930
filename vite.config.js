import { defineConfig } from "vite";
import { resolve } from "path";
import { fileURLToPath } from "url";
import fs from "fs";

const __dirname = fileURLToPath(new URL(".", import.meta.url));

// 手动定义所有 HTML 文件入口点
const input = {
  main: resolve(__dirname, "src/index.html"),
  cases: resolve(__dirname, "src/cases.html"),
  privacy: resolve(__dirname, "src/privacy.html"),
  // 产品页面
  "products/index": resolve(__dirname, "src/products/index.html"),
  "products/bucket-wheel-stacker": resolve(
    __dirname,
    "src/products/bucket-wheel-stacker.html"
  ),
  "products/blending-stacker": resolve(
    __dirname,
    "src/products/blending-stacker.html"
  ),
  "products/chain-bucket-unloader": resolve(
    __dirname,
    "src/products/chain-bucket-unloader.html"
  ),
  "products/chain-grate": resolve(__dirname, "src/products/chain-grate.html"),
  "products/disc-feeder": resolve(__dirname, "src/products/disc-feeder.html"),
  "products/plate-feeder": resolve(__dirname, "src/products/plate-feeder.html"),
  "products/plate-straightener": resolve(
    __dirname,
    "src/products/plate-straightener.html"
  ),
  // 关于页面
  "about/company": resolve(__dirname, "src/about/company.html"),
  "about/contact": resolve(__dirname, "src/about/contact.html"),
  // 服务页面
  "services/spare-parts": resolve(__dirname, "src/services/spare-parts.html"),
  "services/technical-renovation": resolve(
    __dirname,
    "src/services/technical-renovation.html"
  ),
};

export default defineConfig({
  root: "src",
  build: {
    outDir: "../dist",
    emptyOutDir: true,
    rollupOptions: {
      input: input,
    },
    assetsDir: "assets",
  },
  server: {
    port: 3000,
    open: true,
    host: true,
  },
  css: {
    postcss: "./postcss.config.js",
  },
  publicDir: "../public",
});
