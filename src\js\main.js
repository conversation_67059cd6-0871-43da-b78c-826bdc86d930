// Main JavaScript for Hualong Heavy Industry Website

// DOM Content Loaded
document.addEventListener("DOMContentLoaded", function () {
  initializeNavigation();
  initializeLanguageSwitcher();
  initializeScrollEffects();
  initializeImageGallery();
  initializeForms();
  initializeAccessibility();
});

// Navigation Functions
function initializeNavigation() {
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const mobileMenu = document.getElementById("mobile-menu");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", function () {
      mobileMenu.classList.toggle("hidden");

      // Update aria-expanded attribute
      const isExpanded = !mobileMenu.classList.contains("hidden");
      mobileMenuButton.setAttribute("aria-expanded", isExpanded);

      // Update icon
      const icon = mobileMenuButton.querySelector("i");
      if (icon) {
        icon.className = isExpanded
          ? "fas fa-times text-xl"
          : "fas fa-bars text-xl";
      }
    });
  }

  // Close mobile menu when clicking outside
  document.addEventListener("click", function (event) {
    if (
      mobileMenu &&
      !mobileMenu.contains(event.target) &&
      !mobileMenuButton.contains(event.target)
    ) {
      mobileMenu.classList.add("hidden");
      mobileMenuButton.setAttribute("aria-expanded", "false");
      const icon = mobileMenuButton.querySelector("i");
      if (icon) {
        icon.className = "fas fa-bars text-xl";
      }
    }
  });

  // Handle dropdown menus with keyboard navigation
  const dropdownButtons = document.querySelectorAll("[data-dropdown]");
  dropdownButtons.forEach((button) => {
    button.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        toggleDropdown(this);
      }
    });
  });
}

// Toggle mobile submenu
function toggleMobileSubmenu(menuId) {
  const submenu = document.getElementById(menuId + "-submenu");
  const button = event.target;
  const icon = button.querySelector("i");

  if (submenu) {
    submenu.classList.toggle("hidden");
    const isOpen = !submenu.classList.contains("hidden");

    // Update icon
    if (icon) {
      icon.className = isOpen ? "fas fa-chevron-up" : "fas fa-chevron-down";
    }

    // Update aria-expanded
    button.setAttribute("aria-expanded", isOpen);
  }
}

// Language Switcher
function initializeLanguageSwitcher() {
  const languageButtons = document.querySelectorAll("[data-lang]");

  languageButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      const lang = this.getAttribute("data-lang");
      switchLanguage(lang);
    });
  });
}

function switchLanguage(lang) {
  // Store language preference
  localStorage.setItem("preferred-language", lang);

  // Update page language
  document.documentElement.lang = lang;

  // Here you would typically load language-specific content
  // For now, we'll just show a notification
  showNotification(`语言已切换到: ${getLanguageName(lang)}`);
}

function getLanguageName(code) {
  const languages = {
    ar: "العربية",
    zh: "中文",
    en: "English",
    fr: "Français",
    ru: "Русский",
    es: "Español",
  };
  return languages[code] || code;
}

// Scroll Effects
function initializeScrollEffects() {
  // Smooth scroll for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href").substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Scroll-triggered animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-fade-in-up");
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const animateElements = document.querySelectorAll(
    ".product-card, .service-card, .case-card"
  );
  animateElements.forEach((el) => observer.observe(el));
}

// Image Gallery
function initializeImageGallery() {
  const galleryItems = document.querySelectorAll(".gallery-item");

  galleryItems.forEach((item) => {
    item.addEventListener("click", function () {
      const img = this.querySelector("img");
      if (img) {
        openLightbox(img.src, img.alt);
      }
    });

    // Keyboard support
    item.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        this.click();
      }
    });
  });
}

function openLightbox(src, alt) {
  // Create lightbox overlay
  const overlay = document.createElement("div");
  overlay.className =
    "fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50";
  overlay.setAttribute("role", "dialog");
  overlay.setAttribute("aria-label", "图片查看器");

  // Create image container
  const container = document.createElement("div");
  container.className = "relative max-w-4xl max-h-full p-4";

  // Create image
  const img = document.createElement("img");
  img.src = src;
  img.alt = alt;
  img.className = "max-w-full max-h-full object-contain";

  // Create close button
  const closeBtn = document.createElement("button");
  closeBtn.innerHTML = '<i class="fas fa-times"></i>';
  closeBtn.className =
    "absolute top-4 right-4 text-white text-2xl hover:text-gray-300 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center";
  closeBtn.setAttribute("aria-label", "关闭图片查看器");

  // Assemble lightbox
  container.appendChild(img);
  container.appendChild(closeBtn);
  overlay.appendChild(container);
  document.body.appendChild(overlay);

  // Close handlers
  const closeLightbox = () => {
    document.body.removeChild(overlay);
    document.body.style.overflow = "";
  };

  closeBtn.addEventListener("click", closeLightbox);
  overlay.addEventListener("click", function (e) {
    if (e.target === overlay) {
      closeLightbox();
    }
  });

  // Keyboard support
  document.addEventListener("keydown", function escHandler(e) {
    if (e.key === "Escape") {
      closeLightbox();
      document.removeEventListener("keydown", escHandler);
    }
  });

  // Prevent body scroll
  document.body.style.overflow = "hidden";

  // Focus management
  closeBtn.focus();
}

// Form Handling
function initializeForms() {
  const forms = document.querySelectorAll("form");

  forms.forEach((form) => {
    form.addEventListener("submit", function (e) {
      e.preventDefault();
      handleFormSubmit(this);
    });
  });
}

function handleFormSubmit(form) {
  const formData = new FormData(form);
  const submitBtn = form.querySelector('button[type="submit"]');

  // Show loading state
  if (submitBtn) {
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> 提交中...';
    submitBtn.disabled = true;
  }

  // Simulate form submission (replace with actual API call)
  setTimeout(() => {
    showNotification("表单提交成功！我们会尽快与您联系。", "success");
    form.reset();

    // Reset button
    if (submitBtn) {
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  }, 2000);
}

// Notification System
function showNotification(message, type = "info") {
  const notification = document.createElement("div");
  notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-sm ${getNotificationClasses(
    type
  )}`;
  notification.textContent = message;

  // Add close button
  const closeBtn = document.createElement("button");
  closeBtn.innerHTML = '<i class="fas fa-times"></i>';
  closeBtn.className = "ml-2 text-current opacity-70 hover:opacity-100";
  closeBtn.addEventListener("click", () => removeNotification(notification));

  notification.appendChild(closeBtn);
  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => removeNotification(notification), 5000);
}

function getNotificationClasses(type) {
  const classes = {
    info: "bg-blue-500 text-white",
    success: "bg-green-500 text-white",
    warning: "bg-yellow-500 text-black",
    error: "bg-red-500 text-white",
  };
  return classes[type] || classes.info;
}

function removeNotification(notification) {
  if (notification && notification.parentNode) {
    notification.style.opacity = "0";
    notification.style.transform = "translateX(100%)";
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
}

// Accessibility Features
function initializeAccessibility() {
  // Skip to main content link
  const skipLink = document.createElement("a");
  skipLink.href = "#main-content";
  skipLink.textContent = "跳转到主要内容";
  skipLink.className = "skip-link";
  document.body.insertBefore(skipLink, document.body.firstChild);

  // Add main content landmark
  const main = document.querySelector("main");
  if (main) {
    main.id = "main-content";
    main.setAttribute("tabindex", "-1");
  }

  // Enhance keyboard navigation
  enhanceKeyboardNavigation();
}

function enhanceKeyboardNavigation() {
  // Add keyboard support for interactive elements
  const interactiveElements = document.querySelectorAll(
    ".product-card, .service-card, .gallery-item"
  );

  interactiveElements.forEach((element) => {
    if (!element.hasAttribute("tabindex")) {
      element.setAttribute("tabindex", "0");
    }

    element.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        this.click();
      }
    });
  });
}

// Utility Functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Product Image Carousel
function initializeProductCarousel() {
  const carousels = document.querySelectorAll(".product-carousel");

  carousels.forEach((carousel) => {
    const images = carousel.querySelectorAll(".carousel-image");
    const prevBtn = carousel.querySelector(".carousel-prev");
    const nextBtn = carousel.querySelector(".carousel-next");
    const indicators = carousel.querySelectorAll(".carousel-indicator");

    let currentIndex = 0;

    function showImage(index) {
      images.forEach((img, i) => {
        img.classList.toggle("active", i === index);
      });

      indicators.forEach((indicator, i) => {
        indicator.classList.toggle("active", i === index);
      });

      currentIndex = index;
    }

    function nextImage() {
      const nextIndex = (currentIndex + 1) % images.length;
      showImage(nextIndex);
    }

    function prevImage() {
      const prevIndex = (currentIndex - 1 + images.length) % images.length;
      showImage(prevIndex);
    }

    if (nextBtn) nextBtn.addEventListener("click", nextImage);
    if (prevBtn) prevBtn.addEventListener("click", prevImage);

    indicators.forEach((indicator, index) => {
      indicator.addEventListener("click", () => showImage(index));
    });

    // Auto-play carousel
    setInterval(nextImage, 5000);
  });
}

// Lazy Loading for Images
function initializeLazyLoading() {
  const images = document.querySelectorAll("img[data-src]");

  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        observer.unobserve(img);
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));
}

// Search Functionality
function initializeSearch() {
  const searchInput = document.getElementById("search-input");
  const searchResults = document.getElementById("search-results");

  if (!searchInput) return;

  const searchData = [
    {
      title: "斗轮堆取料机",
      url: "products/bucket-wheel-stacker.html",
      category: "产品",
    },
    {
      title: "混匀堆取料机",
      url: "products/blending-stacker.html",
      category: "产品",
    },
    {
      title: "板材矫直机",
      url: "products/plate-straightener.html",
      category: "产品",
    },
    {
      title: "技术改造",
      url: "services/technical-renovation.html",
      category: "服务",
    },
    { title: "华隆简介", url: "about/company.html", category: "关于" },
    { title: "联系我们", url: "about/contact.html", category: "关于" },
  ];

  searchInput.addEventListener(
    "input",
    debounce(function (e) {
      const query = e.target.value.toLowerCase().trim();

      if (query.length < 2) {
        searchResults.innerHTML = "";
        searchResults.classList.add("hidden");
        return;
      }

      const results = searchData.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.category.toLowerCase().includes(query)
      );

      if (results.length > 0) {
        searchResults.innerHTML = results
          .map(
            (item) => `
                <a href="${item.url}" class="block px-4 py-2 hover:bg-light-gray">
                    <div class="font-medium">${item.title}</div>
                    <div class="text-sm text-secondary">${item.category}</div>
                </a>
            `
          )
          .join("");
        searchResults.classList.remove("hidden");
      } else {
        searchResults.innerHTML =
          '<div class="px-4 py-2 text-secondary">未找到相关结果</div>';
        searchResults.classList.remove("hidden");
      }
    }, 300)
  );

  // Close search results when clicking outside
  document.addEventListener("click", function (e) {
    if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
      searchResults.classList.add("hidden");
    }
  });
}

// Sticky Header
function initializeStickyHeader() {
  const header = document.querySelector("header");
  const headerHeight = header.offsetHeight;

  window.addEventListener(
    "scroll",
    throttle(function () {
      if (window.scrollY > headerHeight) {
        header.classList.add("shadow-xl");
      } else {
        header.classList.remove("shadow-xl");
      }
    }, 100)
  );
}

// Back to Top Button
function initializeBackToTop() {
  const backToTopBtn = document.createElement("button");
  backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
  backToTopBtn.className =
    "fixed bottom-8 right-8 bg-accent text-white w-12 h-12 rounded-full shadow-lg hover:bg-orange-600 transition-all duration-300 opacity-0 invisible z-50";
  backToTopBtn.setAttribute("aria-label", "返回顶部");
  document.body.appendChild(backToTopBtn);

  window.addEventListener(
    "scroll",
    throttle(function () {
      if (window.scrollY > 500) {
        backToTopBtn.classList.remove("opacity-0", "invisible");
      } else {
        backToTopBtn.classList.add("opacity-0", "invisible");
      }
    }, 100)
  );

  backToTopBtn.addEventListener("click", function () {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });
}

// Cookie Consent
function initializeCookieConsent() {
  if (localStorage.getItem("cookieConsent")) return;

  const cookieBanner = document.createElement("div");
  cookieBanner.className =
    "fixed bottom-0 left-0 right-0 bg-dark-gray text-white p-4 z-50 transform translate-y-full transition-transform duration-300";
  cookieBanner.innerHTML = `
        <div class="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
            <div class="text-sm">
                <p>我们使用Cookie来改善您的浏览体验。继续使用本网站即表示您同意我们的Cookie政策。</p>
            </div>
            <div class="flex gap-2">
                <button id="accept-cookies" class="bg-accent text-white px-4 py-2 rounded text-sm hover:bg-orange-600 transition-colors">
                    接受
                </button>
                <button id="decline-cookies" class="border border-white text-white px-4 py-2 rounded text-sm hover:bg-white hover:text-dark-gray transition-colors">
                    拒绝
                </button>
            </div>
        </div>
    `;

  document.body.appendChild(cookieBanner);

  // Show banner after a delay
  setTimeout(() => {
    cookieBanner.classList.remove("translate-y-full");
  }, 1000);

  document
    .getElementById("accept-cookies")
    .addEventListener("click", function () {
      localStorage.setItem("cookieConsent", "accepted");
      cookieBanner.remove();
    });

  document
    .getElementById("decline-cookies")
    .addEventListener("click", function () {
      localStorage.setItem("cookieConsent", "declined");
      cookieBanner.remove();
    });
}

// Loading Animation
function showLoadingAnimation() {
  const loader = document.createElement("div");
  loader.id = "page-loader";
  loader.className =
    "fixed inset-0 bg-white z-50 flex items-center justify-center";
  loader.innerHTML = `
        <div class="text-center">
            <div class="loading mb-4"></div>
            <p class="text-primary font-medium">加载中...</p>
        </div>
    `;
  document.body.appendChild(loader);

  window.addEventListener("load", function () {
    setTimeout(() => {
      loader.style.opacity = "0";
      setTimeout(() => loader.remove(), 300);
    }, 500);
  });
}

// Initialize all features when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  initializeNavigation();
  initializeLanguageSwitcher();
  initializeScrollEffects();
  initializeImageGallery();
  initializeForms();
  initializeAccessibility();
  initializeProductCarousel();
  initializeLazyLoading();
  initializeSearch();
  initializeStickyHeader();
  initializeBackToTop();
  initializeCookieConsent();
});

// Show loading animation immediately
if (document.readyState === "loading") {
  showLoadingAnimation();
}

// Export functions for use in other scripts
window.HualongWebsite = {
  toggleMobileSubmenu,
  switchLanguage,
  openLightbox,
  showNotification,
  initializeProductCarousel,
  initializeLazyLoading,
};
