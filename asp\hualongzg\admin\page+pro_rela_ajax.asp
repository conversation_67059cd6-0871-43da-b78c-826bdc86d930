<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<%
action = request.querystring("action")
Select Case action
Case "list"
rels_config_id = request.querystring("rels_config_id")
current_sel = request.querystring("current_sel")
current_selid =  request.querystring("current_selid")
table1 = request.querystring("table1")
table1_key = request.querystring("table1_key")
table2 = request.querystring("table2")
table2_key = request.querystring("table2_key")
if current_sel = 1 then
	sql = "select rels.id," & split(table2_key,",")(1) & " from rels inner join (" & table2 & ") on " & split(table2_key,",")(0) & "=rels.s_id_val where rels.rels_id=" & rels_config_id & " AND rels.p_id_val=" & current_selid & " order by rels.s_sort_id desc,rels.id desc"
elseif current_sel = 2 Then
	sql = "select rels.id," & split(table1_key,",")(1) & " from rels inner join (" & table1 & ") on " & split(table1_key,",")(0) & "=rels.p_id_val where rels.rels_id=" & rels_config_id & " AND rels.s_id_val=" & current_selid & " order by rels.p_sort_id desc,rels.id desc"
end If
set rs=server.createobject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,1
If Not rs.eof then
%>
<select id="list_rel" size="20" style="width:290px">
<%While Not rs.eof%>
<option value="<%=rs(0)%>"><%=rs(1)%></option>
<%
rs.movenext()
wend
%>
</select>
<%
Else
 response.write("没有关联信息，请增加")
End If
rs.close()
Set rs=nothing
Case "del"
	sql="DELETE FROM rels WHERE id = "&request.querystring("current_rel_id")
	set Command1 = Server.CreateObject("ADODB.Command")
	Command1.ActiveConnection = MM_conn_STRING
	Command1.CommandText = sql
	Command1.Execute()
End select
%>