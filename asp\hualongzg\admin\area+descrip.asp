<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp")%>
<%If InStr(session("flag"),"lesson") <=0 Then Response.Redirect("default.asp")%>
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction2 = CStr(Request.ServerVariables("URL")) 'MM_editAction2 = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction2 = MM_editAction2 & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: (Modified for File Upload) construct a sql update statement and execute it

If (CStr(request.form("MM_update2")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<% If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
end if
%>

<%
' *** Update Record: set variables

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "area"
  MM_editColumn = "cate_id"
  MM_recordId = "" + request.form("MM_recordId") + ""
  MM_editRedirectUrl = "pro_descrip.asp"
  MM_fieldsStr  = "content|value"
  MM_columnsStr = "title1|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(request.form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsPP
Dim rsPP_numRows

Set rsPP = Server.CreateObject("ADODB.Recordset")
sql = "select Menu as cate_name,right(MenuID,len(MenuID)-6) as cate_id from Menu where MenuID in (" & str_lesson(session("flag")) & ") and left(MenuID,6) = 'lesson' order by sort_id asc"
rsPP.open sql,MM_conn_STRING,1,1

Dim rsTHIS_PP__MMColParam
If (Request.querystring("cate_id") <> "") Then 
  rsTHIS_PP__MMColParam = Request.querystring("cate_id")
End If
%>
<%
Dim Repeat1__numRows
Dim Repeat1__index

Repeat1__numRows = -1
Repeat1__index = 0
rsPP_numRows = rsPP_numRows + Repeat1__numRows
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">课程区简介</h1>
<script language="JavaScript">
<!--
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
  document.MM_returnValue = (errors == '');
}
//-->
</script>
<form name="form0">
  <select name="menu1" onChange="MM_jumpMenu('parent',this,0)">
  <option value="#" selected>请选择课程区</option>
  <% 
While ((Repeat1__numRows <> 0) AND (NOT rsPP.EOF)) 
%>
	<option value="pro_descrip.asp?cate_id=<%=(rsPP.Fields.Item("cate_id").Value)%>"><%=(rsPP.Fields.Item("cate_name").Value)%></option>
  <% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsPP.MoveNext()
Wend
%>
  </select>
</form>
<%if request.querystring("cate_id") <> "" then'如果选择了系列%>
<%
Dim rsTHIS_PP
Dim rsTHIS_PP_numRows

Set rsTHIS_PP = Server.CreateObject("ADODB.Recordset")
rsTHIS_PP.ActiveConnection = MM_conn_STRING
rsTHIS_PP.Source = "SELECT title1, cate_id, cate_name FROM area WHERE cate_id = " + Replace(rsTHIS_PP__MMColParam, "'", "''") + ""
rsTHIS_PP.CursorType = 0
rsTHIS_PP.CursorLocation = 2
rsTHIS_PP.LockType = 1
rsTHIS_PP.Open()

rsTHIS_PP_numRows = 0
%>
<%=(rsTHIS_PP("cate_name"))%><br>
<form name="form1" method="post" action="<%=MM_editAction%>">
	  <table border="0" cellspacing="0" cellpadding="4" align=center>
		<tr> 
		  <td>课程区简介（120字以内）</td></tr>
		  <tr><td width=526 align=center><textarea class=i500 name="content" cols="100" rows="6"><%=(rsTHIS_PP("title1"))%></textarea>
		  </td>
		</tr>
		<tr> 
		  <td> 
			<input type="submit" name="Submit" value="提交" onClick="MM_validateForm('content','','R');return document.MM_returnValue">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsTHIS_PP("cate_id").Value %>">
	</form>
<%
rsTHIS_PP.Close()
set rsTHIS_PP = nothing
end if'如果选择了系列%>
<!--#include file ="_bottom.asp"-->
<%
rsPP.close()
set rsPP = nothing
%>
