<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">权限管理</h1>
<%
if not isempty(request("selAnnounce")) then
idlist=request("selAnnounce")
if instr(idlist,",")>0 then
dim idarr
idArr=split(idlist)
dim id
for i = 0 to ubound(idarr)
id=clng(idarr(i))
call deleteannounce(id)
next
else
call deleteannounce(clng(idlist))
end if
end if 

sub deleteannounce(id)

dim rs,sql
set conn=server.createobject("ADODB.CONNECTION")
conn.open MM_conn_STRING
set rs=server.createobject("adodb.recordset")
sql="delete from usergroup where group_id="&cstr(id)
conn.execute sql
conn.close()
set conn=nothing
End sub

dim rs
dim sql
set rs=server.createobject("adodb.recordset") 
sql="select * from usergroup order by group_id asc"
rs.open sql,MM_conn_STRING,1,1 
if not rs.eof then%>
<table cellpadding="4" width="600" align="center"><%
%>
<Form name="search" method="POST" action="permissionGroup.asp">
<TR height=25>
<TD align="right" colspan=3><a href="permissionGroup+detail.asp?action=add">
<font color="#FF0000">增加部门或角色（用户组）</a></TD>
</TR>
<TR height=28 bgcolor="<%=back_menubackcolor%>"> 
<TD width="25%" align="center">部门或角色</td>
<TD width="25%" align="center">说明</td>
<TD width="12%" align="center"><input type='submit' value='删除' onClick="GP_popupConfirmMsg('删除部门（角色）将删除该部门所有的管理员，确定吗？');return document.MM_returnValue" title="注意，缺省部门不可删除"></td>
</TR>
<%do while not rs.eof%>
<TR height="28" bgcolor="#ffffff"> 
<TD width="8%" align="center"><a href="permissionGroup+detail.asp?group_id=<%=rs("group_id")%>"><%=rs("name")%></a></td>
<TD width="30%" align="center" class=p12><%=rs("descript")%></td>
<TD width="12%" align="center" class=p12><%if rs("is_default") = false then%><input type='checkbox' name='selAnnounce' value='<%=cstr(rs("group_id"))%>'><%
else
response.write("缺省")
end if%>&nbsp;</td>
</TR>
<% RS.movenext
loop
%></form>
</TABLE>
<%
end if
rs.close()
set rs=nothing
%>

 
<!--#include file ="_bottom.asp"-->