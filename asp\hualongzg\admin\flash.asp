<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: declare variables
MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + Request.Form("MM_recordId") + "'"
  MM_editRedirectUrl = "flash.asp"
  if CStr(Request("MM_recordId")) = "hotnews" then
  MM_fieldsStr  = "fmtilte|value"
  MM_columnsStr = "this_memo|',none,''"
  else
  MM_fieldsStr  = "name|value|fmtitle|value"
  MM_columnsStr = "name|',none,''|title|',none,''"
  end if

 ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(Request.Form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(MM_i) & " = " & MM_formVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

	 If (MM_editRedirectUrl <> "") Then
	'写前台_contact文件
	set rs=server.createobject("adodb.recordset")
	sql="select * from contact order by id asc"
	rs.open sql,MM_conn_STRING,1,1
	contact_body = "<%" & vbCrlf
	rs.MoveFirst()
	While Not rs.eof
		contact_body = contact_body & rs("name") & "=""" & HTMLEncode(rs("content")) & """" & vbcrlf
		rs.movenext()
	wend
	rs.close()

	sql = "SELECT title,name FROM other WHERE left(name,11) = 'intro_title'"
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof then
		While Not rs.eof
		contact_body = contact_body & rs("name") & "=""" & rs("title") & """" & vbcrlf
		rs.movenext()
		Wend
	End if
	rs.close()
	set rs=Nothing

	contact_body = contact_body & vbCrlf & "%" & ">"			'结束封闭
	Call SaveToFile(contact_body,"..\_contact.asp")
	
	 Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsFLASH__MMColParam
rsFLASH__MMColParam = "intro_title"
if (Request("MM_EmptyValue") <> "") then rsFLASH__MMColParam = Request("MM_EmptyValue")
%>
<%
set rsFLASH = Server.CreateObject("ADODB.Recordset")
rsFLASH.ActiveConnection = MM_conn_STRING
rsFLASH.Source = "SELECT name, title FROM other WHERE name = '" + Replace(rsFLASH__MMColParam, "'", "''") + "'"
rsFLASH.CursorType = 0
rsFLASH.CursorLocation = 2
rsFLASH.LockType = 3
rsFLASH.Open()
rsFLASH_numRows = 0
%>
<%
Dim rsFLASHb__MMColParam
rsFLASHb__MMColParam = "intro_title_en"
if (Request("MM_EmptyValue") <> "") then rsFLASHb__MMColParam = Request("MM_EmptyValue")
%>
<%
set rsFLASHb = Server.CreateObject("ADODB.Recordset")
rsFLASHb.ActiveConnection = MM_conn_STRING
rsFLASHb.Source = "SELECT name, title FROM other WHERE name = '" + Replace(rsFLASHb__MMColParam, "'", "''") + "'"
rsFLASHb.CursorType = 0
rsFLASHb.CursorLocation = 2
rsFLASHb.LockType = 3
rsFLASHb.Open()
rsFLASHb_numRows = 0
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">首页标题更新</h1>
	<form method="POST" action="<%=MM_editAction%>" name="form1" onsubmit="return VerifyInput()">
	  <input type="hidden" name="name" value="intro_title">
	  中文首页标题：<input type="text" name="fmtitle" value="<%=(rsFLASH.Fields.Item("title").Value)%>" size="100">
	  <input type="submit" name="submit" value="更新">
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsFLASH.Fields.Item("name").Value %>">
	</form>

<!-- 	<form method="POST" action="<%=MM_editAction_2%>" name="form2" onsubmit="return VerifyInput2()">
	  <input type="hidden" name="name" value="intro_title_en">
	  英文首页标题：<input type="text" name="fmtitle" value="<%=(rsFLASHb.Fields.Item("title").Value)%>" size="100">
	  <input type="submit" name="submit" value="更新">
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsFLASHb.Fields.Item("name").Value %>"><br>
	</form> -->
<script language="JavaScript">
function VerifyInput(){
	if (document.form1.fmtitle.value == "")
	{
	alert("必须输入内容！");
	return false;
	}
}
function VerifyInput2(){
	if (document.form2.fmtitle.value==""){
	  alert("必须输入内容");
	  document.form2.fmtitle.focus();
	  return false;
	}
}
</script>
<!--#include file ="_bottom.asp"-->
<%
rsFLASH.Close()
set rsFLASH =nothing
%>