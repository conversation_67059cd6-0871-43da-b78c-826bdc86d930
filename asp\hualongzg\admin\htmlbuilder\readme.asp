<%

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'※																 ※
'※								使用方法							 ※
'※																 ※
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'
'
'一	把htmlbuilder复制到网站后台（一般是/admin）目录下
'	把icon目录复制到根目录下
'	在根目录建立upload文件夹，并将这个文件夹设定为不可执行
'
'二 对于新后台，什么也不用动。对于旧后台，修改如下：
'	修改后台的 /admin/_config.asp 文件，增加下列行

'上传文件限制
'其他文件	3072 kb
'媒体文件	5120 kb
'其他		200  KB以内

'代码<input type=hidden name=fmcontent><IFRAME ID=htmlbuilder1 SRC='htmlbuilder/htmlbuilder.asp?id=fmcontent&style=red' FRAMEBORDER=0 SCROLLING=no WIDTH=550 HEIGHT=350></IFRAME>

'其中要修改的：
'1 name=fmcontent id=fmcontent 跟表单中的相应内容的项目一样
'2 style=red
'其值如下：
'standard	标准
'light		浅色界面
'blue
'green
'red
'yellow
'3d
'coolblue
'mini		紧凑，只有一行工具栏
'popup		弹出窗口形式

%>