<HTML>
<HEAD>
<TITLE>htmlbuilder - 在线使用帮助</TITLE>
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<style>
BODY { FONT-SIZE: 9pt; COLOR: #333333; FONT-FAMILY: "宋体"}
A {	COLOR: #666666; FONT-FAMILY: 宋体; TEXT-DECORATION: underline }
A:hover { COLOR: #ff0000; FONT-FAMILY: 宋体; TEXT-DECORATION: none }
.plus {	FONT-WEIGHT: bold; COLOR: #003366; FONT-FAMILY: 宋体;font-size:12pt }
HR { FILTER: alpha(opacity=100,finishopacity=0,style=1,startx=0,starty=0,finishx=185,finishy=0); COLOR: #ffffff; HEIGHT: 1px}
td { font-size: 9pt;}
</style>
</HEAD>
<BODY bgcolor="#ffffff" topmargin="3">
<HR SIZE=1>
<a name=top></a>

<span class=plus>在线网页编辑器使用帮助</span>
<div style="background-color:#eeeeee;padding-top:1px;padding-bottom:1px">
<ul>
<li><a href="#function">简述</a>
<li><a href="#skill">使用技巧</a>
<li><a href="#face">界面概述</a>
<li><a href="#menu">工具栏使用说明</a>
<li><a href="#state">视图栏使用说明</a>
<li><a href="#fq">疑难解答</a>
</ul></div>
<HR SIZE=1>

<a name=function></a>
<span class=plus>简述：</span> 
<ul>
在线网页编辑器，能像使用word一样，编辑多媒体页面。提供丰富的按钮，能方便地编辑、修改内容，无需学习专门的网页制作工具<br>
并且，能从word或其他网页中拷贝内容，大大提高效率。
</UL>
<HR SIZE=1>
<a name=skill></a>
<span class=plus> 使用技巧：</span>
<UL>
  <LI><b>从别的网页复制</b><BR>
      如果在网络上看到内容不错可以借鉴，可以用鼠标将相应页面内容全部选择起来后，直接复制或拖动到编辑器的编辑区即可，而且原页面上的图片、文字、样式、动态效果等都能原封不动地转移到编辑区，不用修改或添加任何代码。
  <LI><b>从word里复制内容</b><BR>
很多企业使用word软件办公，对于word里的内容，可以直接复制到编辑区，编辑器能自动优化word的代码。
</UL>
<HR SIZE=1>
<a name=face></a>
<span class=plus>界面概述：</span> 
<P> 从上至下，分为三个部分：</P>
<UL>
  <LI><b><a href="#menu">工具栏</a></b><BR>
      顶部为工具栏，主要放置各种按钮，通过这些按钮进行修改、增加操作。
  <LI><b><a href="#edit">编辑区</a></b><BR>
 中部为编辑区，供输入及编辑内容所用，在设计视图（缺省视图）下，编辑的内容全部都是所见即所得，但对于活动的图像、文字、多媒体电影等在要转换到预览模式方可真实再现。
  <LI><b><a href="#state">视图栏</a></b><BR>
      底部为视图栏，主要放置转换编辑器状态的按钮图标，状态共分为：代码视图、设计视图（默认）、预览视图。
</UL>
<HR SIZE=1>

<a name=menu></a>
<span class=plus>工具栏使用说明：</span> 
<P> 一、编辑器工具栏图标（包括底部视图栏图标）功能：</p>
<ul>
	<li><img border=0 src="../../../icon/button/bold.gif">：设置字体样式为粗体。
	<li><img border=0 src="../../../icon/button/italic.gif">：设置字体样式为斜体。
	<li><img border=0 src="../../../icon/button/underline.gif">：设置字体样式为带下划线。
	<li><img border=0 src="../../../icon/button/strikethrough.gif">：设置字体样式为带中划线。
	<li><img border=0 src="../../../icon/button/superscript.gif">：设置字体样式为上标。
	<li><img border=0 src="../../../icon/button/subscript.gif">：设置字体样式为下标。

	<li><img border=0 src="../../../icon/button/tobig.gif">：设置字体变大。
	<li><img border=0 src="../../../icon/button/tosmall.gif">：设置字体样变小。

	<li><img border=0 src="../../../icon/button/justifyleft.gif">：设置内容向左对齐。
	<li><img border=0 src="../../../icon/button/justifycenter.gif">：设置内容向右对齐。
	<li><img border=0 src="../../../icon/button/justifyright.gif">：设置内容向中对齐。
	<li><img border=0 src="../../../icon/button/justifyfull.gif">：设置内容两端对齐。

	<li><img border=0 src="../../../icon/button/insertorderedlist.gif">：设置内容以编号列表形式排列。
	<li><img border=0 src="../../../icon/button/insertunorderedlist.GIF">：设置内容以列表项形式排列。
	<li><img border=0 src="../../../icon/button/Outdent.GIF">：减少内容的缩进量。
	<li><img border=0 src="../../../icon/button/INDENT.GIF">：增加内容的缩进量。

	<li><img border=0 src="../../../icon/button/forecolor.gif">：设置字体的颜色。
	<li><img border=0 src="../../../icon/button/BackCOLOR.GIF">：设置字体背景颜色。
	<li><img border=0 src="../../../icon/button/BgCOLOR.GIF">：设置对象背景颜色。

	<li><img border=0 src="../../../icon/button/cut.gif">：剪切指定内容。
	<li><img border=0 src="../../../icon/button/copy.gif">：复制指定内容。
	<li><img border=0 src="../../../icon/button/paste.gif">：粘贴剪贴板中的内容。
	<li><img border=0 src="../../../icon/button/pastetext.gif">：以纯文件形式粘贴剪贴板中的内容。
	<li><img border=0 src="../../../icon/button/pasteword.gif">：粘贴从Word中复制的内容，并去除冗余格式。
	<li><img border=0 src="../../../icon/button/DELETE.GIF">：删除指定内容。
	<li><img border=0 src="../../../icon/button/removeformat.gif">：删除指定内容的格式。

	<li><img border=0 src="../../../icon/button/undo.gif">：撒消上次操作。
	<li><img border=0 src="../../../icon/button/redo.gif">：恢复上次操作。

	<li><img border=0 src="../../../icon/button/selectall.gif">：选定所有内容。
	<li><img border=0 src="../../../icon/button/unselect.gif">：取消选定的内容。

	<li><img border=0 src="../../../icon/button/FormMenu.gif">：表单菜单。
	<li><img border=0 src="../../../icon/button/FormText.gif">：插入文本输入框。
	<li><img border=0 src="../../../icon/button/FormTextArea.gif">：插入文字区。
	<li><img border=0 src="../../../icon/button/FormRadio.gif">：插入单选按钮。
	<li><img border=0 src="../../../icon/button/FormCheckbox.gif">：插入复选框。
	<li><img border=0 src="../../../icon/button/FormDropdown.gif">：插入下拉框。
	<li><img border=0 src="../../../icon/button/FormButton.gif">：插入按钮。

	<li><img border=0 src="../../../icon/button/marquee.gif">：插入或修改字幕，即滚动文字。
	<li><img border=0 src="../../../icon/button/InsertHorizontalRule.gif">：插入水平尺。
	<li><img border=0 src="../../../icon/button/br.gif">：插入换行符。
	<li><img border=0 src="../../../icon/button/InsertParagraph.gif">：插入段落。

	<li><img border=0 src="../../../icon/button/createlink.gif">：插入或修改超级链接。
	<li><img border=0 src="../../../icon/button/unlink.gif">：删除超级链接或标签。
	<li><img border=0 src="../../../icon/button/map.gif">：图形热点链接。
	<li><img border=0 src="../../../icon/button/anchor.gif">：标签管理。

	<li><img border=0 src="../../../icon/button/TableMenu.gif">：表格菜单。
	<li><img border=0 src="../../../icon/button/TableInsert.gif">：插入表格...。
	<li><img border=0 src="../../../icon/button/TableProp.gif">：表格属性...。
	<li><img border=0 src="../../../icon/button/TableCellProp.gif">：单元格属性...。
	<li><img border=0 src="../../../icon/button/TableCellSplit.gif">：拆分单元格...。
	<li><img border=0 src="../../../icon/button/TableRowProp.gif">：表格行属性...。
	<li><img border=0 src="../../../icon/button/TableRowInsertAbove.gif">：插入行（在上方）。
	<li><img border=0 src="../../../icon/button/TableRowInsertBelow.gif">：插入行（在下方）。
	<li><img border=0 src="../../../icon/button/TableRowMerge.gif">：合并行（向下方）。
	<li><img border=0 src="../../../icon/button/TableRowSplit.gif">：拆分行。
	<li><img border=0 src="../../../icon/button/TableColInsertLeft.gif">插入列（在左侧）。
	<li><img border=0 src="../../../icon/button/TableColInsertRight.gif">：插入列（在右侧）。
	<li><img border=0 src="../../../icon/button/TableColMerge.gif">：合并列（向右侧）。
	<li><img border=0 src="../../../icon/button/TableColSplit.gif">：拆分列。

	<li><img border=0 src="../../../icon/button/fieldset.gif">：在指定位置插入或修改栏目框。
	<li><img border=0 src="../../../icon/button/iframe.gif">：在指定位置插入或修改网页帧。

	<li><img border=0 src="../../../icon/button/img.gif">：在指定位置插入或修改图片。
	<li><img border=0 src="../../../icon/button/flash.gif">：在指定位置插入Flash动画。
	<li><img border=0 src="../../../icon/button/media.gif">：在指定位置插入自动播放的体文件。
	<li><img border=0 src="../../../icon/button/file.gif">：在指定位置插入其它文件。
	<li><img border=0 src="../../../icon/button/remoteupload.gif">：远程自动文件获取。

	<li><img border=0 src="../../../icon/button/excel.gif">：在指定位置插入EXCEL表格。
	<li><img border=0 src="../../../icon/button/symbol.gif">：在指定位置插入特殊字符。
	<li><img border=0 src="../../../icon/button/bgpic.gif">：指定位置的背景图片管理。
	<li><img border=0 src="../../../icon/button/emot.gif">：在指定位置插入表情图标。

	<li><img border=0 src="../../../icon/button/date.gif">：在指定位置插入当前日期。
	<li><img border=0 src="../../../icon/button/time.gif">：在指定位置插入当前时间。

	<li><img border=0 src="../../../icon/button/code.gif">：转换指定内容为代码样式。
	<li><img border=0 src="../../../icon/button/quote.gif">：转换指定内容为引用样式。

	<li><img border=0 src="../../../icon/button/showborders.gif">：显示或隐藏表格虚框。
	<li><img border=0 src="../../../icon/button/findreplace.gif">：查找替换功能。
	<li><img border=0 src="../../../icon/button/refresh.gif">：新建文档功能。
	<li><img border=0 src="../../../icon/button/abspos.gif">：相对或绝对位置设置功能。
	<li><img border=0 src="../../../icon/button/forward.gif">：上移一层。
	<li><img border=0 src="../../../icon/button/backward.gif">：下移一层。
	<li><img border=0 src="../../../icon/button/zoommenu.gif">：缩放菜单。
	<li><img border=0 src="../../../icon/button/sizeplus.gif">：增高编辑区。
	<li><img border=0 src="../../../icon/button/sizeminus.gif">：减小编辑区。

	<li><img border=0 src="../../../icon/button/modecodebtn.gif">：转为代码视图。
	<li><img border=0 src="../../../icon/button/modeeditbtn.gif">：转为设计视图。
<!-- 	<li><img border=0 src="../../../icon/button/modetextbtn.gif">：转为文本状态。 -->
	<li><img border=0 src="../../../icon/button/modeviewbtn.gif">：转为预览视图。

	<li><img border=0 src="../../../icon/button/print.gif">：打印全页。
	<li><img border=0 src="../../../icon/button/save.gif">：保存内容到相关联的表单。

	<li><img border=0 src="../../../icon/button/maximize.gif">：打开全屏编辑。
	<li><img border=0 src="../../../icon/button/minimize.gif">：关闭全屏编辑并返回。
	<li><img border=0 src="../../../icon/button/save.gif">：弹窗保存并返回。

	<li><img border=0 src="../../../icon/button/help.gif">：查看在线使用帮助。

	<li><img border=0 src="../../../icon/button/toolmenu.gif">：工具菜单。
	<li><img border=0 src="../../../icon/button/filemenu.gif">：文件视图菜单。
	<li><img border=0 src="../../../icon/button/editmenu.gif">：编辑菜单。
	<li><img border=0 src="../../../icon/button/objectmenu.gif">：对象效果菜单。
	<li><img border=0 src="../../../icon/button/ComponentMenu.gif">：组件菜单。

</ul>

<p>二、注意事项：</p>
<ol>
  <li>部分提供修改功能的图标或选项，只需选择原来插入的对象，再按同一图标或选项，即可进行修改。
  <li>部分提供设置功能的图标或选项，都可以通过再按一次图标或选项来取消前一次的设置。
</ol>
<HR SIZE=1>

<a name=state></a>
<span class=plus>视图栏使用说明：</span> 
<P> 共有三种视图可供转换，详细如下：<br>
  <br>
  <img src="../../../icon/button/modeedit.gif" width="50" height="15">：设计视图(默认)，在此视图下所有编辑的内容皆为所见即所得。<br>
  <br>
  <img src="../../../icon/button/modecode.gif" width="50" height="15">：代码视图，在此视图下所有编辑的内容都以HTML标记源代码方式显示或编辑。提供熟悉HTML语法的人使用。<br>
  <br>
  <img src="../../../icon/button/modepreview.gif" width="50" height="15">：预览视图，在此视图下所有内容都以不可编辑的页面输出方式显示，可利用此视图预览编辑内容输出后的效果。</P>
<HR SIZE=1>



<a name=fq></a>
<span class=plus>疑难解答：</span> 
<p>问：在MYIE2等外挂浏览里快捷键无法使用？<br>
答：与外挂浏览器快捷键冲突，修改外挂浏览器的快捷键设置即可。</p>

<p>问：文件大小有什么限制？<br>
答：文件 3M；多媒体文件 5M；图片、FLASH 200K以内</p>
<div style="background-color:#eeeeee;padding-top:3px;padding-bottom:3px;padding-left:3px">
<a href=#top>↑顶部</a></div>
</BODY>
</HTML>
