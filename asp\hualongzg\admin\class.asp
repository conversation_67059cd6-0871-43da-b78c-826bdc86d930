<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%if request.querystring("top") <> "" then
	this_top = request.querystring("top")
end if%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<!--#include file ="../_default.asp"-->
<h1 class="b_ch1">栏目管理</h1>
<%
if not isempty(request("selAnnounce")) then
idlist=request("selAnnounce")
if instr(idlist,",")>0 then
dim idarr
idArr=split(idlist)
dim id
for i = 0 to ubound(idarr)
id=clng(idarr(i))
call deleteannounce(id)
next
else
call deleteannounce(clng(idlist))
end if
end if 

Set rs = Server.CreateObject("ADODB.Recordset")
sql = "SELECT * from class order by id asc"
rs.open sql,MM_conn_STRING,1,1
%>
<div align=center style="padding-bottom:15px">
<select style="width:200px" name="select"onChange="MM_jumpMenu('self',this,0)" >
<option value=class.asp>请选择主栏目</option>
<%while not rs.eof%>
<option value="class.asp?top=<%=(rs("id"))%>"<%if cint(this_top)=rs("id") then response.write(" selected style='color:#ff0000'")%>><%=(rs("name"))%></option>
<% 
  rs.MoveNext()
Wend
rs.Close()
Set rs = Nothing
%></select>
</div>
<%if request.querystring("top") <> "" then%>
<Form name="search" method="POST" action="class.asp?top=<%=request.querystring("top")%>">
<table border="1" cellspacing="0" cellpadding="4" bordercolor="<%=back_menubackcolor0%>" align=center width=600>
<%
Set rs1 = Server.CreateObject("ADODB.Recordset")
sql = "SELECT * from class where id=" & request.querystring("top")
rs1.open sql,MM_conn_STRING,1,1

if rs1("this_edit") = false then
%>
<tr><td colspan=6>本栏目为耦合栏目，子栏目不可编辑</td></tr>
<% else%>
<tr><td colspan=6 align=right><a href=class_detail.asp?top=<%=request.querystring("top")%>&action=add><font color="#FF0000">→增加子栏目</font></a></td></tr>
<%end if
%>
<tr bgcolor=<%=(back_menubackcolor)%>>
	<td>子栏目名称</td>
	<td>栏目类型</td>
	<td>是否单页面栏目</td>
	<td>栏目页面排序</td>
	<td>主题显示日期</td>
<%if rs1("this_edit") = true then%><td><input type='submit' value='删除' onClick="GP_popupConfirmMsg('删除子栏目将删除该栏目所有页面，确定吗？');return document.MM_returnValue"></td><%end if%>
</tr>
<%
Set rs = Server.CreateObject("ADODB.Recordset")
sql = "SELECT subclass.*,subclass_style.style_name, subclass_style.style_descript from (subclass inner join subclass_style on subclass_style.style_id = subclass.style_id) where id=" & this_top & " order by sort_id, subid asc"
rs.open sql,MM_conn_STRING,1,1
while not rs.eof
%>
<tr align=center>
	<td><a href=class_detail.asp?subid=<%=rs("subid")%>><%=rs("name")%></a></td>
	<td><%=rs("style_name")%></td>
	<td><%if rs("onlyone") = true then
	response.write("是")
	else 
	response.write("否")	
	end if%></td>
	<td><%
	if rs("date_sort") = 0 then
	response.write("<a tilte='最新页面在最上面显示'>最新的先显示</a>")
	elseif rs("date_sort") = 1 then
	response.write("<a>最新的后显示</a>")
	end if
	%></td>
	<td><%if rs("date_display_on") = true then
	response.write("是")
	else 
	response.write("否")	
	end if%></td>
<%if rs1("this_edit") = true then%><td><input type='checkbox' name='selAnnounce' value='<%=cstr(rs("subid"))%>'<%If rs("not_del") = True Then response.write(" disabled title=""本子栏目被首页引用，不可删除""")%>></td><%end if%>
</tr>
<%
rs.MoveNext()
Wend
rs.Close()
Set rs = Nothing

rs1.close()
set rs1=nothing

sub deleteannounce(id)
If id=r_t_1 Or id =r_t_2 Or id=r_t_3 Or id=r_t_4 Then
	alert_str = "删除的子栏目里有栏目为学校首页或附医首页栏目，请在""首页管理>首页栏目""里修改后再删除"
	Response.Write "<script language=javascript>alert('" & alert_str & "');;history.back();</script>"	
	Exit sub
End if
set conn=server.createobject("ADODB.CONNECTION")
conn.open MM_conn_STRING
dim rs,sql
set rs=server.createobject("adodb.recordset")
sql="delete from subclass where subid="&cstr(id)
conn.execute sql
set conn=nothing
End sub

%>
</table></form>
<br>

<%end if%>
<!--#include file ="_bottom.asp"-->
