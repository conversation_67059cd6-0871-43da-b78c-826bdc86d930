<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">首页左侧新闻（中文）</h1>
<style type="text/css">
/* <![CDATA[ */
ul.newpro{list-style-type:none;}
ul.newpro li{display:block;position:relative}
ul.newpro li a{color:#333;padding:0 0 0 3px}
ul.newpro li a:hover{background-color:#dedede}
ul.newpro li div.sortmenu{position:absolute;left:28px;top:18px;display:none;background-color:#eee}
ul.newpro li div.sortmenu a{display:block;width:50px;border:1px solid #666}
ul.newpro li div.sortmenu a:hover{color:#f00}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
Event.onDOMReady(newprostart);

function newprostart(){
	gotoPage();newpro();
}

function shownew(obj){
	if(obj.value=="只显示首页左侧新闻"){
		$A($$("ul.newpro li")).each(function(node){
			if (!node.down('input').checked){
				node.style.display = "none";
			}
		});		
		obj.value ="显示所有新闻"
	}else{
		$A($$("ul.newpro li")).each(function(node){
			node.style.display = "block";	
		});		
		obj.value="只显示首页左侧新闻"
	}
}

function newpro(){
	nCol = $$("ul.newpro")[0].getElementsByTagName("li");
	for (i=nCol.length-1;i>=0;i--){
		nCol[i].style.zIndex = nCol.length -i+2;
	}
}
function gotoPage(){
	var url = 'newpage_ajax.asp?action=ok';
	var myAjax = new Ajax.Updater(document.getElementsByClassName("newpro")[0], url, {method: 'get',asynchronous:false});
}

//]]>
</script><%
If request.Form("action") = "ok" Then
	sql_command("update page set top_submit = true where page.pageid in(" & request.Form("id")) & ")"
	sql_command("update page set top_submit = false where page.pageid not in(" & request.Form("id")) & ")"
	response.redirect("newpage.asp")
End If

%>
<form method="post" action="newpage.asp">
<ul class="newpro"></ul>

<p style="display:block;clear:left;padding-top:10px"><input type="hidden" name="action" value="ok" />
<input type="submit" value="提交" style="width:100px;height:22px;border:1px #666 solid" />
<input type="submit" id="onlysnew" onclick="shownew(this);return false;" value="只显示首页左侧新闻" style="width:150px;height:22px;border:1px #666 solid;margin-left:5px" />
</p>
</form>

<!--#include file ="_bottom.asp"-->