<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="event__config.asp"-->
<!--#include file ="_head.asp"-->
<%
Dim t_action
t_action =request.querystring("action")

Select Case t_action
Case "sort"
	Call form_sort(request.querystring("id"),request.querystring("sort_type"),"event_type")
Case "del"
If request.querystring("id")<> "" Then
'要判断是不能删除的记录
	set rs_t=server.createobject("adodb.recordset")
	sql = "select not_del from event_type where event_type_id=" & request.querystring("id")
	rs_t.open sql,MM_conn_STRING,1,1
	If rs_t("not_del") = False Then sql_command("delete from event_type where event_type_id=" & request.querystring("id"))
	response.redirect("jobs_event_config.asp")
End If
Case "add"
	set rs=server.createobject("adodb.recordset")
	sql="select * from event_type where category_id=" & request.querystring("cid")
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	rs("category_id") = request.querystring("cid")
	rs("name") = HTMLEncode(request.form("name"))
	If request.form("email_on") <> "" then
		rs("email_on") = True
	Else
		rs("email_on") = false
	End if
	If request.form("email_content") <> "" then rs("email_content") = HTMLEncode(request.form("email_content"))
	If request.form("email_subject") <> "" Then rs("email_subject") = HTMLEncode(request.form("email_subject"))
	rs.update
	rs("sort_id") = rs.bookmark
	rs.update
	rs.close()
	set rs=Nothing
	response.redirect("jobs_event_config.asp")
Case "mod"
	set rs=server.createobject("adodb.recordset")
	sql="select * from event_type where event_type_id=" & request.querystring("id")
	rs.open sql,MM_conn_STRING,3,3
	If rs("not_del") = False then rs("name") = HTMLEncode(request.form("name"))
	If request.form("email_on") <> "" then
		rs("email_on") = True
	Else
		rs("email_on") = false
	End if
	If request.form("email_content") <> "" then rs("email_content") = HTMLEncode(request.form("email_content"))
	If request.form("email_subject") <> "" Then rs("email_subject") = HTMLEncode(request.form("email_subject"))
	rs.update
	rs.close()
	set rs=Nothing
	response.redirect("jobs_event_config.asp")
End Select

%>
<script language="JavaScript">
<!--
var http_request = false;
function showrequest(url,element,asyn) {
	var isFFCLS = true;
	http_request = false;
    if (window.XMLHttpRequest) {
        http_request = new XMLHttpRequest();
        if (http_request.overrideMimeType) {
            http_request.overrideMimeType('text/xml');
        }
    } else if (window.ActiveXObject) {
        try {
            http_request = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            try {
            http_request = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {}
        }
    }
    if (!http_request) {alert('出错 :( 不能建立XMLHTTP实例');return false;}
    http_request.onreadystatechange = sub_showrequest;
    http_request.open('GET', url, asyn);
    http_request.send(null);
	if ((!asyn) && (isFFCLS)) sub_showrequest();
	function sub_showrequest(){
	    if (http_request.readyState == 4) {
        if (http_request.status == 200) {
			isFFCLS = false;
			document.getElementById(element).innerHTML = http_request.responseText;
        } else {alert('请求时出错');}
		}	
	}
}

function validateadd(cid){
	var n0=document.add.elements[0];
	var n1=document.add.elements[1];
	if (cid==1){
		if (n0.value=="")	{alert("请输入流程名称");n0.focus();return false;}
		if (n1.checked){
			var email_subject = document.add.email_subject; var email_content = document.add.email_content;
			if (email_subject.value==""){alert("请输入EMAIL主题");email_subject.focus();return false;}
			if (email_content.value==""){alert("请输入EMAIL模板内容\n\n不要输入称呼和签名，因为是自动生成的");email_content.focus();return false;}
		}
	}
}

function mod(id){
showrequest('jobs_event_config_ajax.asp?action=mod&id=' + id,'ac',false);
var p = document.getElementById("event" + id),q=document.getElementById("ac");
q.style.top = (p.offsetTop + q.offsetHeight <=document.body.scrollHeight)?(p.offsetTop + 165):(document.body.scrollHeight-q.offsetHeight-100);
}

function addc(cid){
showrequest('jobs_event_config_ajax.asp?action=add&cid=' + cid,'ac',false);
var p = document.getElementById("eventypeadd"),q=document.getElementById("ac");
q.style.top = (p.offsetTop + 190 + q.offsetHeight <=document.body.scrollHeight)?(p.offsetTop + 165):(document.body.scrollHeight-q.offsetHeight-100);
}

function emailcheck(){
var emailon = document.add.email_on.checked;
if(emailon){
document.getElementById("email0").style.display = "block";
document.getElementById("email1").style.display = "block";
document.getElementById("email2").style.display = "block";
	}
else{
document.getElementById("email0").style.display = "none";
document.getElementById("email1").style.display = "none";
document.getElementById("email2").style.display = "none";

	}
}

//-->
</script>
<h1 class="b_ch1">招聘流程设置</h1>
<table width="300">
<thead>
<tr>
	<td>流程名称</td><td>排序</td><td>操作</td>
</tr>
</thead>
<tbody>
<tr id="eventypeadd"><td colspan=3 align=right><a href='javascript:void(null)' onclick='addc(1)'><span class=hbottom>增加流程</span></a></td></tr>
<%
Set rs = Server.CreateObject("ADODB.Recordset")
sql = "SELECT event_type_id,name,not_del from event_type where category_id=1 ORDER BY sort_id asc,event_type_id asc"
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
	type_id = rs("event_type_id")
	If rs("not_del") = False then
	action_str="<a href='jobs_event_config.asp?action=del&id=" & type_id & "' onclick=""return confirm('真的这个流程吗？')""><span class=hbottom>删除</span></a> <a href='javascript:void(null)' onclick='mod(" & type_id & ")'><span class=hbottom>修改</span></a>"
	Else
	action_str="<a href='javascript:void(null)' onclick='mod(" & type_id & ")'><span class=hbottom>修改</span></a>"
	End If
	sort_str = "<a href='jobs_event_config.asp?action=sort&sort_type=up&id=" & type_id & "'><span class=hbottom>↑</span></a><a href='jobs_event_config.asp?action=sort&sort_type=down&id=" & type_id & "'><span class=hbottom>↓</span></a>"
	
	response.write ("<tr id=event" & rs("event_type_id") & "><td>" & rs("name") & "</td><td>" & sort_str & "</td><td align=right nowrap>" & action_str & "</td></tr>")
	rs.movenext()
Wend
rs.close()
Set rs=Nothing


%>
<tr>
</tbody>
</table>
<div id="ac" style="position:absolute;top:190px;left:500px;z-index:2"></div>
<!--#include file ="_bottom.asp"-->