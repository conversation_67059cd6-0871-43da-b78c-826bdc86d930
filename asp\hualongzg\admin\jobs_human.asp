<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="event__config.asp"-->
<!--#include file ="_head.asp"-->
<%
  Response.Buffer   =   True    
  Response.ExpiresAbsolute   =  Now() - 1    
  Response.Expires   =   0    
  Response.CacheControl   =   "no-cache"    
  Response.AddHeader   "Pragma",   "No-Cache" 

If request.form("action") = "del" Then
	Call sql_command("delete from jobs_human where h_id=" & request.Form("id"))
	If request.Form("offset") > 0 then
		response.redirect("jobs_human.asp?offset=" & request.Form("offset"))
	Else
		response.redirect("jobs_human.asp")
	End if
End If

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function									

dim rsRESUMES
set rsRESUMES=server.createobject("adodb.recordset")
sql="select h_id,job_id,name,surname,f_name,jobs_human.sex,birth,jobs_human.xueli,school,zhuangye,biye_date,attachment,attachment_descript,links,email,mobile,telphone,add_date,this_type,score from jobs_human,jobs where job_id = jobs.id and jobs_human.this_type <> 1 and jobs_human.this_type <> 2 and jobs_human.this_type <> 3 and jobs_human.this_type <> 11"
if request.querystring("position") <> "" then sql = sql & " AND job_id = " & request.querystring("position")
sql = sql & " order by h_id desc"
'response.write(sql)
'response.end
rsRESUMES.open sql,MM_conn_STRING,1,1
%>
<%
Dim Repeat3__numRows
Dim Repeat3__index

Repeat3__numRows = jobs_list_num			'10条换页

Repeat3__this = Repeat3__numRows
Repeat3__index = 0
rsRESUMES_numRows = rsRESUMES_numRows + Repeat3__numRows
%>
<%
'  *** Recordset Stats, Move To Record, and Go To Record: declare stats variables

Dim rsRESUMES_total
Dim rsRESUMES_first
Dim rsRESUMES_last

' set the record count
rsRESUMES_total = rsRESUMES.RecordCount

' set the number of rows displayed on this page
If (rsRESUMES_numRows < 0) Then
  rsRESUMES_numRows = rsRESUMES_total
Elseif (rsRESUMES_numRows = 0) Then
  rsRESUMES_numRows = 1
End If

' set the first and last displayed record
rsRESUMES_first = 1
rsRESUMES_last  = rsRESUMES_first + rsRESUMES_numRows - 1

' if we have the correct record count, check the other stats
If (rsRESUMES_total <> -1) Then
  If (rsRESUMES_first > rsRESUMES_total) Then
    rsRESUMES_first = rsRESUMES_total
  End If
  If (rsRESUMES_last > rsRESUMES_total) Then
    rsRESUMES_last = rsRESUMES_total
  End If
  If (rsRESUMES_numRows > rsRESUMES_total) Then
    rsRESUMES_numRows = rsRESUMES_total
  End If
End If
%>
<%
' *** Recordset Stats: if we don't know the record count, manually count them

If (rsRESUMES_total = -1) Then

  ' count the total records by iterating through the recordset
  rsRESUMES_total=0
  While (Not rsRESUMES.EOF)
    rsRESUMES_total = rsRESUMES_total + 1
    rsRESUMES.MoveNext
  Wend

  ' reset the cursor to the beginning
  If (rsRESUMES.CursorType > 0) Then
    rsRESUMES.MoveFirst
  Else
    rsRESUMES.Requery
  End If

  ' set the number of rows displayed on this page
  If (rsRESUMES_numRows < 0 Or rsRESUMES_numRows > rsRESUMES_total) Then
    rsRESUMES_numRows = rsRESUMES_total
  End If

  ' set the first and last displayed record
  rsRESUMES_first = 1
  rsRESUMES_last = rsRESUMES_first + rsRESUMES_numRows - 1
  
  If (rsRESUMES_first > rsRESUMES_total) Then
    rsRESUMES_first = rsRESUMES_total
  End If
  If (rsRESUMES_last > rsRESUMES_total) Then
    rsRESUMES_last = rsRESUMES_total
  End If

End If
%>
<%
Dim MM_paramName 
%>
<%
' *** Move To Record and Go To Record: declare variables

Dim MM_rs
Dim MM_rsCount
Dim MM_size
Dim MM_uniqueCol
Dim MM_offset
Dim MM_atTotal
Dim MM_paramIsDefined

Dim MM_param
Dim MM_index

Set MM_rs    = rsRESUMES
MM_rsCount   = rsRESUMES_total
MM_size      = rsRESUMES_numRows
MM_uniqueCol = ""
MM_paramName = ""
MM_offset = 0
MM_atTotal = false
MM_paramIsDefined = false
If (MM_paramName <> "") Then
  MM_paramIsDefined = (Request.QueryString(MM_paramName) <> "")
End If
%>
<%
' *** Move To Record: handle 'index' or 'offset' parameter

if (Not MM_paramIsDefined And MM_rsCount <> 0) then

  ' use index parameter if defined, otherwise use offset parameter
  MM_param = Request.QueryString("index")
  If (MM_param = "") Then
    MM_param = Request.QueryString("offset")
  End If
  If (MM_param <> "") Then
    MM_offset = Int(MM_param)
  End If

  ' if we have a record count, check if we are past the end of the recordset
  If (MM_rsCount <> -1) Then
    If (MM_offset >= MM_rsCount Or MM_offset = -1) Then  ' past end or move last
      If ((MM_rsCount Mod MM_size) > 0) Then         ' last page not a full repeat region
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' move the cursor to the selected record
  MM_index = 0
  While ((Not MM_rs.EOF) And (MM_index < MM_offset Or MM_offset = -1))
    MM_rs.MoveNext
    MM_index = MM_index + 1
  Wend
  If (MM_rs.EOF) Then 
    MM_offset = MM_index  ' set MM_offset to the last possible record
  End If

End If
%>
<%
' *** Move To Record: if we dont know the record count, check the display range

If (MM_rsCount = -1) Then

  ' walk to the end of the display range for this page
  MM_index = MM_offset
  While (Not MM_rs.EOF And (MM_size < 0 Or MM_index < MM_offset + MM_size))
    MM_rs.MoveNext
    MM_index = MM_index + 1
  Wend

  ' if we walked off the end of the recordset, set MM_rsCount and MM_size
  If (MM_rs.EOF) Then
    MM_rsCount = MM_index
    If (MM_size < 0 Or MM_size > MM_rsCount) Then
      MM_size = MM_rsCount
    End If
  End If

  ' if we walked off the end, set the offset based on page size
  If (MM_rs.EOF And Not MM_paramIsDefined) Then
    If (MM_offset > MM_rsCount - MM_size Or MM_offset = -1) Then
      If ((MM_rsCount Mod MM_size) > 0) Then
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' reset the cursor to the beginning
  If (MM_rs.CursorType > 0) Then
    MM_rs.MoveFirst
  Else
    MM_rs.Requery
  End If

  ' move the cursor to the selected record
  MM_index = 0
  While (Not MM_rs.EOF And MM_index < MM_offset)
    MM_rs.MoveNext
    MM_index = MM_index + 1
  Wend
End If
%>
<%
' *** Move To Record: update recordset stats

' set the first and last displayed record
rsRESUMES_first = MM_offset + 1
rsRESUMES_last  = MM_offset + MM_size

If (MM_rsCount <> -1) Then
  If (rsRESUMES_first > MM_rsCount) Then
    rsRESUMES_first = MM_rsCount
  End If
  If (rsRESUMES_last > MM_rsCount) Then
    rsRESUMES_last = MM_rsCount
  End If
End If

' set the boolean used by hide region to check if we are on the last record
MM_atTotal = (MM_rsCount <> -1 And MM_offset + MM_size >= MM_rsCount)
%>
<%
' *** Go To Record and Move To Record: create strings for maintaining URL and Form parameters

Dim MM_keepNone
Dim MM_keepURL
Dim MM_keepForm
Dim MM_keepBoth

Dim MM_removeList
Dim MM_item
Dim MM_nextItem

' create the list of parameters which should not be maintained
MM_removeList = "&index="
If (MM_paramName <> "") Then
  MM_removeList = MM_removeList & "&" & MM_paramName & "="
End If

MM_keepURL=""
MM_keepForm=""
MM_keepBoth=""
MM_keepNone=""

' add the URL parameters to the MM_keepURL string
For Each MM_item In Request.QueryString
  MM_nextItem = "&" & MM_item & "="
  If (InStr(1,MM_removeList,MM_nextItem,1) = 0) Then
    MM_keepURL = MM_keepURL & MM_nextItem & Server.URLencode(Request.QueryString(MM_item))
  End If
Next

' add the Form variables to the MM_keepForm string
For Each MM_item In Request.Form
  MM_nextItem = "&" & MM_item & "="
  If (InStr(1,MM_removeList,MM_nextItem,1) = 0) Then
    MM_keepForm = MM_keepForm & MM_nextItem & Server.URLencode(Request.Form(MM_item))
  End If
Next

' create the Form + URL string and remove the intial '&' from each of the strings
MM_keepBoth = MM_keepURL & MM_keepForm
If (MM_keepBoth <> "") Then 
  MM_keepBoth = Right(MM_keepBoth, Len(MM_keepBoth) - 1)
End If
If (MM_keepURL <> "")  Then
  MM_keepURL  = Right(MM_keepURL, Len(MM_keepURL) - 1)
End If
If (MM_keepForm <> "") Then
  MM_keepForm = Right(MM_keepForm, Len(MM_keepForm) - 1)
End If

' a utility function used for adding additional parameters to these strings
Function MM_joinChar(firstItem)
  If (firstItem <> "") Then
    MM_joinChar = "&"
  Else
    MM_joinChar = ""
  End If
End Function
%>
<%
' *** Move To Record: set the strings for the first, last, next, and previous links

Dim MM_keepMove
Dim MM_moveParam
Dim MM_moveFirst
Dim MM_moveLast
Dim MM_moveNext
Dim MM_movePrev

Dim MM_urlStr
Dim MM_paramList
Dim MM_paramIndex
Dim MM_nextParam

MM_keepMove = MM_keepBoth
MM_moveParam = "index"

' if the page has a repeated region, remove 'offset' from the maintained parameters
If (MM_size > 1) Then
  MM_moveParam = "offset"
  If (MM_keepMove <> "") Then
    MM_paramList = Split(MM_keepMove, "&")
    MM_keepMove = ""
    For MM_paramIndex = 0 To UBound(MM_paramList)
      MM_nextParam = Left(MM_paramList(MM_paramIndex), InStr(MM_paramList(MM_paramIndex),"=") - 1)
      If (StrComp(MM_nextParam,MM_moveParam,1) <> 0) Then
        MM_keepMove = MM_keepMove & "&" & MM_paramList(MM_paramIndex)
      End If
    Next
    If (MM_keepMove <> "") Then
      MM_keepMove = Right(MM_keepMove, Len(MM_keepMove) - 1)
    End If
  End If
End If

' set the strings for the move to links
If (MM_keepMove <> "") Then 
  MM_keepMove = MM_keepMove & "&"
End If

MM_urlStr = Request.ServerVariables("URL") & "?" & MM_keepMove & MM_moveParam & "="

MM_moveFirst = MM_urlStr & "0"
MM_moveLast  = MM_urlStr & "-1"
MM_moveNext  = MM_urlStr & CStr(MM_offset + MM_size)
If (MM_offset - MM_size < 0) Then
  MM_movePrev = MM_urlStr & "0"
Else
  MM_movePrev = MM_urlStr & CStr(MM_offset - MM_size)
End If
%>
<script language="JavaScript">
<!--
function hu_del(hid,offset){
if (confirm("真的删除这条信息吗，如果不是垃圾信息，一般不要删除。")){
	document.frm1.action.value = "del";
	document.frm1.id.value = hid;
	document.frm1.offset.value = offset;
	document.frm1.submit();
	window.location.reload();
	}
}
//-->
</script>
<h1 class="b_ch1">简历列表</h1>
<p class="b_tip">单击姓名查看简历详细信息并在线处理该应聘者招聘工作</p>
<form name="frm1" action="jobs_human.asp?action=del" method="post"><input name="id" type="hidden"><input name="offset" type="hidden"><input type="hidden" name="action"></form>
<table width="100%">
<thead>
<tr><td colspan="11" style="text-align:left">
<select name="position" onchange="MM_jumpMenu('self',this,0)"><option value="jobs_human.asp">所有职位</option>
<%dim rs
set rs=server.createobject("adodb.recordset")
sql1="select id,name from jobs order by dept_id asc,id asc"
rs.open sql1,MM_conn_STRING,1,1
while not rs.eof
%>
<option value="jobs_human.asp?position=<%=rs("id")%>"<%if request("position") = cstr(rs("id")) then response.write("selected style='color:#ff0000'")%>><%=rs("name")%></option>
<%rs.movenext()
wend%>
</select>
<%
rs.close()
set rs=nothing
%></td></tr>
<tr>
	<td>姓名</td>
	<td>性别</td>
	<td>应聘职位</td>
	<td>生日</td>
	<td>毕业学校</td>
	<td>专业</td>
	<td>电话</td>
	<td>手机</td>
	<td>处理</td>
	<td>加入时间</td>
	<td>&nbsp;</td>
</tr>
</thead>
<tbody>
<% this_rsRESUMES_i = 0
while (not rsRESUMES.eof) AND (this_rsRESUMES_i < jobs_list_num)
if rsRESUMES("sex") = "female" then
jobs_human_sex_1 = "女"
else
jobs_human_sex_1 = "男"
end If
If rsRESUMES("this_type") = 0 Then
	this_type = "未处理"
ElseIf  rsRESUMES("this_type") = 11 Then
	this_type = "已退信"
else
	this_type = "处理中"
End if


If request.querystring("offset") = "" Or request.querystring("offset") = 0 Then
	offset = 0
	Else
	offset = request.querystring("offset")
End if
%>
<tr>
	<td><a href="jobs_human_detail.asp?id=<%=rsRESUMES("h_id")%>"><%=rsRESUMES("surname")%><%=rsRESUMES("f_name")%></a></td>
	<td><%=jobs_human_sex_1%></td>
	<td nowrap><%=rsRESUMES("name")%></td>
	<td><%=rsRESUMES("birth")%></td>
	<td><%=rsRESUMES("school")%></td>
	<td><%=rsRESUMES("zhuangye")%></td>
	<td><%=rsRESUMES("telphone")%>&nbsp;</td>
	<td><%=rsRESUMES("mobile")%>&nbsp;</td>
	<td><%=this_type%></td>
	<td><%=rsRESUMES("add_date")%></td>
	<td><a href="javascript:void(null)" onclick="hu_del(<%=rsRESUMES("h_id")%>,<%=offset%>)" class="hbottom">删除</a></td>
</tr>
<%
this_rsRESUMES_i = this_rsRESUMES_i +1
rsRESUMES.movenext()
wend%>
<tr><td style="padding-right:5px;padding-top:8px;padding-bottom:8px;text-align:right" colspan="11">共 <%=rsRESUMES_total%> 位
<%
if rsRESUMES_total > Repeat3__this then'如果超过分页数%>
　　页码：
<%
For i = 1 to rsRESUMES_total Step MM_size
TM_counter = TM_counter + 1
TM_PageEndCount = i + MM_size - 1
if TM_PageEndCount > rsRESUMES_total Then TM_PageEndCount = rsRESUMES_total
if i <> MM_offset + 1 then
Response.Write("<a href=" & Request.ServerVariables("URL") & "?" & MM_keepMove & "offset=" & i-1 & ">")
Response.Write(TM_counter & "</a>")
else
Response.Write("<font color=" & back_titlebackcolor & "><b>" & TM_counter & "</b></font>")
End if
if(TM_PageEndCount <> rsRESUMES_total) then Response.Write(" | ")
next%>
<%end if%></td></tr>
<%rsRESUMES.close()
set rsRESUMES=nothing
%></tbody></table>
<!--#include file ="_bottom.asp"-->