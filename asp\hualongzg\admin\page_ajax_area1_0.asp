&nbsp;<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
htmlbuilderstyle=request.querystring("style")
id = Request.QueryString("id")

actionok = request.querystring("actionok")
If actionok = "mod" Then
		set rsPRO = Server.CreateObject("ADODB.Recordset")
		sql = "SELECT name,content,content2 FROM list WHERE id = " & id
		rsPRO.open sql,MM_conn_String,3,2
		rsPRO("content") = VBsUnEscape(request.querystring("content"))
		rsPRO("content2") = VBsUnEscape(request.Form)
		rsPRO.update
		rsPRO.close()
		Set rsPRO =Nothing	
else
	Set rslist = Server.CreateObject("ADODB.Recordset")
	sql = "select list.*,area.cate_type_id from list inner join area on list.cate_id=area.cate_id where list.id=" & id
	rslist.open sql,MM_conn_STRING,1,1
	If Trim(rslist("thumb")) <> "" Then
		acstring = "修改"
	Else
		acstring = "修改"
	End if
	%>
			<form method="post" class="b_confrm" name="prodetail" id="prodetail">
	<p class="thumb" id="img_list<%=id%>"><label>特征图片</label>
	<a href="../upload/<%=rslist("img")%>" class="img" target="_blank"><img src="../upload/<%=rslist("thumb")%>" alt="点击看大图" /></a>
	<br /><a href="javascript:void(null)" onclick="modlistimg('mod,img_list<%=id%>,list,<%=id%>,thumb,<%=thumbwidth%>,<%=thumbwidth%>,img,<%=img_bigwidth%>,<%=img_bigheight%>')" style="text-align:center"><%=acstring%></a>
	</p>
	<p><label>产品概述</label><textarea id="p_content" name="p_content"  cols="80" rows="10"><%=rslist("content")%></textarea></p>
	<p<%If pro_hidecontent = true then response.write(" style=""display:none""")%>><label>产品说明</label><textarea id="p_content2" name="p_content2" rows="15" cols="80" style="display:none"><%=rslist("content2")%></textarea><iframe ID="htmlbuilder1" src="htmlbuilder/htmlbuilder.asp?id=p_content2&style=<%=htmlbuilderstyle%>" frameborder="0" scrolling="no" width="550" height="350"></iframe></p>
	<p><input type="submit" value="提交" class="isubmit" onclick="return Prook();" /><input type="hidden" name="actionok" value="mod" /><input type="hidden" id="id" name="id" value="<%=id%>" /></p>
			</form><%
	rslist.close()
	Set rslist = Nothing
End if
	%>