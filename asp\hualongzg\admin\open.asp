<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("/admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction2 = CStr(Request.ServerVariables("URL")) 'MM_editAction2 = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction2 = MM_editAction2 & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: (Modified for File Upload) set variables

If (CStr(request("MM_update2")) <> "" And CStr(request("MM_recordId")) <> "") Then'更新图片

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + request("MM_recordId") + "'"
  MM_editRedirectUrl = "open.asp"
  MM_fieldsStr  = "fmimg|value"
  MM_columnsStr = "img|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(request(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & replace(UploadQueryString,"&&GP_upload=true","")
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & replace(UploadQueryString,"&&GP_upload=true","")
    End If
  End If

End If
%>
<%
if request("oldimg") <> "" then'删除旧图
     Set File = CreateObject("Scripting.FileSystemObject")
      ImagePath = Server.MapPath("\img\")
      ImagePath = ImagePath & "\" & request("oldimg")
	  if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
end if'删除旧图
%>
<%
' *** Update Record: (Modified for File Upload) construct a sql update statement and execute it

If (CStr(request("MM_update2")) <> "" And CStr(request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<% If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
end if
%>

<%
' *** Update Record: set variables

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + request.form("MM_recordId") + "'"
  MM_editRedirectUrl = "open.asp"
  MM_fieldsStr  = "content|value"
  MM_columnsStr = "content|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(request.form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%'发送email到管理员
If CStr(request.form("Submit")) <>"" then
set rsREALTIME = Server.CreateObject("ADODB.Recordset")
rsREALTIME.ActiveConnection = MM_conn_STRING
rsREALTIME.Source = "SELECT item_c, zhi  FROM realtime  WHERE item_c = 'page_mod'"
rsREALTIME.CursorType = 0
rsREALTIME.CursorLocation = 2
rsREALTIME.LockType = 3
rsREALTIME.Open()
rsREALTIME_numRows = 0
	if rsREALTIME.Fields.Item("zhi").Value = true then '如果要发送
%>
<%'得到管理员信箱
set rsADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsADMINMAIL.ActiveConnection = MM_conn_STRING
rsADMINMAIL.Source = "SELECT email  FROM admin"
rsADMINMAIL.CursorType = 0
rsADMINMAIL.CursorLocation = 2
rsADMINMAIL.LockType = 3
rsADMINMAIL.Open()
rsADMINMAIL_numRows = 0
%>
<%'使用CDONTS发送email
real_subject= "网站管理员在栏目“" & request.querystring("subname") & "”修改页面"
real_content="页面主题：<font color=ff0000>" & request.form("fmtitle") & "</font>"
if request.form("content") <> "" then real_content = real_content & "<br><br>" & "以下是该页面的内容：" & "<br><hr>"  & Replace(HTMLEncode(request.form("content")),vbCrLF,"<br>")
real_mailfrom=rsADMINMAIL.Fields.Item("email").Value
real_email=rsADMINMAIL.Fields.Item("email").Value
real_body= real_content
if request.form("img") <> "" then
real_body = real_body & "<br><hr><img src=http://www.cnflyer.com/img/" & request.form("img") & ">"
	if request.form("alt") <> "" then real_body =real_body & "<br><br>" & "图片说明：" & request.form("alt")
end if
Response.Write(SendEmail(real_mailfrom,real_email,real_subject,real_body,2,true))
'response.write real_subject & "<br>" & real_content & "<br>" & real_mailfrom & "<br>" & real_email & "<br>" & real_body '测试
'response.end '测试
%>
<%'关闭rs
rsADMINMAIL.Close()
	end if'如果要发送
rsREALTIME.Close()
end if'发送email到管理员
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then%>
<!--#include file="open_w.asp"  -->
<%      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsMODPAGE4__MMColParam
rsMODPAGE4__MMColParam = "openwin"
%>
<%
set rsMODPAGE4 = Server.CreateObject("ADODB.Recordset")
rsMODPAGE4.ActiveConnection = MM_conn_STRING
rsMODPAGE4.Source = "SELECT * FROM other WHERE name = '" + Replace(rsMODPAGE4__MMColParam, "'", "''") + "'"
rsMODPAGE4.CursorType = 0
rsMODPAGE4.CursorLocation = 2
rsMODPAGE4.LockType = 3
rsMODPAGE4.Open()
rsMODPAGE4_numRows = 0
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">最新通告</h1>
<script language="JavaScript">
<!--
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
  document.MM_returnValue = (errors == '');
}
//-->
</script>
	<% If Not rsMODPAGE4.EOF Or Not rsMODPAGE4.BOF Then %>
	<form ACTION="<%=MM_editAction%>" METHOD="POST" name="form1">
	  <table border="1" cellspacing="0" cellpadding="4" align=center>
		<tr> 
		  <td valign="top" width="126">内容</td>
		  <td width="400"> 
			<textarea class=i400 name="content" cols="80" rows="10"><%=(rsMODPAGE4.Fields.Item("content").Value)%></textarea>
		  </td>
		</tr>
		<tr> 
		  <td width="126">&nbsp;</td>
		  <td width="400"> 
			<input type="submit" name="Submit" value="提交" onClick="MM_validateForm('fmtitle','','R');return document.MM_returnValue">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("name").Value %>">
	</form>
<script language="javascript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}
//-->
</script>
	<% else
	  End If ' end Not rsMODPAGE4.EOF Or NOT rsMODPAGE4.BOF %>
<!--#include file ="_bottom.asp"-->
<%
rsMODPAGE4.Close()
set rsMODPAGE4 = nothing
%>
