<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"--><%
if (Request.QueryString("subclass") <> "") then rsSUBNAME__MMColParam = Request.QueryString("subclass")

set rs=server.createobject("adodb.recordset")
sql="select * from rels_config where id=" & request.querystring("id") & " order by id asc"
rs.open sql,MM_conn_STRING,1,1
this_rels_name = rs("rels_name")
this_id = rs("id")
this_table1 = rs("table1")
this_table1_name = rs("table1_name")
this_table1_key = rs("table1_key")
this_where1 = rs("where1")
this_table2 = rs("table2")
this_table2_name = rs("table2_name")
this_table2_key = rs("table2_key")
this_where2 = rs("where2")
rs.close()
Set rs=nothing
%>
<h1 class="b_ch1"><%=this_rels_name%></h1>
<style type="text/css">
/* <![CDATA[ */
dl#rels_tb1,dl#rels_tb2,dl#relslist{width:300px;float:left;margin-right:10px}
dl#rels_tb1 dt,dl#rels_tb2 dt,dl#relslist dt{height:20px;padding:2px;padding-left:5px}
dl#rels_tb1 dt,dl#rels_tb2 dt{font-size:14px;background-color:#ececec;}
dl#rels_tb1 dd,dl#rels_tb2 dd,dl#relslist dd{padding:5px}
dl#relslist{background-color:#d4e1f7;display:none}
dl#relslist dt a{display:block;width:40px;margin-right:5px;float:left;text-align:center;border:1px #333 solid}
.nosel{filter:Alpha(Opacity=50);opacity:0.50}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
Event.onDOMReady(relsini);
var rels_config_id = <%=this_id%>;
var table1 = "<%=this_table1%>";
var table1_key = "<%=this_table1_key%>";
var table2 = "<%=this_table2%>";
var table2_key = "<%=this_table2_key%>";
var where1 = "<%=this_where1%>";
var where2 = "<%=this_where2%>"
var current_sel,current_selid;
var current_rel_id;
var sellable;
function relsini(){
	$A($$('#list_table1','#list_table2')).each(function(node){
		Event.observe(node,'click',function(){
			current_selid = node.children[node.selectedIndex].value;
			current_sel = node.id.toString().substring(10);
			current_rel_id = "";
			if(current_selid !=''){	
				$('rels_tb' + current_sel).removeClassName("nosel");
				$('rels_tb' + 2/current_sel).addClassName("nosel");
				$('relslist').style.display = 'block';
				relslist(current_sel,current_selid);
			}
		});
	});
}

function relslist(sel,selid){
	var url = 'page+pro_rela_ajax.asp';
	var pars = 'action=list&table1=' + escape(table1) + '&table1_key=' + escape(table1_key) + '&table2=' + escape(table2) + '&table2_key=' + escape(table2_key) +  '&rels_config_id=' + rels_config_id + '&current_sel=' + sel + '&current_selid=' + selid;
	var myAjax = new Ajax.Updater($("relslist").childNodes[1], url, {method: 'get', parameters: pars,asynchronous:false});
	if($('list_rel')){
		$("relslist").childNodes[0].innerHTML = "<a href=\"javascript:void(null)\" onclick=\"addrel()\">增加</a><a href=\"javascript:void(null)\" onclick=\"delrel()\">删除</a><a href=\"javascript:void(null)\" onclick=\"modrel()\">修改</a>";
	}else{
		$("relslist").childNodes[0].innerHTML = "<a href=\"javascript:void(null)\" onclick=\"addrel()\">增加</a>";
	}
	list_relini();
}

function list_relini(){
	current_rel_id = "";
	if($('list_rel')){
		Event.observe($('list_rel'),'click',function(){
			current_rel_id = $('list_rel').children[$('list_rel').selectedIndex].value;
		});
	}
}

function addrel (){
	if(current_selid !=''){
		if(current_sel == "1"){
			sellable = "<%=this_table2_name%>";
		}else{
			sellable = "<%=this_table1_name%>";
		}
		window.showModalDialog('page+pro_rela_addmod.asp?action=add&sellable=' + escape(sellable) + '&rels_config_id=' + rels_config_id + '&current_sel='+ current_sel + '&current_selid=' + current_selid + '&table1=' + escape(table1) + '&table1_key=' + escape(table1_key) + '&table2=' + escape(table2) + '&table2_key=' + escape(table2_key) + '&where1=' + escape(where1) + '&where2=' + escape(where2),window,'dialogWidth=500px;dialogHeight=200px;center: yes; help: no; resizable: no; status: no;');
	}else{
		alert("请选择客户");
	}
}

function addrelok (v1,v2,v3,v4,v5){
	var url = 'page+pro_rela_addmod.asp?action=addok&rels_config_id=' + v1 + '&current_sel=' + v2 + "&current_selid=" + v3 + "&rel_list=" + v4;
	var myAjax = new Ajax.Request(url,{postBody: v5,asynchronous:false});
}

function modrel (){
	if(current_rel_id != "") {
		window.showModalDialog('page+pro_rela_addmod.asp?action=mod&rels_config_id=' + rels_config_id + '&current_rel_id=' + current_rel_id + '&table1=' + escape(table1) + '&table1_key=' + escape(table1_key) + '&table2=' + escape(table2) + '&table2_key=' + escape(table2_key)+ '&where1=' + escape(where1) + '&where2=' + escape(where2),window,'dialogWidth=500px;dialogHeight=200px;center: yes; help: no; resizable: no; status: no;');
	}else{
		alert("请选择关联信息");
	}
}

function modrelok (v1,v2){
	var url = 'page+pro_rela_addmod.asp?action=modok&current_rel_id=' + v1;
	var myAjax = new Ajax.Request(url,{postBody: v2,asynchronous:false});
}

function delrel(){
	if(current_rel_id != "") {
		if(confirm("真的删除这条关联信息？")){
			var url = 'page+pro_rela_ajax.asp';
			var pars = 'action=del&current_rel_id=' + current_rel_id;
			var myAjax = new Ajax.Request(url, {method: 'get', parameters: pars,onSuccess:function(){relslist(current_sel,current_selid)}});
		}
	}else{
		alert("请选择关联信息");
	}
}
//]]>
</script>
<div class="pronav"><%
For i = LBound(pro_content_array) To UBound(pro_content_array) Step 2%>
<a href="<%=pro_content_array(i+1)%><%=rsSUBNAME__MMColParam%>"<%If Left(pro_content_array(i+1),Len(back_url))=back_url Then response.write(" class=ac")%>><%=pro_content_array(i)%></a>	
<%Next%></div>
<div class="help">
	◇ 在左边选择客户（先在<span style="color:#f00">客户服务》典型客户</span>中输入） 或者在右边选择产品 <br />
	◇ 再输入该客户使用的产品情况（数量、规格），或者该产品的客户情况等 <br />
	◇ 输入后，将自动关联产品和客户，在产品页面中显示该产品的所有客户；在该客户页面显示所有相关产品。 <br />
	其他产品的案例也都可以在这个页面输入。
</div>
<dl id="rels_tb1">
	<dd><select id="list_table1" size="20" style="width:290px">
		<option selected="selected">选择客户，查看与编辑关联信息</option><%
sql="select " & this_table1_key & " from " & this_table1 & " " & this_where1
set rs=server.createobject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
		%><option value="<%=rs(0)%>"><%=rs(1)%></option>
		<%
rs.movenext()
Wend
rs.close()
Set rs=nothing
%>
	</select></dd>
</dl>
<dl id="relslist">
	<dt><a href="javascript:void(null)" onclick="addrel()">增加</a></dt>
	<dd></dd>
</dl>
<dl id="rels_tb2">
	<dt><%=this_table2_name%></dt>
	<dd><select id="list_table2" size="20" style="width:290px">
		<option selected="selected">选择客户，查看与编辑关联信息</option><%
sql="select " & this_table2_key & " from " & this_table2 & " " & this_where2
set rs=server.createobject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
		%><option value="<%=rs(0)%>"><%=rs(1)%></option>
		<%
rs.movenext()
Wend
rs.close()
Set rs=nothing
%>
	</select></dd>
</dl>
<!--#include file ="_bottom.asp"-->