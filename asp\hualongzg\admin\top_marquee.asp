<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
dim top_type
top_type=""
if request.querystring("type")="dynamic" then top_type="dynamic"

select case top_type
case ""
page_title = "顶部滚动条"
top_style = "1"
case "dynamic"
page_title = "工作动态"
top_style = "3"
end select

if request.form("admin") <> "" then '批准与取消
	set rs1=server.createobject("adodb.recordset")
	sql="select pageid,top_submit,data_mod from page where pageid=" & request("pageid")
	rs1.open sql,MM_conn_STRING,3,3
	if request.form("admin") <> "更新" then  rs1("top_submit")= not rs1("top_submit")
	rs1("data_mod") = now()
	rs1.update
	rs1.close()
	set rs1=nothing
end if

if request.form("cancel") <> "" then '批准与取消
	set rs1=server.createobject("adodb.recordset")
	sql="select pageid,top_style from page where pageid=" & request("pageid")
	rs1.open sql,MM_conn_STRING,3,3
	rs1("top_style") = 0
	rs1.update
	rs1.close()
	set rs1=nothing
end if
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1"><%=page_title%></h1>
<%
'定义记录集
set rs=server.createobject("adodb.recordset") 

if top_style = "3" then
sql="select page.pageid,page.title,page.top_submit,page.top_style,page.data_mod,page.data_creat,list.name as subname,area.cate_id as classid,list.id,area.cate_name from ((page inner join list on page.subid=list.id) inner join area on area.id=list.cate_id) where area.id in (" & trim_lesson(session("flag")) & ") AND top_style=" & top_style & " order by top_submit asc,data_mod desc,data_creat desc" 
elseif top_style= "1" then
sql="select page.pageid,page.title,page.top_submit,page.top_style,page.data_mod,page.data_creat,list.name as subname,area.cate_id as classid,list.id,area.cate_name from ((page inner join list on page.subid=list.id) inner join class on area.cate_id=list.cate_id) where area.cate_id in (" & trim_lesson(session("flag")) & ") AND top_style=" & top_style & " order by top_submit asc,data_mod desc,data_creat desc" 
end If
rs.open sql,MM_conn_STRING,1,1 
if not rs.eof then
dim temp_i
temp_i =0
%>
<span class=p12>以下是您负责的栏目里要加入<%=page_title%>的页面</span><br>

<table width=650 cellpadding=2 cellspacing=0 border=1 bordercolor=<%=(back_menubackcolor)%>>
<tr align=center bgcolor=<%=(back_menubackcolor)%>>
	<td>主题（点击主题修改相应页面）</td>
	<td>所属栏目</td>
	<td>发布时间</td>
	<td>状态</td>
	<td>决定</td>
</tr>
<%while not rs.eof
temp_i = temp_i +1
if rs("top_submit") = true then
	if temp_i > 6 then
		td_con_1 = "过期"
		td_con_2 = "更新"
		td_con_22 = "重新让这个页面在" & page_title & "显示？"
	else
		td_con_1 = "已批"
		td_con_2 = "取消"
		td_con_22 = "不让这个页面在" & page_title & "显示？"
	end if
	else
	td_con_1 = "待批"
	td_con_2 = "批准"
	td_con_22 = "确定让这个页面在" & page_title & "显示？"
end if
%>
<form method=post>
<tr>
<td class=p12><a href=page_mod.asp?subname=<%=server.urlencode(rs("subname"))%>&action=mod&pageid=<%=rs("pageid")%> title="点击修改这个页面"><%=HTMLEncode(rs("title"))%></a></td>
<td nowrap width=150 class=p12><a href=page2.asp?action=mod&class=<%=rs("classid")%>><%=rs("name")%></a> &gt; <a href=page3.asp?action=mod&subclass=<%=rs("subid")%>><%=rs("subname")%></a></td>
<td width=120 class=p12 nowrap><%=rs("data_creat")%></td>

<td width=30 align=center><%=td_con_1%></td>

<%if session("ismember")=true then%>
<td align=center width=30><input name="cancel" type="submit" value="取消" onClick="GP_popupConfirmMsg('确定取消这个页面的主题在<%=page_title%>显示？');return document.MM_returnValue"></td>
<%elseif Session("isadmin") = true then%>
<td align=center width=30><input name="admin" type="submit" value="<%=td_con_2%>" onClick="GP_popupConfirmMsg('<%=td_con_22%>');return document.MM_returnValue"></td><%
end if%>

</tr>
<%if temp_i=6 then response.write("<tr><td height=2 colspan=5>&nbsp;</td></tr>")%>
<input type=hidden name="pageid" value="<%=rs("pageid")%>">
</form>
<%
rs.movenext()
wend%>
</table>
<br>
<div class=p12>
<span class=p14><font color=ff0000>说明</font></span><br>
最多显示6条，按批准时间排序。新闻栏目的内容（包括图片新闻和要闻集萃）不在<%=page_title%>显示<br>
待批：指等待管理员批准；<br>
已批：指管理员已经批准，在<%=page_title%>显示；<br>
过期：指管理员已经批准，但因为过期，不在<%=page_title%>显示，但管理员可以通过“更新”，使这个主题再次在<%=page_title%>显示。
</div>
<%
else			'如果没有记录
	if Session("isadmin") = true then
	response.write("<h3>本站没有设置要在" & page_title & "显示的页面</h3>")
	else
	response.write("<h3>在您负责的栏目里，没有设置要在" & page_title & "显示的页面</h3>")
	end if
end if
rs.close()
set rs=nothing
%>
<!--#include file ="_bottom.asp"-->