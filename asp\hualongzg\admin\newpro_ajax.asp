<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<%
start_catelcode = request.querystring("lcode")
set rs=server.createobject("adodb.recordset")
sql="select list.name,list.id,list.cate_id,area.cate_name,area.cate_lcode,list.is_top from list inner join area on list.cate_id = area.cate_id where left(cate_lcode,len('" & start_catelcode & "')) = '" & start_catelcode & "' order by sort_id2 desc"
rs.open sql,MM_conn_STRING,1,1
while Not rs.eof
If rs("is_top") = True Then
	checkedstr= " checked=""checked"""
Else
	checkedstr= ""
End if
%>
<li><div><input type="checkbox" name="id" value="<%=rs("id")%>"<%=checkedstr%> /> <a href="javascript:void(null)" class="proname"><%=rs("name")%></a><div class="sortmenu" id="sortmenu<%=rs("id")%>"><a href="javascript:void(null)" onclick="sortmenu(<%=rs("id")%>,'top')">顶部↑↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rs("id")%>,'up')">向上↑</a><a href="javascript:void(null)" onclick="sortmenu(<%=rs("id")%>,'down')">向下↓</a><a href="javascript:void(null)" onclick="sortmenu(<%=rs("id")%>,'bottom')">底部↓↓</a></div></div></li>
<%rs.movenext()
wend
%>