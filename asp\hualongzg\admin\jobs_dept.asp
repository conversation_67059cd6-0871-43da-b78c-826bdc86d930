<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="event__config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">部门设置</h1>

<%
if not isempty(request("selAnnounce")) then
	idlist=request("selAnnounce")
	if instr(idlist,",")>0 then
		dim idarr
		idArr=split(idlist)
		dim id
			for i = 0 to ubound(idarr)
			id=clng(idarr(i))
			call deleteannounce(id)
			next
		else
			call deleteannounce(clng(idlist))
	end if
end if 

sub deleteannounce(id)
	dim rs,sql
	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	set rs=server.createobject("adodb.recordset")
	sql="delete from jobs_dept where id="&cstr(id)
	conn.execute sql
	conn.close()
	set conn=nothing
End sub

If request.querystring("action") = "add" Then
	sql_command("insert into jobs_dept (name) values ('" & request.form("dept")) & "')"
	response.redirect("jobs_dept.asp")
End If

dim rs
dim sql
set rs=server.createobject("adodb.recordset") 
sql="select * from jobs_dept order by id asc"
rs.open sql,MM_conn_STRING,1,1 %>
<table width="600">
<form name="search" method="post" action="jobs_dept.asp">
<thead>
<tr>
<td colspan="3" style="text-align:right"><a href="javascript:void(null)" onclick="add_dept()">
<span class="emph">增加部门</span></a></td>
</tr>
<tr> 
<td>部门</td>
<td><input type="submit" value="删除" onclick="GP_popupConfirmMsg('如果部门有招聘职位，删除部门将使应聘这些职位的应聘者资料列在其他职位中\n\n确定删除部门吗？');return document.MM_returnValue"></td>
</TR>
</thead>
<tbody>
<%
do while not rs.eof%>
<tr> 
<td><%=rs("name")%></td>
<td><input type="checkbox" name="selAnnounce" value="<%=cstr(rs("id"))%>">&nbsp;</td>
</tr>
<% RS.movenext
loop
%></form>
<form name="adddept" action="jobs_dept.asp?action=add" onsubmit="return veryname()" method="post">
<tr><td colspan=3 id="add_dept"></td></tr></form>
</tbody>
</table>
<script language="JavaScript">
<!--
function add_dept(){
var s=document.getElementById("add_dept");
if (!(s.innerHTML)||(s.innerHTML == ""))s.innerHTML = "<input type=\"text\" name=\"dept\" value=\"\"> <input type=submit value='增加部门'>";else {s.innerHTML="";}
}

function veryname(){
if (document.adddept.dept.value==""){
	alert('请输入部门名称');
	document.adddept.dept.focus();
	return false;
	}
}
//-->
</script>
<%
rs.close()
set rs=nothing
%>

<!--#include file ="_bottom.asp"-->