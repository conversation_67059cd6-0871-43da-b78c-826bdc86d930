<%
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'									产品系统参数
'-------------------------------------------------------------------------------------------
Dim top_node '定义树起点

dim pro_trademark_on					
dim cate_nomix							
dim pro_english_on							
dim cate_descript						
dim cate_img_on, cate_img_width, cate_imgwidth	'类特征图，如果true，则图片最大宽、高
dim cate_del_confirm
dim cate_select_size
dim cate_level_max

dim filterjp_on						
dim watermark_on						
dim img_big_on,img_big2_on,thumb_pro_on	
dim is_newest_on						
dim is_new_on							
dim is_top_on
dim table_sql_pro(1),table_sql(1)		'数据库表修改sql
dim cate_sort_on								'排序

top_node = ""
top_node_id = ""
top_node_level = ""

table_sql_pro(0) = ""
table_sql_pro(1) = ""
table_sql(0) = ""
table_sql(1) = ""
'类
cate_name_str = "区域"		'产品类别名称
pro_trademark_on = 0			'品牌，有则为1，否则为0
cate_nomix = true				'是否严格类，如果true，则不允许同级下类、产品混合
pro_english_on = false		'是否打开英文属性
cate_descript = false		'是否打开详细属性
cate_img_on = false			'类特征图，如果true，则图片最大宽cate_img_width、高cate_imgwidth
cate_del_confirm = true		'删除类前要等无产品才能删除
cate_select_size = 25		'类列表、产品列表高度
cate_level_max = 8			'限制类级别，一般最大4级
cate_sort_on = true
cate_mana_on = true

'列表
pro_name_str = "列表"		'列表名称
watermark_on = true			'产品图是否打上水印
filterjp_on = true			'产品是否过滤日文片假名，在windows2000上要过滤，否则搜索里会出现内存泄漏。
is_newest_on = false			'产品是否在首页新品里显示
is_new_on = true				'产品是否推荐新品
is_top_on = false				'产品是否在页面顶部显示
img_big_on = true			'产品的三种图片是否打开（如果要打开图，首先要打开img_big）
img_big2_on = true
thumb_pro_on = True
list_num = 15					'列表页面数

img_bigwidth = 200			'大图的宽高
img_bigheight = 180
img_big_kuang = ""
img_big_trademark_on = false	'是否打水印

img_big2width = 720			'大图二的宽高
img_big2height = 480
img_big2_kuang = ""
img_big2_trademark_on = true

thumbwidth = 86				'小图的宽高
thumbheight = 68				
thumb_kuang = ""		'背景，可以没有，这个图要放到 images 目录下
thumb_trademark_on = false

cate_top_add = true			'允许增加产品顶级类，如果 cate_mana_on =false 则这个选项无效

pro_content = false			'是否打开产品简介
pro_hidecontent = false		'是否隐藏产品简介与内容，内容全部在产品详细列表里显示，产品记录里只输入特征图


Dim pro_content_array
pro_content_array = Array("产品说明","page+2.asp?subclass=","产品案例","page+pro_rela.asp?id=1&subclass=","产品图集","page+pro_img.asp?id=") 	'分别是
'pro_content_array = Array("产品说明","page+2.asp?subclass=","详细内容","page+pro_list.asp?id=","产品案例","page+pro_rela.asp?id=1&subclass=","产品图集","page+pro_img.asp?id=","资料下载","page+pro_down.asp?id=") 	'分别是产品说明、详细内容（列表形式）、产品图集、资料下载

'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
function filterjp(str)
	dim jp
	jp = array("ァ","ア","ィ","イ","ゥ","ウ","ェ","エ","ォ","オ","カ","ガ","キ","ギ","ク","グ","ケ","ゲ","コ","ゴ","サ","ザ","シ","ジ","ス","ズ","セ","ゼ","ソ","ゾ","タ","ダ","チ","ヂ","ッ","ツ","ヅ","テ","デ","ト","ド","ナ","ニ","ヌ","ネ","ノ","ハ","バ","パ","ヒ","ビ","ピ","フ","ブ","プ","ヘ","ベ","ペ","ホ","ボ","ポ","マ","ミ","ム","メ","モ","ャ","ヤ","ュ","ユ","ョ","ヨ","ラ","リ","ル","レ","ロ","ヮ","ワ","ヰ","ヱ","ヲ","ン","ヴ","ヵ","ヶ","ー","ヽ","ヾ")
	for filterjp_i = 0 to 88

	str = replace(str,jp(filterjp_i),"")
	next
	filterjp = str
end function

sub update_procount(cate_id) '更新类别的产品数量和cate_pro_sign值
	dim rs,sql
	set rs=server.createobject("adodb.recordset")
	sql="select count(id) as count_id from list where cate_id="& cate_id & " group by cate_id"
	rs.open sql,MM_conn_STRING,1,1

	set conn=server.createobject("ADODB.CONNECTION")
	conn.open MM_conn_STRING
	if  not rs.eof then
		sql = "update area set cate_pro_sign=true,cate_pro_count=(" & rs("count_id") & ") where area.cate_id=" & cate_id
		else
		sql = "update area set cate_pro_sign=false,cate_pro_count=0 where area.cate_id=" & cate_id
	end if
	conn.execute sql
	set conn=nothing
	set rs=nothing	
end Sub

'分页函数
'total_page为总页数,ac_page为当前页,display_page为显示的页数，应该为奇数，至少为5，language为语言
'"en"为英文 "cn"为中文
'onclick_str link_str 为链接串（必须有值），前面的实际上优先。格式为其中 #page# 为替换成页号
Function page_list(ByVal total_page,ByVal curpage,ByVal display_page,ByVal language,ByVal onclick_str, ByVal link_str)

	Dim p_c_id,page_altn(5)
	p_c_id = request.querystring("pid")
	If language = "en" Then
	page_altn(0) = "First"
	page_altn(1) = "Previous"
	page_altn(2) = "Next"
	page_altn(3) = "Last"
	page_altn(4) = "Total: "
	page_altn(5) = ""
	Else
	page_altn(0) = "第1页"
	page_altn(1) = "上一页"
	page_altn(2) = "下一页"
	page_altn(3) = "最后页"
	page_altn(4) = "共"
	page_altn(5) = "页"
	End if
	If curpage = "" Then
		curpage = 1
	Else
		curpage = CInt(curpage)
	End if
	page_list = "<ul>"

	'显示前页开始，当前页大于1
	if curpage > 1 Then
		page_list = page_list & "<li><a title=""" & page_altn(1) & """" & " href=""" & replace_page(link_str,curpage-1,1) & """ onclick=""" & replace_page(onclick_str,curpage-1,0) & """>&lt;</a></li>"
	End if

	'显示第1页开始，如果没有显示第1页（即当前页为1＋半数）
	if curpage > (display_page + 1)/2 Then
		page_list = page_list & "<li><a href=""" & replace_page(link_str,"1",1) & """ onclick=""" & replace_page(onclick_str,"1",0) & """>1" & "</a></li>"
	End If

	'显示中间部分（当前页的前一半、当前页、后一半）
	for page_break_temp_i= (curpage - (display_page -1)/2) to (curpage + (display_page -1)/2)
		if (page_break_temp_i > 0) AND (page_break_temp_i <= total_page) then
			if page_break_temp_i = curpage then
				page_list = page_list & "<li><a href=""" & replace_page(link_str,page_break_temp_i,0) & """ class=""ac"" onclick=""" & replace_page(onclick_str,page_break_temp_i,1) & """>" & page_break_temp_i & "</a></li>"
			Else
				page_list = page_list & "<li><a href=""" & replace_page(link_str, page_break_temp_i,0) & """ onclick=""" & replace_page(onclick_str, page_break_temp_i,1) & """>" & page_break_temp_i & "</a></li>"
			end if
		end if
	Next

	'显示最后页开始
	if curpage < total_page - (display_page - 1)/2 then	'如果最后页没有出现，即当前页小于（总页数-半）
		page_list = page_list & "<li><a href=""" & replace_page(link_str,total_page,0) & """ onclick=""" & replace_page(onclick_str,total_page,1) & """>" & total_page & "</a></li>"
	End If
	
	'显示下一页开始
	if curpage < total_page then'如果不是最后1页，则显示后页
		page_list = page_list & "<li><a title=""" & page_altn(2) & """ href=""" & replace_page(link_str,curpage + 1,0) & """ onclick=""" & replace_page(onclick_str, curpage + 1,1) & """>&gt;</a></li>"
	End If
	page_list = page_list & "</ul>"
End Function
function replace_page(ByVal ll,ByVal str,Byval ltype)
	if ltype = 1 then
	replace_page = replace(ll,"#page#",str)
	else
	replace_page = replace(ll,"#page#",str)
	End if
end Function

Function VBsUnEscape(str) 
    dim i,s,c 
    s="" 
    For i=1 to Len(str) 
        c=Mid(str,i,1) 
        If Mid(str,i,2)="%u" and i<=Len(str)-5 Then 
            If IsNumeric("&H" & Mid(str,i+2,4)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+2,4))) 
                i = i+5 
            Else 
                s = s & c 
            End If 
        ElseIf c="%" and i<=Len(str)-2 Then 
            If IsNumeric("&H" & Mid(str,i+1,2)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+1,2))) 
                i = i+2 
            Else 
                s = s & c 
            End If 
        Else 
            s = s & c 
        End If 
    Next 
    VBsUnEscape = s 
End Function 

Function VBsEscape(str) 
    dim i,s,c,a 
    s="" 
    For i=1 to Len(str) 
        c=Mid(str,i,1) 
        a=ASCW(c) 
        If (a>=48 and a<=57) or (a>=65 and a<=90) or (a>=97 and a<=122) Then 
            s = s & c 
        ElseIf InStr("@*_+-./",c)>0 Then 
            s = s & c 
        ElseIf a>0 and a<16 Then 
            s = s & "%0" & Hex(a) 
        ElseIf a>=16 and a<256 Then 
            s = s & "%" & Hex(a) 
        Else 
            s = s & "%u" & Hex(a) 
        End If 
    Next 
    VBsEscape = s 
End Function 

Function imgDimension(str,wh)'判断upload下的一个图形的宽度与高度 wh的值'w','h'
	Set Img = Server.CreateObject("Persits.Jpeg") 
	ImgPath = Server.MapPath("..\upload") & "\" & str 
	Img.Open ImgPath
		If wh="w" Then
			imgDimension = Img.OriginalWidth
		ElseIf wh="h" Then
			imgDimension = Img.OriginalHeight
		End if
	Set Img=nothing
End Function

function cate_type_list_name(cateid) '获取区域里的列表名称
	If IsNull(cateid) Or cateid="" Then
		cate_type_list_name = ""
	else
		set rs_cate_type_list_name=server.createobject("adodb.recordset") 
		sql="select area.cate_type_id,area_type.type_list_name from area inner join area_type on area.cate_type_id =area_type.type_id where area.cate_id=" &cateid
		rs_cate_type_list_name.open sql,MM_conn_STRING,1,1 
		cate_type_list_name = rs_cate_type_list_name("type_list_name")
		rs_cate_type_list_name.close()
		set rs_cate_type_list_name=Nothing
	End if
End Function
%>