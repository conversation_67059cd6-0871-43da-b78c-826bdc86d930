<%'2009.04.17 首先用于 chaint.com , 2009.05用于reshine.net日韩文版,改为utf-8

Function tree_show(ByVal cate_lcode)
'新的web2.0 树 cate_lcode 为树起点 cate_lcode href_str为产品类链接
'当前类 li class = curr
	Dim rs,sql,cate_name,j
	If cate_lcode = "" Then
		j = 0
	Else
		j = len(cate_lcode)/2-1
	End if
	tree_show = ""
	Set rs = Server.CreateObject("ADODB.Recordset")
	If cate_lcode = "" Then
		sql="select cate_id,cate_name,cate_level,cate_lcode,cate_pro_sign,cate_pro_count,(cate_level-1) as cate_level_show from area where cate_screen = false and left(cate_lcode,2)='01' and cate_level>=3 order by cate_lcode asc"
	else
		sql="select cate_id,cate_name,cate_level,cate_lcode,cate_pro_sign,cate_pro_count,(cate_level-2) as cate_level_show from area where cate_screen = false and left(cate_lcode,len('" & cate_lcode & "'))='" & cate_lcode & "' AND cate_level>3 order by cate_lcode asc"
	End If
	rs.open sql,MM_conn_STRING,1,1
	Dim i,i1,i2,li_str
	i = 1
	i2 = 0
	While Not rs.eof
		cate_level_show = rs("cate_level_show")
		cate_level = rs("cate_level")
		cate_name = HTMLEncode(rs("cate_name"))
		li_str = "<li>"
		
		If i= 1 Then 
			pre_cate_level = cate_level - 1
		Else
			rs.MovePrevious()
			pre_cate_level = rs("cate_level")
			rs.movenext()
		End if

		If pre_cate_level < cate_level Then
			tree_show = tree_show & "<ul>" & li_str
			tree_show = tree_show & "<h" & cate_level_show & "><span>" & cate_name & "</span>" & "</h" & cate_level_show & ">"
			If rs("cate_pro_sign") = True Then tree_show = tree_show & prolist(rs("cate_id")) 
		End if
		If pre_cate_level = cate_level Then
			tree_show = tree_show & "</li>" & li_str & "<h" & cate_level_show & "><span>" & cate_name & "</span>" & "</h" & cate_level_show & ">"
			If rs("cate_pro_sign") = True Then tree_show = tree_show & prolist(rs("cate_id")) 
		End If
		If pre_cate_level > cate_level Then
			For i1=pre_cate_level - cate_level To 1 Step -1
				tree_show = tree_show & "</li></ul>"
			Next
			tree_show = tree_show & "</li>" & li_str
			tree_show = tree_show & "<h" & cate_level_show & "><span>" & cate_name & "</span>" & "</h" & cate_level_show & ">"
			If rs("cate_pro_sign") = True Then tree_show = tree_show & prolist(rs("cate_id")) 
		End If
		If i = rs.recordcount Then
			For i1=cate_level -1 To j Step -1
				tree_show = tree_show & "</li>"
			next
		End If
		
		i=i+1
		rs.movenext()
	wend
End Function

Sub pro_out_sub(ByVal proid)
'通过id得到这个产品的信息
'如果同时执行树生成，要先执行本过程，因为要传递 pro_current_cate_id
'pro_current_cate_name pro_content pro_current_cate_id pro_current_cate_lcode
	If Not IsNumeric(proid) Then
		proid = first_id("product")
	End if
	Dim rs,sql
	sql = "select list.id,list.name,list.content2,list.content,list.cate_id AS cate_id,area.cate_pro_count,area.cate_name,area.cate_lcode,area.cate_pro_count,list.img,list.imgwidth,list.imgheight,b.minpage_id,b.countpage from ((list inner join area on area.cate_id=list.cate_id) left join  (select subid, min(pageid) as minpage_id,count(pageid) as countpage from page where page.isscreen = false group by subid ) AS b on list.id = b.subid) where id=" & proid
	set rs = Server.CreateObject("ADODB.Recordset")
	rs.open sql,MM_conn_STRING,1,1
	if not rs.eof then
		pro_out(0) = rs("cate_id")
		pro_out(1) = rs("cate_lcode")
		pro_out(2) = HTMLEncode(rs("cate_name"))
		'以上与 proarea_para(2) 一样
		pro_out(3) = rs("id")
		pro_out(4) = HTMLEncode(rs("name"))
		pro_out(6) = rs("cate_pro_count")
		pro_out(7) = rs("minpage_id")
		pro_out(8) = rs("countpage")
		If Trim(rs("content")) <> "" then
			pro_out(5) = pro_content & "<p class=""summary"">" & replace(Replace(HTMLEncode(rs("content")),vbCrLF,"<br />"),"  ","&nbsp;&nbsp;") & "</p>"
		Else
			pro_out(5) = ""
		End If
			pro_out(5) = pro_out(5) & "<div class=""procon"">" & rs("content2") & "</div>"
		rs.close()
		set rs=nothing
	else
		rs.close()
		set rs=nothing
		Call error_flow("none this product")
	end If
End Sub

Function prolist(cateid)
	Set rs1 = Server.CreateObject("ADODB.Recordset")
	sql1 = "select id,name,list.thumb,list.thumbwidth,list.thumbheight from list where list.cate_id=" & cateid
	rs1.open sql1,MM_conn_STRING,1,1
	While Not rs1.eof
		prolist = prolist & "<li><a href=""" & plist_name & "?id=" & rs1("id") & """ class=""pro"">" & "<img src=""/upload/" & rs1("thumb") & """ width=""" & rs1("thumbwidth") & """ height=""" & rs1("thumbheight") & """ alt=""" & rs1("name") & """ /><br /><span>" & rs1("name") & "</span></a></li>"
		rs1.movenext()
	Wend
	rs1.close()
	Set rs1 =Nothing
	prolist = "<ul class=""prolist"">" & prolist & "</ul>"
End function

Function prolist2(ByVal cateid,ByVal proid,ByVal strlength)
	Dim ac_str,title_str,name_str
	Set rs1 = Server.CreateObject("ADODB.Recordset")
	sql1 = "select id,name,list.thumb,list.thumbwidth,list.thumbheight from list where list.cate_id=" & cateid & " order by sort_id asc"
	rs1.open sql1,MM_conn_STRING,1,1
	While Not rs1.eof
		If rs1("id") = proid then
		ac_str = " class=""ac"""
		Else
		ac_str = ""
		End If
		If strlength <> 0 And len(rs1("name")) > strlength Then
			name_str = Left(rs1("name"),strlength-1) & "..."
			title_str = " title=""" & rs1("name") & """"
		Else
			name_str = rs1("name")
			title_str = ""
		End if
		prolist2 = prolist2 & "<li><a href=""" & plist_name & "?" & pro_querystingid & "=" & rs1("id") & """" & ac_str & title_str & ">" & name_str & "</a></li>"
		rs1.movenext()
	Wend
	rs1.close()
	Set rs1 =Nothing
End Function

sub f_pro_current_cate_lcode(cate_id)
	'由 cate_id 得到当前的 cate_lcode ,这个产品类的类名
	'对应变量如下：
	'pro_current_cate_lcode
	'pro_current_cate_name
	pro_current_cate_id = cate_id

	dim rs,sql,f_lcode
	set rs = Server.CreateObject("ADODB.Recordset")
	sql = "select cate_lcode,cate_name from area where cate_id=" &cate_id
	rs.open sql,MM_conn_STRING,1,1
	if not rs.eof then		'如果有这个类
		pro_current_cate_lcode = rs("cate_lcode")
		pro_current_cate_name = HTMLEncode(rs("cate_name"))
		rs.close()
		Set rs=nothing
	else					'如果没有这个类，转向
		rs.close()
		set rs=nothing
		response.redirect (pro_redirect)
	end if
end sub

function pro_path(lcode)
'从 cate_lcode 得到类路径，参数lcode
	pro_path = ""
	If Len(lcode) >= 4 then
		if lcode <> "" then
			dim lcode_str,i
			for i=2 to len(lcode) step 2
				if i > 2 then
					lcode_str = lcode_str & "," & "'" & left(lcode,i) & "'"
				else
					lcode_str = "'" & left(lcode,2) & "'"
				end if
			next

			dim rs,sql
			sql = "select cate_id,cate_name from area where cate_lcode in(" & lcode_str & ") and len(cate_lcode)>2 order by cate_lcode asc"
			set rs = Server.CreateObject("ADODB.Recordset")
			rs.open sql,MM_conn_STRING,1,1
			i = 1
			while not rs.eof
				pro_path = pro_path & "<p><span>" & rs("cate_name") & "</span></p>"
				rs.movenext()
				i=i+1
			wend
			rs.close()
			set rs=nothing
		end If
	End if
end Function

Function product_cate_page_nav
'生成产品类页面导航 通过url参数 page_nav_querystring 得到当前页
'本函数显示全部页
	Dim i,current_id,url,product_cate_page_nav_base
'	if front__language = "english" Then
'		url=request("url") & "?language=english"
'	Else
		url=request("url")
'	End If

	If request.querystring(querystring_function) <> "" Then
		If (InStr(1, url, "?", vbTextCompare) = 0) Then
			url = url & "?"
		Else
			url = url & "&amp;"
		End If
		url = url & querystring_function & "=" & request.querystring(querystring_function)
	End If

	If request.querystring(page_nav_querystring) <> "" Then
		current_id  = CInt(request.querystring(page_nav_querystring))
		Else
		current_id = 1
	End If

	If front__language = "english" Then
		product_cate_page_nav_base = "<span>Total: " & pro_list_count & "</span>"
	Else
		product_cate_page_nav_base = pro_current_cate_name & "共有" & pro_list_count & "个产品"
	End If

	If pro_list_pagecount > 1 Then
		If (InStr(1, url, "?", vbTextCompare) = 0) Then
			url = url & "?"
			Else
			url = url & "&amp;"
		End If
		product_cate_page_nav = "<ul class=""plist"">"
		For i=1 To pro_list_pagecount
			If i = current_id Then
				product_cate_page_nav = product_cate_page_nav & "<li class=""curr""><a href=""" & url & page_nav_querystring & "=" & i & """>" & i & "</a></li>"
			Else
				product_cate_page_nav = product_cate_page_nav & "<li><a href=""" & url & page_nav_querystring & "=" & i & """>" & i & "</a></li>"	
			End If
		Next
		product_cate_page_nav = product_cate_page_nav & "</ul>"

		if front__language = "english" Then
			product_cate_page_nav = product_cate_page_nav & product_cate_page_nav_base
		Else
			product_cate_page_nav = "<div>页码：" & product_cate_page_nav & "</div>" & product_cate_page_nav_base & " &nbsp; " & "每页" & pro_list_apage & "个/ 共" & pro_list_pagecount & "页"
		End If
	Else
		product_cate_page_nav = product_cate_page_nav_base
	End If
End function

Sub product_detail(request_proid_name)
'通过id得到这个产品的信息，如果同时执行树生成，要先执行本过程，因为要传递 pro_current_cate_id
'pro_current_cate_name pro_content pro_current_cate_id pro_current_cate_lcode
	If request.querystring(request_proid_name) = "" Then
		response.redirect(pro_redirect)
		response.End
	End if
	Dim rs,sql
	sql = "select list.id,list.name,list.content2,list.content,list.cate_id AS cate_id,area.cate_pro_count,area.cate_name,area.cate_lcode,list.img,list.imgwidth,list.imgheight from (list inner join area on area.cate_id=list.cate_id) where list.id=" & SafeRequest(request_proid_name,1)
	set rs = Server.CreateObject("ADODB.Recordset")
	rs.open sql,MM_conn_STRING,1,1
	if not rs.eof then
		pro_current_cate_name = HTMLEncode(rs("cate_name"))
		pro_current_cate_id = rs("cate_id")
		pro_current_cate_lcode = rs("cate_lcode")
		pro_current_name = HTMLEncode(rs("name"))
'		pro_content = "<div class=""proimg""><img class=""proimg"" src=""/upload/" & rs("img")  & """ width=""" & rs("imgwidth") & """ height=""" & rs("imgheight") & """ border=""0"" alt=""" & pro_current_name & """></div>"
		If Trim(rs("content")) <> "" then
			pro_content = pro_content & replace(Replace(HTMLEncode(rs("content")),vbCrLF,"<br />"),"  ","&nbsp;&nbsp;")
		End if
		pro_content = pro_content & rs("content2")
		pro_current_id = rs("id")
		pro_list_count = rs("cate_pro_count")
		rs.close()
		set rs=nothing
	else
		rs.close()
		set rs=nothing
		response.redirect(pro_redirect)
	end If
End Sub

Function product_page_nav
'生成产品的页面导航
	Dim i,current_id,url,product_cate_page_nav_base,product_page_nav_p_n
	if front__language = "english" Then
		url=request("url") & "?language=english"
	Else
		url=request("url")
	End If

	If request.querystring(querystring_function) <> "" Then
		If (InStr(1, url, "?", vbTextCompare) = 0) Then
			url = url & "?"
		Else
			url = url & "&amp;"
		End If
		url = url & querystring_function & "=" & request.querystring(querystring_function)
	End If

	If request.querystring(page_nav_querystring) <> "" Then
		current_id  = CInt(request.querystring(page_nav_querystring))
		Else
		current_id = 1
	End If

	If front__language = "english" Then
		product_page_nav = "<li><a href=""javascript:history.go(-1)""><span></span>back</a></li><li><a href=""javascript:window.print()""><span></span>print</a></li><li><a href=""buy.asp?id=" & pro_current_id & this__lan2 & "&pro=" & server.urlencode(pro_current_name) & """><span></span>buy</a></li>"
	Else
		product_page_nav = "<li><a href=""javascript:history.go(-1)"" class=""bback""><span></span>返 回</a></li><li><a href=""javascript:window.print()"" class=""bprint""><span></span>打 印</a></li><li><a href=""buy.asp?id=" & pro_current_id & this__lan2 & "&pro=" & server.urlencode(pro_current_name) & """ class=""bbuy""><span></span>加入订单</a></li>"
	End If

	product_page_nav = "<ul class=""pnav"">" & product_page_nav & "</ul>"

	Dim rs,sql
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "(select top 1 id,0 as pgo from products where sort_id<" & pro_current_id & " and cate_id=" & pro_current_cate_id & " order by sort_id desc) union (select top 1 id,1 as pgo from products where sort_id>" & pro_current_id & " and cate_id=" & pro_current_cate_id & " order by sort_id asc)"
	rs.open sql,MM_conn_STRING,1,1
	If Not rs.eof Then
		product_page_nav_p_n = "<ul class=""pnav"">"
		While Not rs.eof
			If rs(1) = 0 Then product_page_nav_p_n = product_page_nav_p_n & "<li><a href=""" & pro_detail_url & rs("id") & """>Previous</a></li>"
			If rs(1) = 1 Then product_page_nav_p_n = product_page_nav_p_n & "<li><a href=""" & pro_detail_url & rs("id") & """>Next</a></li>"
		rs.movenext()
		Wend
		product_page_nav_p_n = product_page_nav_p_n & "</ul>"		
	End if
	rs.close()
	Set rs=Nothing
	
	product_page_nav = product_page_nav & product_page_nav_p_n
End Function

Function product_list_item(ByVal proid)
	'检查一个产品的列表项目，输出一个数组(0,3,1,4,2,5) ，如 pro_out(8) > 0 再使用些函数
	product_list_item = ""
	Dim rs,sql,i
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql="select page_style_id,count(pageid) from page where page.subid=" & proid & " group by page.page_style_id"
	rs.open sql,MM_conn_STRING,1,1
	i =0
	While Not rs.eof
		If i = 0 then
			product_list_item = product_list_item & rs(0) & "," & rs(1)
		Else
			product_list_item = product_list_item & "," & rs(0) & "," & rs(1)
		End if
		i=i+1
	rs.movenext()
	wend
	rs.close()
	Set rs=nothing
End Function

sub product_out_sub(byval proid,ByVal page_style_id,ByVal nostr)
	'要在页面中定义 current_cid
	'输出product页详细内容，page_style_id 为：
	'0 详细（暂未作）
	'1 图
	'4 下载
	'9 案例 (暂未作）
	If (not isNumeric(proid)) Or IsNull(proid) Or (proid= "") then
		response.redirect (pro_redirect)
	End If
	Dim rs,sql,li,li_str
	Select Case page_style_id
	Case 0
		Set rs = Server.CreateObject("ADODB.Recordset")
		sql = "select pageid,title from page where page.isscreen=false AND page.subid=" & proid & " AND page.page_style_id=0 order by sort_id asc"
		rs.open sql,MM_conn_STRING,1,1
		i = 0
		If Not rs.eof Then first_c = rs("pageid")

		If request.querystring(plist_more_querystingid) <> "" Then
			current_cid = SafeRequest(plist_more_querystingid,1)
		Else
			current_cid = first_c
		End if
		If rs.recordcount > 1 then
		While Not rs.eof
		If i = 0 Then
			al_str = " class=""alternate"""
		Else
			al_str = ""
		End If
		If CInt(current_cid) = rs("pageid") Then
			a_cls = " class=""current"""
		Else
			a_cls = ""
		End if
		product_out = product_out & "<li" & al_str & "><a href=""" & "product_list.asp?" & pro_querystingid & "=" & proid & "&amp;" & plist_more_querystingid & "=" & rs("pageid") & """" & a_cls & ">" & rs("title") & "</a></li>"

		i=i+1
		If i= 2 Then i=0
		rs.movenext()
		Wend
		End if
		rs.close()
		Set rs=Nothing

	Case 1
		Set rs = Server.CreateObject("ADODB.Recordset")
		product_out = ""
		sql = "SELECT page.pageid,page.title,page.data_mod,page.img,page.imgwidth,page.imgheight,page.imgsmall,page.imgsmallwidth,page.imgsmallheight,list.date_display_on,list.sign_on,list.style_id,trim(page.content)<>'' as iscon from (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id where page.isscreen=false and page.subid=" & proid & " AND page_style_id=" & page_style_id & " order by page.sort_id asc"
		rs.open sql,MM_conn_STRING,1,1
		if rs.recordcount > 0 Then
			While Not rs.eof
				product_out = product_out & "<li>"
					product_out = product_out & "<a class=""thumb"" href=""upload/" & rs("img") & """ rel=""lightbox[plants]"" onclick=""javascript:return false""><img class=""imglbox"" src=""upload/" & rs("imgsmall") & """ width=""" & rs("imgsmallwidth") & """ height=""" &  rs("imgsmallheight") & """ alt=""" & HTMLEncode(rs("title")) & """ /> </a>"
					product_out = product_out & "<span class=""name"">" & HTMLEncode(rs("title")) & "</span>"
				rs.movenext()
			wend
		Else
			product_out = "<li class=""none"">" & nostr & "</li>"
		End If
		rs.close()
		Set rs=Nothing
	Case 4
		Set rs = Server.CreateObject("ADODB.Recordset")
		product_out = ""
		sql = "SELECT page.pageid,page.title,page.data_mod,page.content from (page inner join list on page.subid=list.id) inner join area on area.cate_id = list.cate_id where page.isscreen=false and page.subid=" & proid & " AND page_style_id=" & page_style_id & " order by page.sort_id asc"
		rs.open sql,MM_conn_STRING,1,1
		if rs.recordcount > 0 Then
			While Not rs.eof
				product_out = product_out & "<li><h2>" & HTMLEncode(rs("title")) & "</h2><p>" & rs("content") & "</p></li>"
				rs.movenext()
			wend
		Else
			product_out = "<li>" & nostr & "</li>"
		End If
		rs.close()
		Set rs=Nothing
	Case 9

	End select
End sub
%>