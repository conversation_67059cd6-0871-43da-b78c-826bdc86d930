<!--#include file="Connections/conn.asp" -->
<!--#include file="_contact.asp" -->
<!--#include file="_base.asp" -->
<%
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
'※　　　　　　　　　　　前台设置　　　　　　　　　　　　    　※
'※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※

'◇网站描述、关键字。用于首页，一般小型网站，用于所有页面与产品页面
'---------------------------------------------------------------------------------
dim front_Description
Const front_mailserver = "mail.hualongzg.com"
Const front_mainserver = "hualongzg.com"
Const trade_name = "华隆机械"
Const trade_name_en = "hualongzg"
Const front_Keywords = "散装物料设备,斗轮机,斗轮堆取料机,混匀堆取料机,板式给矿机,自动装卸设备,链斗卸车机,定量圆盘给料机,皮带输送设备,球磨,破碎设备,板材矫直机,链篦机,金属板材矫正设备,冶金,矿山,电力,建材,配套设备,备件"
front_Description = "长沙华隆重型机器制造有限公司致力于钢铁冶金、电力、矿山、建材等行业机械配套设备的设计、研发、制造、销售和服务，公司占地面积18000平方米，厂房8000平方米，前期投资5000万元。联系电话：" & tel & "，传真：" & fax
Const front_Keywords_en = ""
Const front_Description_en = ""
Const area_querystingid = "p"
Const list_querystingid = "lid"
Const page_querystingid = "i"
Const list_p_querystingid = "page"
Const pro_querystingid = "pid"			'产品URL参数
Const page_num = 20
Const img_num = 6
Const list_name = "class.asp"
Const page_name = "page.asp"
Const plist_name = "product_detail.asp"
%>

<%
front_copy = "<script src=""admin/stats.asp"" type=""text/javascript""></script>电话：<span class=""en"">" & tel & "</span>  传真：<span class=""en"">" & fax & "</span> 地址：" & add & " <br /><span class=""en"">&copy; 2010</span> 长沙华隆重型机器制造有限公司"

Function VBsUnEscape(str) 
    dim i,s,c 
    s="" 
    For i=1 to Len(str) 
        c=Mid(str,i,1) 
        If Mid(str,i,2)="%u" and i<=Len(str)-5 Then 
            If IsNumeric("&H" & Mid(str,i+2,4)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+2,4))) 
                i = i+5 
            Else 
                s = s & c 
            End If 
        ElseIf c="%" and i<=Len(str)-2 Then 
            If IsNumeric("&H" & Mid(str,i+1,2)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+1,2))) 
                i = i+2 
            Else 
                s = s & c 
            End If 
        Else 
            s = s & c 
        End If 
    Next 
    VBsUnEscape = s 
End Function 

Function VBsEscape(str) 
    dim i,s,c,a 
    s="" 
    For i=1 to Len(str) 
        c=Mid(str,i,1) 
        a=ASCW(c) 
        If (a>=48 and a<=57) or (a>=65 and a<=90) or (a>=97 and a<=122) Then 
            s = s & c 
        ElseIf InStr("@*_+-./",c)>0 Then 
            s = s & c 
        ElseIf a>0 and a<16 Then 
            s = s & "%0" & Hex(a) 
        ElseIf a>=16 and a<256 Then 
            s = s & "%" & Hex(a) 
        Else 
            s = s & "%u" & Hex(a) 
        End If 
    Next 
    VBsEscape = s 
End Function 

%>