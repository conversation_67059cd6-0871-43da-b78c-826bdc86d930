<HTML>
<HEAD>
<TITLE></TITLE>
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<style>
.menu {BORDER-RIGHT: buttonface 1px solid; BORDER-TOP: buttonface 1px solid; BORDER-LEFT: buttonface 1px solid; BORDER-BOTTOM: buttonface 1px solid}
body {BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; CURSOR: default; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: menu}
</style>
<SCRIPT language=javascript>
function v(e){
	e.bgColor = "#CEE7FF";
	e.style.borderColor = "#00557D";
}
function d(e){
	e.bgColor = "";
	e.style.borderColor = "buttonface";
}

</SCRIPT>
<SCRIPT language=javascript event=onselectstart for=document type=text/javascript>
return false;
</SCRIPT>
<SCRIPT language=javascript event=oncontextmenu for=document>
return false;
</SCRIPT>
</HEAD>
<BODY leftMargin=2 topMargin=2>
<TABLE height=18 border=0 cellPadding=0 cellSpacing=0 style="FONT-SIZE: 9pt">
  <TBODY>
    <TR align=center> 
	  <TD width=10></TD>
      <TD width=60 onmouseover=v(this) class=menu onclick="parent.h_main.location.href='help_main.htm#top'" onmouseout=d(this) vAlign=bottom width="25%">目录(<U>D</U>)</TD>
    </TR>
  </TBODY>
</TABLE>
</BODY>
</HTML>
