<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.Charset = "utf-8" %>
<!--#include file="_config.asp" --><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><%=intro_title%></title>
<meta name="Keywords" content="<%=front_Keywords%>" />
<meta name="Description" content="<%=front_Description%>" />
<link type="application/rss+xml" title="<%=trade_name%>" href="rss.asp?a=01" rel="alternate" />
<link rel="stylesheet" href="layout.css" type="text/css" />
<script type="text/javascript" src="common.js"></script>
<script type="text/javascript" src="Portalclient.js"></script>
</head>
<body id="P00">
<div class="page_wrapper">
	<div class="logo">
		<a href="default.asp" id="logo"><img src="images/hualongzg_logo.jpg" width="165" height="41" alt="hualongzg logo" /></a>
		<div id="language">
			<a href="#" class="en">English</a><a href="default.asp" class="cn">中文</a>
		</div>
       <form method="get" action="#" id="frmsearch">
          <fieldset>
			 <input type="text" name="keywords" id="searchArea" value="关键词"  />
			 <input id="searchGo" type="submit" class="go" value="" />
          </fieldset>
		</form>
	</div>
	<div class="ba"></div>
	<div class="navigator">
		<ul><li><a href="default.asp" class="ac">首　　页</a></li><%=nav_tree("01",0)%></ul>
	</div>
	<dl id="product">
		<dt><h2>产品中心</h2></dt>
		<dd>
			<ul><%
	Dim rs,sql,listr,content
	Set rs = Server.CreateObject("ADODB.Recordset")
	sql = "select top 7 list.id,list.name,list.content2,list.thumb,list.thumbwidth,list.thumbheight,list.img,list.imgwidth,list.imgheight,list.is_top,list.sort_id2 from list inner join area on list.cate_id = area.cate_id where left(area.cate_lcode,4)='0101' and trim(list.thumb) <> '' order by area.cate_lcode asc,sort_id2 desc"
	rs.open sql,MM_conn_STRING,1,1
	While Not rs.eof
	content = cutStr(rs("content2"),182)
	response.write "<li><a class=""proname"" href=""" & plist_name & "?" & pro_querystingid & "=" & rs("id") & """>" & rs("name") & "</a><div><a href=""" & plist_name & "?" & pro_querystingid & "=" & rs("id") & """><img src=""upload/" & rs("img") & """ width=""" & rs("imgwidth") & """ height=""" & rs("imgheight") & """ alt=""" & rs("name") & """ /></a><p>" & content & "</p></div></li>"
	rs.movenext()
	Wend
	rs.close()
	Set rs=nothing			
			%>
			</ul>
		</dd>
	</dl>
	<dl id="news">
		<dt><h2>新闻中心</h2></dt>
		<dd>
			<ul><%=page_list("top 3","list.id=22",3,1,false,0,true,"没有输入内容",18)%></ul>
		</dd>
	</dl>
	<div class="copyright">
		<p><%=front_copy%>［<a href="http://www.miibeian.gov.cn/" title="点击在线验证">湘<span>ICP</span>备<span>10001217</span>号</a>］<a href="rss.asp?a=01" class="rss">XML/RSS</a></p>
	</div>
</div>
</body>
</html>