<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">流量统计系统</h1>
<%
dim stat_style_bg, stat_style_nav, stat_chart_gif
stat_chart_gif = "stat_images/chart" & cstr(chart_style) & ".gif"
select case chart_style
case 0'黄色
stat_style_bg = "#ffffcc"
stat_style_nav = "#FFFF00"
case 1'绿色
stat_style_bg = "#99CC99"
stat_style_nav = "#66CC00"
case 2'红色
stat_style_bg = "#ff9999"
stat_style_nav = "#ff6666"
case 3'橙色
stat_style_bg = "#ffCC00"
stat_style_nav = "#ff9900"
case 4'灰色
stat_style_bg = "#cccccc"
stat_style_nav = "#999999"
case 5'蓝紫色
stat_style_bg = "B8A4CE"
stat_style_nav = "#9c9ace"
case 6'蓝色
stat_style_bg = "#00ccff"
stat_style_nav = "#0099ff"
case 7'紫色
stat_style_bg = "#DEC7FF"
stat_style_nav = "#9999ff"
case 8'桃红红
stat_style_bg = "#ffCCff"
stat_style_nav = "#ff66ff"
case 9'浅红
stat_style_bg = "#ffcccc"
stat_style_nav = "#F9A6BC"
end select%>
<%select case nav_style
case 0%>
<script language="JavaScript" type="text/JavaScript">
<!--
function MM_jumpMenu(targ,selObj,restore){ //v3.0
  eval(targ+".location='"+selObj.options[selObj.selectedIndex].value+"'");
  if (restore) selObj.selectedIndex=0;
}
//-->
</script>
<table width="540" cellspacing="0" align="center" cellpadding="0" border="0" class=table0><tr>
<form><td class=td0 align=left>
  <select style="background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)">
    <option value="#">总述及按时间统计</option>
	<option value="stat_default.asp">总述</option>
    <option value="stat_anhour.asp">时段统计</option>
    <option value="stat_anday.asp">日统计</option>
    <option value="stat_anweek.asp">周统计</option>
    <option value="stat_anmonth.asp">月/年统计</option>
  </select>
</td></form>
<form><td class=td0 align=center>
  <select style="background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)">
    <option value="#">访问者情况统计</option>
    <option value="stat_ancome.asp">来源网址</option>
    <option value="stat_anpage.asp">访问页面</option>
    <option value="stat_anip.asp">访问者IP</option>
    <option value="stat_an_soft.asp">浏览器和操作系统</option>
    <option value="stat_anwhere.asp">地区统计</option><%if session.Contents("master")=true or mlevel>2 then%>
    <option value="stat_anall.asp">详细日志</option><%end if%>
  </select>
</td></form>
<form><td class=td0 align=right>
  <select style="background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)"><%if session.Contents("master")=true or mlevel>3 then%>
    <option value="#" selected>高级功能</option>
    <option value="stat_an_search.asp">自定义统计</option><%end if%>
<%if stat_advance = 0 then%>
<option value="stat_help.asp?id=a01">日志过滤</option> 
<option value="stat_help.asp?id=a02">数据库减肥&nbsp;</option> 
<option value="stat_help.asp?id=a03">IP数据库更新</option> 
<%else%>
<option value="stat_filter.asp">日志过滤</option> 
<option value="stat_subtra.asp">数据库减肥</option> 
<option value="stat_ipnew.asp">IP数据库更新</option> 
<%end if%>
  </select>
</td></form>
</tr></table><br>

<%case 1%>
<table width="540" cellspacing="0" align="center" cellpadding="4" border="0" class=table1 bgcolor=<%=(stat_style_nav)%>>
  <tr height="30">
    <td class=td0><div class=p12>
		<a href="stat_default.asp">*总述</a>　  
		<a href="stat_anhour.asp">*时段统计</a> 
		<a href="stat_anday.asp">*日统计</a> 
		<a href="stat_anweek.asp">*周统计</a> 
		<a href="stat_anmonth.asp">*月/年统计&nbsp;</a> 
		<a href="stat_ancome.asp">*来源网址</a> 
		<a href="stat_anpage.asp">*访问页面</a> 　
		<a href="stat_an_soft.asp">*浏览器和操作系统</a> <br>
		<a href="stat_anip.asp">*访问者IP</a> 
		<a href="stat_anwhere.asp">*地区统计</a> 
		<%if session.Contents("master")=true or mlevel>2 then%><a href="stat_anall.asp">*详细日志&nbsp;</a> <%end if%> 
		<%if session.Contents("master")=true or mlevel>3 then%><a href="stat_an_search.asp">*自定义统计</a> 　 <%end if%>
<%if stat_advance = 0 then%>
<a href="stat_help.asp?id=a01">*日志过滤</a> 
<a href="stat_help.asp?id=a02">*数据库减肥&nbsp;</a> 
<a href="stat_help.asp?id=a03">*IP数据库更新</a> 
<%else%>
<a href="stat_filter.asp">*日志过滤</a> 
<a href="stat_subtra.asp">*数据库减肥</a> 
<a href="stat_ipnew.asp">*IP数据库更新</a> 
<%end if%>
	</div></td>
  </tr>
</table>
<br>

<%case 2%>
<table width="100%" cellpadding=0 cellspacing=0 class=table0>
<tr><td class=td0 width=140 valign=top>
<table width="140" cellspacing="0" align="left" cellpadding="10" border="0" class=table1 bgcolor=<%=(stat_style_nav)%>>
	<tr><td class=td012>
		<a href="stat_default.asp">※ 总述</a><br><br>

		<a href="stat_anhour.asp">※ 时段统计</a><br>
		<a href="stat_anday.asp">※ 日统计</a><br>
		<a href="stat_anweek.asp">※ 周统计</a><br>
		<a href="stat_anmonth.asp">※ 月/年统计</a><br><br>

		<a href="stat_ancome.asp">※ 来源网址</a><br>
		<a href="stat_anpage.asp">※ 访问页面</a><br>
		<a href="stat_anip.asp">※ 访问者IP</a><br>
		<a href="stat_an_soft.asp">※ 浏览器和操作系统</a><br>
		<a href="stat_anwhere.asp">※ 地区统计</a><br>
		<%if session.Contents("master")=true or mlevel>2 then%><a href="stat_anall.asp">※ 详细日志</a><br><%end if%><br>

		<%if session.Contents("master")=true or mlevel>3 then%><a href="stat_an_search.asp">※ 自定义统计</a><br><%end if%>
<%if stat_advance = 0 then%>
<a href="stat_help.asp?id=a01">※ 日志过滤</a><br>
<a href="stat_help.asp?id=a02">※ 数据库减肥</a><br>
<a href="stat_help.asp?id=a03">※ IP数据库更新</a><br>
<%else%>
<a href="stat_filter.asp">※ 日志过滤</a><br>
<a href="stat_subtra.asp">※ 数据库减肥</a><br>
<a href="stat_ipnew.asp">※ IP数据库更新</a><br>
<%end if%>
	</td></tr>
</table>
</td>
<td class=td0 width="100%" valign=top>

<%case 3%>
<script language="JavaScript" type="text/JavaScript">
<!--
function MM_jumpMenu(targ,selObj,restore){ //v3.0
  eval(targ+".location='"+selObj.options[selObj.selectedIndex].value+"'");
  if (restore) selObj.selectedIndex=0;
}
//-->
</script>
<table width="100%" cellpadding=0 cellspacing=0 class=table0>
<tr><td class=td0 width=140 valign=top>
<table width="140" cellspacing="0" align="left" cellpadding="10" border="0" style="border: 1px dashed <%=(back_titlebackcolor)%>">
	<form><tr><td class=td012 align=center>总述及按时间统计<br>
  <select size=5 style="width:124px; background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)">
	<option value="stat_default.asp">总述</option>
    <option value="stat_anhour.asp">时段统计</option>
    <option value="stat_anday.asp">日统计</option>
    <option value="stat_anweek.asp">周统计</option>
    <option value="stat_anmonth.asp">月/年统计</option>
  </select>
</td></tr></form>
<form><tr><td class=td012 align=center>访问者情况统计<br>
  <select size=6 style="width:124px; background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)">
    <option value="stat_ancome.asp">来源网址</option>
    <option value="stat_anpage.asp">访问页面</option>
    <option value="stat_anip.asp">访问者IP</option>
    <option value="stat_an_soft.asp">浏览器和操作系统</option>
    <option value="stat_anwhere.asp">地区统计</option><%if session.Contents("master")=true or mlevel>2 then%>
    <option value="stat_anall.asp">详细日志</option><%end if%>
  </select>
</td></tr></form>
<form><tr><td class=td012 align=center>高级功能<br>
  <select size=4 style="width:124px; background-color: <%=(stat_style_nav)%>" name="menu1" onChange="MM_jumpMenu('parent',this,0)"><%if session.Contents("master")=true or mlevel>3 then%>
    <option value="stat_an_search.asp">自定义统计</option><%end if%>
<%if stat_advance = 0 then%>
<option value="stat_help.asp?id=a01">日志过滤</option> 
<option value="stat_help.asp?id=a02">数据库减肥&nbsp;</option> 
<option value="stat_help.asp?id=a03">IP数据库更新</option> 
<%else%>
<option value="stat_filter.asp">日志过滤</option> 
<option value="stat_subtra.asp">数据库减肥</option> 
<option value="stat_ipnew.asp">IP数据库更新</option> 
<%end if%>
  </select>
</td></tr></form>
</table>
</td>
<td class=td0 width="100%" valign=top>
<%end select%>