<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="_config.asp" -->
<!--#include file="event__config.asp" -->
<!--#include file="../ScriptLibrary/sub_email.asp" -->
<%
'jobs_email_sign 签名
Dim t_action,hs_id
t_action =request.querystring("action")
hs_id = request.querystring("hs_id")
Select Case t_action
Case "initIt"
	call initIt(hs_id)
Case "eventype"
	hs_title = request.querystring("hs_title")
	Call eventype(request.querystring("id"))
Case "add2"
	set rs=server.createobject("adodb.recordset")
	
	'发送EMAIL
	sql = "select top 1 email from admin"
	rs.open sql,MM_conn_STRING,1,1
	sender_mail = rs("email")
	rs.close()
	sql = "select email from jobs_human where h_id=" & hs_id
	rs.open sql,MM_conn_STRING,1,1
	hs_email = rs("email")
	rs.close()
	If request.querystring("send_email") = "true" Then
		description = email_to(sender_mail,back_coname, sender_mail,request.querystring("event_subject"),hs_email,request.querystring("event_content"),back_smtpsever)
	End If

	sql="select * from event where hs_id=" & hs_id
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	rs("hs_id") = hs_id
	rs("category_id") = request.querystring("category_id")
	rs("event_name") = request.querystring("event_name")
	rs("subject") = request.querystring("event_subject")
	rs("event_content") = request.querystring("event_content")
	rs("event_type_id") = request.querystring("event_type_id")
	rs("send_email") = request.querystring("send_email")
	If description <> "" Then rs("description") = description
	rs.update
	rs.close()
	sql = "select this_type,score from jobs_human where h_id=" & hs_id
	rs.open sql,MM_conn_STRING,3,3
	rs("this_type") = request.querystring("event_type_id")
	rs.update
	rs.close()
	set rs=Nothing
call initIt(hs_id)
%>

<%end select
%>

<%'------------------------------
sub initIt(hs_id)	'显示所有的envent
	Dim rs,sql
	set rs=server.createobject("adodb.recordset")
	sql = "select send_email,event_id,event_type_id,event_name,subject,description,event_date,event_person,event_content from event where hs_id=" & hs_id & " and category_id=1 order by event_date desc"
	rs.open sql,MM_conn_STRING,1,1
%>
<table border=1 cellspacing=0 cellpadding=4 width=100%>
<tr bgcolor=<%=back_menubackcolor%>>
<td height=25><div style="float:right;padding:3px;"><a href="javascript:void(null)" onclick="new_event()"><span class=hbottom id=neweventb>新处理</span></a></div>
<div style="padding:3px"><a href="javascript:void(null)" onclick="hide_detail()"><span class=hbottom id="detaillbottom">←隐藏简历</span></a><a href="javascript:void(null)" onclick="hide_more()"><span class=hbottom id="morebottom">隐藏内容</span></a></div></td>
</tr>

<tr><td id="newevent" style="display:none;background-color:#ececec">
<TABLE width="100%" border=1 cellPadding=0 cellSpacing=0 bordercolor="#ececec" style="border-collapse: collapse;border:dotted;border-width:1px" align=center><form name="frm_detail" method=post action="jobs_human_detail_ajax.asp" onsubmit="return validateadd();">
<tr>
	<td width=100><select name="event_name" onchange="eventype()">
	<option>选择处理类型</option><%
	set rs1=server.createobject("adodb.recordset")
	sql = "select event_type_id,name from event_type where category_id = 1 order by sort_id asc"
	rs1.open sql,MM_conn_STRING,1,1
	While Not rs1.eof
	response.write "<option value=" & rs1("event_type_id") & ">" & rs1("name") & "</option>"
	rs1.movenext()
	wend
	rs1.close()
	Set rs1=nothing
	%></select></td>
	<td width=100%><input size=15 type="text" name="event_name_2"  title="补充说明：关于处理类型的补充说明，便于自己查看" style="overflow-x:visible"></td>
	<td width=30 nowrap align=right><span>发信</span></td>
	<td width=30><input type="checkbox" name="email_on" onclick="emailcheck()"></td>
</tr>
<tr id=email0>
	<td align=right>主题</td>
	<td colspan=3><input type="text" name="subject" style="width:100%;"></td>
</tr>
<tr id=email1>
	<td align=right valign=top>内容</td>
	<td colspan=3><textarea name="event_content" style="width:100%;height:80px;overflow-y:visible"></textarea></td>
</tr>
<tr><td colspan=4 align=right><input name="sub" type="submit" value="确 定" style="width:200px;"></td></tr></form>
</table>
</td></tr>

<%While Not rs.eof
send_email=""
event_name = ""
subject = ""
event_content = ""
description = ""
If rs("send_email") = True And (Trim(rs("description")) = "" Or IsNull(rs("description"))) Then send_email = "<span class=emailsign title='已经发送邮件'>email</span>"
If Trim(rs("event_name")) <> "" Then event_name = HTMLEncode(rs("event_name"))
If Trim(rs("subject")) <> "" Then subject = HTMLEncode(rs("subject"))
If Trim(rs("event_content")) <> "" Then event_content = Replace(HTMLEncode(rs("event_content")),vbcrlf,"<br>")
If Trim(rs("description")) <> "" Then 
	description = "<div class=evente>" & HTMLEncode(rs("description")) & "</div>"
End if	
%>
<tr><td width=100%>
<div class=eventt><div style="float:right"><%=rs("event_date")%></div><%=event_name%><%=send_email%></div>
<div class=eventc><div style="padding-bottom:8px;height:auto;">主题：<%=subject%> <p><%=event_content%></p></div>
<%=description%>
</td></tr>
<%rs.movenext()
wend%>
<%If rs.recordcount = 0 then%>
<tr>
	<td height=100 valign=top>对于这位应聘者，您还未作任何处理，点击右上角的“新处理”按钮，开始处理；点击左上角的按钮，隐藏或者显示简历信息。请注意：请输入准确信息，已经输入的处理信息不可修改与删除；并及时处理相关事务，会提高您的工作效率和乐趣。</td>
</tr>
<%End if%>
<tr><td height=25 align=right width=100%><span id=mback><a href='javascript:history.go(-1)'><span class=hbottom>&gt;&gt; 返回</span></a></span></td></tr>
</table>
<%
	rs.close()
	Set rs=nothing
End Sub

'------------------------------
Sub eventype(id)	'流程类型显示
	Dim rs,sql,select_str,sub_str,style_str,check_str
	set rs=server.createobject("adodb.recordset")
	sql = "select event_type_id,sort_id,name,email_on,email_content,email_subject from event_type where event_type_id=" & id & " and category_id=1"
	rs.open sql,MM_conn_STRING,1,1
	If rs("email_on") = True Then
		style_str = " style='background-color:" & back_menubackcolor & "'"
		sub_str = "发 信"
		check_str = " checked"
		email_content = hs_title & "，您好！" & vbcrlf & vbcrlf & rs("email_content") & jobs_email_sign
		Else
		style_str = ""
		sub_str = "确 定"
		check_str = ""
		email_content = rs("email_content")
	End if
%>
<TABLE width="100%" border=1 cellPadding=0 cellSpacing=0 bordercolor="#ececec" style="border-collapse: collapse;border:dotted;border-width:1px" align=center><form name="frm_detail" method=post action="jobs_human_detail_ajax.asp" onsubmit="return validateadd();">
<tr>
	<td width=100><select name="event_name" onchange="eventype()">
	<option>选择处理类型</option><%
	set rs1=server.createobject("adodb.recordset")
	sql = "select event_type_id,name from event_type where category_id = 1 order by sort_id asc"
	rs1.open sql,MM_conn_STRING,1,1
	While Not rs1.eof
	select_str = ""
	If rs1("event_type_id") = rs("event_type_id") Then select_str = " selected style='color:#ff0000'"
	response.write "<option value=" & rs1("event_type_id") & select_str & ">" & rs1("name") & "</option>"
	rs1.movenext()
	wend
	rs1.close()
	Set rs1=nothing
	%></select></td>
	<td width=100%><input size=15 type="text" name="event_name_2"  title="补充说明：关于处理类型的补充说明，便于自己查看" style="overflow-x:visible"></td>
	<td style="width:30px" nowrap align=right><span>发信</span></td>
	<td style="width:30px"><input type="checkbox" name="email_on" onclick="emailcheck()"<%=check_str%>></td>
</tr>
<tr id=email0<%=style_str%>>
	<td align=right>主题</td>
	<td colspan=3><input type="text" name="subject" style="width:100%" value="<%=rs("email_subject")%>"></td>
</tr>
<tr id=email1<%=style_str%>>
	<td align=right valign=top>内容</td>
	<td colspan=3><textarea name="event_content" style="width:100%;height:80px;overflow-y:visible"><%=email_content%></textarea></td>
</tr>
<tr><td colspan=4 align=right><input name="sub" type="submit" value="<%=sub_str%>" style="width:200px;"></td></tr></form>
</table>
<%
	rs.close()
	Set rs=nothing
end sub%>