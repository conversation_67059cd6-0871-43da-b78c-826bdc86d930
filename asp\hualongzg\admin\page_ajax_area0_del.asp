<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file="area_config.asp" -->
<%
Dim pageid
pageid = request.querystring("pageid")

call del_page_img(page_id,"upload")

sub del_page_img(pageids,folder)
	'删除页面里的文件，参数为字符串pageid组,父目录名称（不带\）
	dim rs,sql,imgs,i
	imgs = ""
	set rs=server.createobject("adodb.recordset") 
	sql="select img, imgsmall,media from page WHERE pageid="& pageid
	rs.open sql,MM_conn_STRING,1,1 
	while not rs.eof
		for i=0 to 2
			if rs(i) <> "" AND not isnull(rs(i)) then
				if imgs <> "" then
				imgs = imgs & "|" & rs(i)
				else
				imgs = rs(i)
				end if
			end if
		next
		rs.movenext()
	wend
	rs.close()
	set rs=nothing

	if imgs <> "" then
		dim File,fdir,ImagePath
		Set File = CreateObject("Scripting.FileSystemObject")
		fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
		set fdir = File.getfile(fdir)
		set fdir = fdir.parentfolder
		set fdir = fdir.parentfolder								'获取父父目录

		dim imgs_str
		imgs_str = split(imgs,"|")
		for i = lbound(imgs_str) to ubound(imgs_str)
			ImagePath = fdir & "\" & folder & "\" & imgs_str(i)
			if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
		next
	end if'删除旧图或者媒体文件
end sub

sql="DELETE FROM page WHERE pageid = "&pageid
set Command1 = Server.CreateObject("ADODB.Command")
Command1.ActiveConnection = MM_conn_STRING
Command1.CommandText = sql
Command1.Execute()
%>
