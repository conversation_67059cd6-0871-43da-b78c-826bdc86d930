<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("../admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->

<%
'*** File Upload to: ../img, Extensions: "GIF,JPG,JPEG,BMP,PNG", Form: form1, Redirect: "", "file", "", "uniq"
'*** Pure ASP File Upload Modify Version by xPilot-----------------------------------------------------
' Copyright 2000 (c) <PERSON>
'
' Sc<PERSON>t partially based on code from <PERSON> Collignon 
'              (http://www.asptoday.com/articles/20000316.htm)
'
' New features from GP:
'  * Fast file save with ADO 2.5 stream object
'  * new wrapper functions, extra error checking
'  * UltraDev Server Behavior extension
'
' Copyright 2001-2002 (c) Modify by xPilot
' *** Date: 12/15/2001 ***
' *** 支持所有双字节文件名，而且修复了原函数中遇到空格也会自动截断文件名的错误！ ***
' *** 保证百分百以原文件名保存上传文件！***
' *** Welcome to visite pilothome.yeah.net <NAME_EMAIL> to me！***
'
' Version: 2.0.1 Beta for GB2312,BIG5,Japan,Korea ...
'------------------------------------------------------------------------------
Sub BuildUploadRequest(RequestBin,UploadDirectory,storeType,sizeLimit,nameConflict)
  'Get the boundary
  PosBeg = 1
  PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
  if PosEnd = 0 then
    Response.Write "<b>Form was submitted with no ENCTYPE=""multipart/form-data""</b><br>"
    Response.Write "Please correct the form attributes and try again."
    Response.End
  end if
  'Check ADO Version
	set checkADOConn = Server.CreateObject("ADODB.Connection")
	adoVersion = CSng(checkADOConn.Version)
	set checkADOConn = Nothing
	if adoVersion < 2.5 then
    Response.Write "<b>You don't have ADO 2.5 installed on the server.</b><br>"
    Response.Write "The File Upload extension needs ADO 2.5 or greater to run properly.<br>"
    Response.Write "You can download the latest MDAC (ADO is included) from <a href=""www.microsoft.com/data"">www.microsoft.com/data</a><br>"
    Response.End
	end if		
  'Check content length if needed
	Length = CLng(Request.ServerVariables("HTTP_Content_Length")) 'Get Content-Length header
	If "" & sizeLimit <> "" Then
    sizeLimit = CLng(sizeLimit)
    If Length > sizeLimit Then
      Request.BinaryRead (Length)
      Response.Write "Upload size " & FormatNumber(Length, 0) & "B exceeds limit of " & FormatNumber(sizeLimit, 0) & "B"
      Response.End
    End If
  End If
  boundary = MidB(RequestBin,PosBeg,PosEnd-PosBeg)
  boundaryPos = InstrB(1,RequestBin,boundary)
  'Get all data inside the boundaries
  Do until (boundaryPos=InstrB(RequestBin,boundary & getByteString("--")))
    'Members variable of objects are put in a dictionary object
    Dim UploadControl
    Set UploadControl = CreateObject("Scripting.Dictionary")
    'Get an object name
    Pos = InstrB(BoundaryPos,RequestBin,getByteString("Content-Disposition"))
    Pos = InstrB(Pos,RequestBin,getByteString("name="))
    PosBeg = Pos+6
    PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(34)))
    Name = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
    PosFile = InstrB(BoundaryPos,RequestBin,getByteString("filename="))
    PosBound = InstrB(PosEnd,RequestBin,boundary)
    'Test if object is of file type
    If  PosFile<>0 AND (PosFile<PosBound) Then
      'Get Filename, content-type and content of file
      PosBeg = PosFile + 10
      PosEnd =  InstrB(PosBeg,RequestBin,getByteString(chr(34)))
      FileName = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      FileName = Mid(FileName,InStrRev(FileName,"\")+1)
      'Add filename to dictionary object
      UploadControl.Add "FileName", FileName
      Pos = InstrB(PosEnd,RequestBin,getByteString("Content-Type:"))
      PosBeg = Pos+14
      PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
      'Add content-type to dictionary object
      ContentType = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      UploadControl.Add "ContentType",ContentType
      'Get content of object
      PosBeg = PosEnd+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = FileName
      ValueBeg = PosBeg-1
      ValueLen = PosEnd-Posbeg
    Else
      'Get content of object
      Pos = InstrB(Pos,RequestBin,getByteString(chr(13)))
      PosBeg = Pos+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      ValueBeg = 0
      ValueEnd = 0
    End If
    'Add content to dictionary object
    UploadControl.Add "Value" , Value	
    UploadControl.Add "ValueBeg" , ValueBeg
    UploadControl.Add "ValueLen" , ValueLen	
    'Add dictionary object to main dictionary
    UploadRequest.Add name, UploadControl	
    'Loop to next object
    BoundaryPos=InstrB(BoundaryPos+LenB(boundary),RequestBin,boundary)
  Loop

  GP_keys = UploadRequest.Keys
  for GP_i = 0 to UploadRequest.Count - 1
    GP_curKey = GP_keys(GP_i)
    'Save all uploaded files
    if UploadRequest.Item(GP_curKey).Item("FileName") <> "" then
      GP_value = UploadRequest.Item(GP_curKey).Item("Value")
      GP_valueBeg = UploadRequest.Item(GP_curKey).Item("ValueBeg")
      GP_valueLen = UploadRequest.Item(GP_curKey).Item("ValueLen")

      if GP_valueLen = 0 then
        Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
        Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
        Response.Write "File does not exists or is empty.<br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
	  	  response.End
	    end if
      
      'Create a Stream instance
      Dim GP_strm1, GP_strm2
      Set GP_strm1 = Server.CreateObject("ADODB.Stream")
      Set GP_strm2 = Server.CreateObject("ADODB.Stream")
      
      'Open the stream
      GP_strm1.Open
      GP_strm1.Type = 1 'Binary
      GP_strm2.Open
      GP_strm2.Type = 1 'Binary
        
      GP_strm1.Write RequestBin
      GP_strm1.Position = GP_ValueBeg
      GP_strm1.CopyTo GP_strm2,GP_ValueLen
    
      'Create and Write to a File
      GP_curPath = Request.ServerVariables("PATH_INFO")
      GP_curPath = Trim(Mid(GP_curPath,1,InStrRev(GP_curPath,"/")) & UploadDirectory)
      if Mid(GP_curPath,Len(GP_curPath),1)  <> "/" then
        GP_curPath = GP_curPath & "/"
      end if 
      GP_CurFileName = UploadRequest.Item(GP_curKey).Item("FileName")
      GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & GP_CurFileName
      'Check if the file alreadu exist
      GP_FileExist = false
      Set fso = CreateObject("Scripting.FileSystemObject")
      If (fso.FileExists(GP_FullFileName)) Then
        GP_FileExist = true
      End If      
      if nameConflict = "error" and GP_FileExist then
        Response.Write "<B>File already exists!</B><br><br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
				GP_strm1.Close
				GP_strm2.Close
	  	  response.End
      end if
      if ((nameConflict = "over" or nameConflict = "uniq") and GP_FileExist) or (NOT GP_FileExist) then
        if nameConflict = "uniq" and GP_FileExist then
          Begin_Name_Num = 0
          while GP_FileExist    
            Begin_Name_Num = Begin_Name_Num + 1
            GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
            GP_FileExist = fso.FileExists(GP_FullFileName)
          wend  
          UploadRequest.Item(GP_curKey).Item("FileName") = fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
					UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
        end if
        on error resume next
        GP_strm2.SaveToFile GP_FullFileName,2
        if err then
          Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
          Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
          Response.Write "Maybe the destination directory does not exist, or you don't have write permission.<br>"
          Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
    		  err.clear
  				GP_strm1.Close
  				GP_strm2.Close
  	  	  response.End
  	    end if
  			GP_strm1.Close
  			GP_strm2.Close
  			if storeType = "path" then
  				UploadRequest.Item(GP_curKey).Item("Value") = GP_curPath & UploadRequest.Item(GP_curKey).Item("Value")
  			end if
        on error goto 0
      end if
    end if
  next

End Sub

'把普通字符串转成二进制字符串函数
Function getByteString(StringStr)
    getByteString=""
  For i = 1 To Len(StringStr) 
    XP_varchar = mid(StringStr,i,1)
    XP_varasc = Asc(XP_varchar) 
    If XP_varasc < 0 Then 
       XP_varasc = XP_varasc + 65535 
    End If 

    If XP_varasc > 255 Then 
       XP_varlow = Left(Hex(Asc(XP_varchar)),2) 
       XP_varhigh = right(Hex(Asc(XP_varchar)),2) 
       getByteString = getByteString & chrB("&H" & XP_varlow) & chrB("&H" & XP_varhigh) 
    Else 
       getByteString = getByteString & chrB(AscB(XP_varchar)) 
    End If 
  Next 
End Function

'把二进制字符串转换成普通字符串函数 
Function getString(StringBin)
   getString =""
   Dim XP_varlen,XP_vargetstr,XP_string,XP_skip
   XP_skip = 0 
   XP_string = "" 
 If Not IsNull(StringBin) Then 
      XP_varlen = LenB(StringBin) 
    For i = 1 To XP_varlen 
      If XP_skip = 0 Then
         XP_vargetstr = MidB(StringBin,i,1) 
         If AscB(XP_vargetstr) > 127 Then 
           XP_string = XP_string & Chr(AscW(MidB(StringBin,i+1,1) & XP_vargetstr)) 
           XP_skip = 1 
         Else 
           XP_string = XP_string & Chr(AscB(XP_vargetstr)) 
         End If 
      Else 
      XP_skip = 0
   End If 
    Next 
 End If 
      getString = XP_string 
End Function 

Function UploadFormRequest(name)
  on error resume next
  if UploadRequest.Item(name) then
    UploadFormRequest = UploadRequest.Item(name).Item("Value")
  end if  
End Function

'Process the upload
UploadQueryString = Replace(Request.QueryString,"GP_upload=true","")
if mid(UploadQueryString,1,1) = "&" then
	UploadQueryString = Mid(UploadQueryString,2)
end if

GP_uploadAction = CStr(Request.ServerVariables("URL")) & "?GP_upload=true"
If (Request.QueryString <> "") Then  
  if UploadQueryString <> "" then
	  GP_uploadAction = GP_uploadAction & "&" & UploadQueryString
  end if 
End If

If (CStr(Request.QueryString("GP_upload")) <> "") Then
  GP_redirectPage = ""
  If (GP_redirectPage = "") Then
    GP_redirectPage = CStr(Request.ServerVariables("URL"))
  end if
    
  RequestBin = Request.BinaryRead(Request.TotalBytes)
  Dim UploadRequest
  Set UploadRequest = CreateObject("Scripting.Dictionary")  

  BuildUploadRequest RequestBin, "../img", "file", "", "uniq"
  
  '*** GP NO REDIRECT
end if  
if UploadQueryString <> "" then
  UploadQueryString = UploadQueryString & "&GP_upload=true"
else  
  UploadQueryString = "GP_upload=true"
end if  
%>
<%
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction = CStr(Request.ServerVariables("URL")) 'MM_editAction = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction = MM_editAction & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Insert Record: set variables

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "link_exchang"
  MM_editRedirectUrl = "links2+add.asp?action=ok"
  MM_fieldsStr  = "img|value|imgwidth|value|imgheight|value|site_name|value|site_url|value|site_decript|value|site_contact|value|site_email|value|agree|value"
  MM_columnsStr = "img|',none,''|imgwidth|none,none,NULL|imgheight|none,none,NULL|site_name|',none,''|site_url|',none,''|site_decript|',none,''|site_contact|',none,''|site_email|',none,''|agree|none,none,NULL"


  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
'  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
'    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
'      MM_editRedirectUrl = MM_editRedirectUrl & "?" & UploadQueryString
'    Else
'      MM_editRedirectUrl = MM_editRedirectUrl & "&" & UploadQueryString
'    End If
'  End If

End If
%>

<%'以下代码获取管理员信箱，放入总控制台好些，以免冲突
set rsADMINMAIL = Server.CreateObject("ADODB.Recordset")
rsADMINMAIL.ActiveConnection = MM_conn_STRING
rsADMINMAIL.Source = "SELECT email FROM admin"
rsADMINMAIL.CursorType = 0
rsADMINMAIL.CursorLocation = 2
rsADMINMAIL.LockType = 3
rsADMINMAIL.Open()
admin_mail = rsADMINMAIL("email")
rsADMINMAIL.close()
set rsADMINMAIL = nothing
%>
<% if UploadFormRequest("action") ="ok" then'这个通过post方式提交的action与get方式提交的作用不同
Sender_mail = admin_mail
Sender =  "网站www." & back_site & "的管理员"
replyto = admin_mail
Subject = "友情链接：" & UploadFormRequest("site_name")
mail_body = "您好，网站www." & back_site & "欲与贵站" & UploadFormRequest("site_name") & "(" & UploadFormRequest("site_url") & "）友情链接" & vbCrLf & vbCrLf &_
"本站名称：" & back_coname & vbCrLf &_
"网　　址：http://www." & back_site & vbCrLf &_
"联系信箱：" & admin_mail & vbCrLf &_
"请到本站查看我们为贵站作好的友情链接，并请按照我站友情链接栏目的要求添加我站的链接。" & vbCrLf & vbCrLf & "谢谢！" & vbCrLf & vbCrLf & "网站www." & back_site & "敬上！"
email_to sender_mail,sender, replyto,subject,UploadFormRequest("site_email"),mail_body,back_smtpsever
end if
%> 
<%
' *** Insert Record: construct a sql insert statement and execute it

If (CStr(UploadFormRequest("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End if
    MM_tableValues = MM_tableValues & MM_columns(i)
    MM_dbValues = MM_dbValues & FormVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
sub email_to(sender_mail,sender, replyto,subject,recipient,mail_body,smtp_server)				
Set JMail = Server.CreateObject("JMail.SMTPMail") 
JMail.Priority = 1				'1 - highest priority (Urgent)  3 - normal  5 - lowest 
JMail.Sender = sender_mail		'发送者email
JMail.SenderName = sender		'发送人
if replyto = "" then
	replyto = sender_mail
	else
	JMail.replyto = replyto		'回信人，一般与sernder_mail相同
end if
JMail.Subject = subject
JMail.AddRecipient recipient	'收信者信箱
JMail.ServerAddress = smtp_server

JMail.Charset = "utf-8"
JMail.Body = mail_body

On Error Resume Next
JMail.Execute

'错误处理
If Err.Number <> 0 Then
	set jmail= nothing
	On Error GoTo 0
response.write("本程序自动发送邮件通知相关人。但是程序执行时，发送邮件时出错。<br>可能原因如下：<br>1）收件人信箱错误；<br>2）收件人的邮件服务器故障；<br>3）收件人信箱的域名出错。<br><br><h3>请确定收件人信箱有效，或与网管及森捷信息技术公司联系帮助解决！</h3><a href=""javascript:history.go(-1)"">返回</a>")
	response.end
End If 

set jmail= nothing
End sub
%>
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">友情链接列表</h1>
<a href="links2.asp">← 返回</a><br><br>
<font class=p12 color=ff0000>所谓主动链接指主动在友情链接系统里增加一个网站的链接，增加后系统会自动发email给对方网站的管理员提示。
</font>
<% if request.querystring("action") <> "ok"  then%>
<script language="JavaScript">
<!--

function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} }

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(gif|jpg|jpeg)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('上传文件太小!\n应大于 ' + img.minWidth + ' x ' + img.minHeight); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('上传文件太大!\n不超过 ' + img.maxWidth + ' x ' + img.maxHeight); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('上传文件太大!\n不超过 ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}
//-->
</script>
<br>
						 <form method="POST" ENCTYPE="multipart/form-data" action="<%=MM_editAction%>" name="form_links2" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG',false,50,'','','88','31','imgwidth','imgheight');return document.MM_returnValue;">
							<table align="center" width=460 cellpadding=1 border=0 style="border-collapse:collapse">
							 <tr> 
								<td colspan=2>&nbsp;<b>网站信息</b>（关于对方网站的信息）</td>
							 </tr>
							 <tr> 
								<td align="right" width=60 nowrap>网站名称</td>
								<td> 
								 		<input type="text" name="site_name" class=i400>
								</td>
							 </tr>
							 <tr> 
								<td align="right">网　　址</td>
								<td> 
								 <input type="text" name="site_url" class=i400 value="http://">
								</td>
							 </tr>
							 <tr valign=top> 
								<td align="right">网站图标</td>
								<td class=p12> 
								 <input type="file" name="img" class=i400 onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,50,'','','88','31','imgwidth','imgheight')">
								 <input type=hidden name="imgwidth">
								 <input type=hidden name="imgheight">图标为GIF或者JPEG格式，大小88×31（像素）
								</td>
							 </tr>
							 <tr> 
								<td align="right" valign=top>网站简介</td>
								<td> 
								 <textarea type="text" name="site_decript" cols="60" rows="6" class=i400>请勿超过120字</textarea>
								</td>
							 </tr>
								<td colspan=2>&nbsp;<b>联系信息</b>（对方的联系信息）</td>
							 </tr>
							 <tr> 
								<td align="right">联 系 人</td>
								<td> 
								 <input type="text" name="site_contact" value="网管先生" class=i150>
								</td>
							 </tr>
							 <tr> 
								<td align="right">电子信箱</td>
								<td> 
								 <input type="text" name="site_email" class=i400>
								</td>
							 </tr>
							 <tr> 
								<td nowrap align="right">&nbsp;</td>
								<td> 
								 <input type="submit" value="主动链接" onclick="return noerror(form_links2)">
								</td>
							 </tr>
							</table><input type=hidden name="agree" value=1>
							<input type="hidden" name="MM_insert" value="true">
					  <input type="hidden" name="action" value="ok">
						 </form>
<SCRIPT language=javascript>

function chklogin(form_links2)
{
    if (document.form_links2.site_name.value == "")
		{
		alert("请输入主动链接的对方网站名称");
		document.form_links2.site_name.focus();
		return false;
		}
    if (document.form_links2.site_url.value == "" || document.form_links2.site_url.value == "http://")
		{
		alert("请输入对方网站的网址");
		document.form_links2.site_url.focus();
		return false;
		}
    if ((document.form_links2.site_decript.value == "请勿超过120字" || document.form_links2.site_decript.value == "") && document.form_links2.img.value == "" )
		{
		alert("请输入网站简介或者网站图标中任意一项");
		document.form_links2.site_decript.focus();
		return false;
		}
    if (document.form_links2.site_contact.value == "")
		{
		alert("联系人呢？");
		document.form_links2.site_contact.focus();
		return false;
		}
	if (form_links2.site_email.value==""){
		alert("请输入对方网管真实的电子信箱，因为提交后系统自动发提醒email给对方");
		form_links2.site_email.focus();
		return false;
	}
	return true;
}

function chkmail(aa, graf)
{
   var pos = 0;
   var num = -1;
   var i = -1;
   var email = new Array()
   
   while (pos != -1)
   {
      pos = graf.indexOf(";",i+1);
      num += 1;
      if (pos == -1) { email[num] = graf.substring(i+1,graf.length); }
      else { email[num] = graf.substring(i+1,pos); }
      i = pos;
   }
   for ( i = 0 ; i <= num ; i++ )
   {
     if (email[i].length > 0)
     {
       l=email[i].indexOf("@");
       j=email[i].indexOf(".",l);
       k=email[i].indexOf(",");
       kk=email[i].indexOf(" ");
       jj=email[i].lastIndexOf(".") + 1;
       ll=email[i].indexOf(":");
       mm=email[i].indexOf("(");
       nn=email[i].indexOf(")");
       oo=email[i].indexOf("");
       len=email[i].length;

       if ((l <= 0) || (j <= (1+1)) || (k != -1) || (kk != -1) || (len-jj < 2) || (len-jj > 3) || (ll != -1) || (mm != -1 ) || (nn != -1) || (oo != -1))
       {
       	   if ( aa == "" ) { alert("输入的 email 地址不正确"); }
       	   else { alert("输入的 email 地址不正确"); }
           return false;
       }
     }
   }
   return true;
}

function noerror(form_links2)
{
    if ( !chkmail('',form_links2.site_email.value)){ return( false ) ; }
    if ( !chklogin(form_links2)){ return( false ) ; }
	return confirm('填写无误，确定提交？')
    return( true ) ;
}

//-->
</SCRIPT>

<% elseif request.querystring("action") = "ok" then 
response.redirect("links2.asp")
end if%>
<!--#include file ="_bottom.asp"-->