<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%'本页面管理 pro_trademark 表
Dim MM_editAction
Dim MM_editAction_0
Dim MM_abortEdit
Dim MM_editQuery
Dim MM_editCmd

Dim MM_editConnection
Dim MM_editTable
Dim MM_editRedirectUrl
Dim MM_editColumn
Dim MM_recordId

Dim MM_fieldsStr
Dim MM_columnsStr
Dim MM_fields
Dim MM_columns
Dim MM_typeArray
Dim MM_formVal
Dim MM_delim
Dim MM_altVal
Dim MM_emptyVal
Dim MM_i

MM_editAction_0 = CStr(Request.ServerVariables("SCRIPT_NAME"))
MM_editAction = CStr(Request.ServerVariables("SCRIPT_NAME"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>


<%
Dim rsMODid__MMColParam
rsMODid__MMColParam = "1"
If (Request.QueryString("id") <> "") Then 
  rsMODid__MMColParam = Request.QueryString("id")
End If
rsMODPAGE4__MMColParam = rsMODid__MMColParam
%>
<%
Dim rsMODid
Dim rsMODid_numRows

Set rsMODid = Server.CreateObject("ADODB.Recordset")
rsMODid.ActiveConnection = MM_conn_STRING
rsMODid.Source = "SELECT id, name FROM pro_trademark WHERE id = " + Replace(rsMODid__MMColParam, "'", "''") + ""
rsMODid.CursorType = 0
rsMODid.CursorLocation = 2
rsMODid.LockType = 1
rsMODid.Open()

rsMODid_numRows = 0
%>
<%

set rsMODPAGE4 = Server.CreateObject("ADODB.Recordset")
rsMODPAGE4.ActiveConnection = MM_conn_STRING
rsMODPAGE4.Source = "SELECT * FROM pro_trademark WHERE id = " & rsMODPAGE4__MMColParam & ""
rsMODPAGE4.CursorType = 0
rsMODPAGE4.CursorLocation = 2
rsMODPAGE4.LockType = 3
rsMODPAGE4.Open()

%>
<%
'*** File Upload to: ../img, Extensions: "GIF,JPG,JPEG", Form: form2, Redirect: "", "file", "500000", "uniq"
'*** Pure ASP File Upload Modify Version by xPilot-----------------------------------------------------
' Copyright 2000 (c) George Petrov
'
' Script partially based on code from Philippe Collignon 
'              (http://www.asptoday.com/articles/20000316.htm)
'
' New features from GP:
'  * Fast file save with ADO 2.5 stream object
'  * new wrapper functions, extra error checking
'  * UltraDev Server Behavior extension
'
' Copyright 2001-2002 (c) Modify by xPilot
' *** Date: 12/15/2001 ***
' *** 支持所有双字节文件名，而且修复了原函数中遇到空格也会自动截断文件名的错误！ ***
' *** 保证百分百以原文件名保存上传文件！***
' *** Welcome to visite pilothome.yeah.net <NAME_EMAIL> to me！***
'
' Version: 2.0.1 Beta for GB2312,BIG5,Japan,Korea ...
'------------------------------------------------------------------------------
Sub BuildUploadRequest(RequestBin,UploadDirectory,storeType,sizeLimit,nameConflict)
  'Get the boundary
  PosBeg = 1
  PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
  if PosEnd = 0 then
    Response.Write "<b>Form was submitted with no ENCTYPE=""multipart/form-data""</b><br>"
    Response.Write "Please correct the form attributes and try again."
    Response.End
  end if
  'Check ADO Version
	set checkADOConn = Server.CreateObject("ADODB.Connection")
	adoVersion = CSng(checkADOConn.Version)
	set checkADOConn = Nothing
	if adoVersion < 2.5 then
    Response.Write "<b>You don't have ADO 2.5 installed on the server.</b><br>"
    Response.Write "The File Upload extension needs ADO 2.5 or greater to run properly.<br>"
    Response.Write "You can download the latest MDAC (ADO is included) from <a href=""www.microsoft.com/data"">www.microsoft.com/data</a><br>"
    Response.End
	end if		
  'Check content length if needed
	Length = CLng(Request.ServerVariables("HTTP_Content_Length")) 'Get Content-Length header
	If "" & sizeLimit <> "" Then
    sizeLimit = CLng(sizeLimit)
    If Length > sizeLimit Then
      Request.BinaryRead (Length)
      Response.Write "Upload size " & FormatNumber(Length, 0) & "B exceeds limit of " & FormatNumber(sizeLimit, 0) & "B"
      Response.End
    End If
  End If
  boundary = MidB(RequestBin,PosBeg,PosEnd-PosBeg)
  boundaryPos = InstrB(1,RequestBin,boundary)
  'Get all data inside the boundaries
  Do until (boundaryPos=InstrB(RequestBin,boundary & getByteString("--")))
    'Members variable of objects are put in a dictionary object
    Dim UploadControl
    Set UploadControl = CreateObject("Scripting.Dictionary")
    'Get an object name
    Pos = InstrB(BoundaryPos,RequestBin,getByteString("Content-Disposition"))
    Pos = InstrB(Pos,RequestBin,getByteString("name="))
    PosBeg = Pos+6
    PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(34)))
    Name = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
    PosFile = InstrB(BoundaryPos,RequestBin,getByteString("filename="))
    PosBound = InstrB(PosEnd,RequestBin,boundary)
    'Test if object is of file type
    If  PosFile<>0 AND (PosFile<PosBound) Then
      'Get Filename, content-type and content of file
      PosBeg = PosFile + 10
      PosEnd =  InstrB(PosBeg,RequestBin,getByteString(chr(34)))
      FileName = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      FileName = Mid(FileName,InStrRev(FileName,"\")+1)
      'Add filename to dictionary object
      UploadControl.Add "FileName", FileName
      Pos = InstrB(PosEnd,RequestBin,getByteString("Content-Type:"))
      PosBeg = Pos+14
      PosEnd = InstrB(PosBeg,RequestBin,getByteString(chr(13)))
      'Add content-type to dictionary object
      ContentType = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      UploadControl.Add "ContentType",ContentType
      'Get content of object
      PosBeg = PosEnd+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = FileName
      ValueBeg = PosBeg-1
      ValueLen = PosEnd-Posbeg
    Else
      'Get content of object
      Pos = InstrB(Pos,RequestBin,getByteString(chr(13)))
      PosBeg = Pos+4
      PosEnd = InstrB(PosBeg,RequestBin,boundary)-2
      Value = getString(MidB(RequestBin,PosBeg,PosEnd-PosBeg))
      ValueBeg = 0
      ValueEnd = 0
    End If
    'Add content to dictionary object
    UploadControl.Add "Value" , Value	
    UploadControl.Add "ValueBeg" , ValueBeg
    UploadControl.Add "ValueLen" , ValueLen	
    'Add dictionary object to main dictionary
    UploadRequest.Add name, UploadControl	
    'Loop to next object
    BoundaryPos=InstrB(BoundaryPos+LenB(boundary),RequestBin,boundary)
  Loop

  GP_keys = UploadRequest.Keys
  for GP_i = 0 to UploadRequest.Count - 1
    GP_curKey = GP_keys(GP_i)
    'Save all uploaded files
    if UploadRequest.Item(GP_curKey).Item("FileName") <> "" then
      GP_value = UploadRequest.Item(GP_curKey).Item("Value")
      GP_valueBeg = UploadRequest.Item(GP_curKey).Item("ValueBeg")
      GP_valueLen = UploadRequest.Item(GP_curKey).Item("ValueLen")

      if GP_valueLen = 0 then
        Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
        Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
        Response.Write "File does not exists or is empty.<br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
	  	  response.End
	    end if
      
      'Create a Stream instance
      Dim GP_strm1, GP_strm2
      Set GP_strm1 = Server.CreateObject("ADODB.Stream")
      Set GP_strm2 = Server.CreateObject("ADODB.Stream")
      
      'Open the stream
      GP_strm1.Open
      GP_strm1.Type = 1 'Binary
      GP_strm2.Open
      GP_strm2.Type = 1 'Binary
        
      GP_strm1.Write RequestBin
      GP_strm1.Position = GP_ValueBeg
      GP_strm1.CopyTo GP_strm2,GP_ValueLen
    
      'Create and Write to a File
      GP_curPath = Request.ServerVariables("PATH_INFO")
      GP_curPath = Trim(Mid(GP_curPath,1,InStrRev(GP_curPath,"/")) & UploadDirectory)
      if Mid(GP_curPath,Len(GP_curPath),1)  <> "/" then
        GP_curPath = GP_curPath & "/"
      end if 
      GP_CurFileName = UploadRequest.Item(GP_curKey).Item("FileName")
      GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & GP_CurFileName
      'Check if the file alreadu exist
      GP_FileExist = false
      Set fso = CreateObject("Scripting.FileSystemObject")
      If (fso.FileExists(GP_FullFileName)) Then
        GP_FileExist = true
      End If      
      if nameConflict = "error" and GP_FileExist then
        Response.Write "<B>File already exists!</B><br><br>"
        Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
				GP_strm1.Close
				GP_strm2.Close
	  	  response.End
      end if
      if ((nameConflict = "over" or nameConflict = "uniq") and GP_FileExist) or (NOT GP_FileExist) then
        if nameConflict = "uniq" and GP_FileExist then
          Begin_Name_Num = 0
          while GP_FileExist    
            Begin_Name_Num = Begin_Name_Num + 1
            GP_FullFileName = Trim(Server.mappath(GP_curPath))& "\" & fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
            GP_FileExist = fso.FileExists(GP_FullFileName)
          wend  
          UploadRequest.Item(GP_curKey).Item("FileName") = fso.GetBaseName(GP_CurFileName) & "_" & Begin_Name_Num & "." & fso.GetExtensionName(GP_CurFileName)
					UploadRequest.Item(GP_curKey).Item("Value") = UploadRequest.Item(GP_curKey).Item("FileName")
        end if
        on error resume next
        GP_strm2.SaveToFile GP_FullFileName,2
        if err then
          Response.Write "<B>An error has occured saving uploaded file!</B><br><br>"
          Response.Write "Filename: " & Trim(GP_curPath) & UploadRequest.Item(GP_curKey).Item("FileName") & "<br>"
          Response.Write "Maybe the destination directory does not exist, or you don't have write permission.<br>"
          Response.Write "Please correct and <A HREF=""javascript:history.back(1)"">try again</a>"
    		  err.clear
  				GP_strm1.Close
  				GP_strm2.Close
  	  	  response.End
  	    end if
  			GP_strm1.Close
  			GP_strm2.Close
  			if storeType = "path" then
  				UploadRequest.Item(GP_curKey).Item("Value") = GP_curPath & UploadRequest.Item(GP_curKey).Item("Value")
  			end if
        on error goto 0
      end if
    end if
  next

End Sub

'把普通字符串转成二进制字符串函数
Function getByteString(StringStr)
    getByteString=""
  For i = 1 To Len(StringStr) 
    XP_varchar = mid(StringStr,i,1)
    XP_varasc = Asc(XP_varchar) 
    If XP_varasc < 0 Then 
       XP_varasc = XP_varasc + 65535 
    End If 

    If XP_varasc > 255 Then 
       XP_varlow = Left(Hex(Asc(XP_varchar)),2) 
       XP_varhigh = right(Hex(Asc(XP_varchar)),2) 
       getByteString = getByteString & chrB("&H" & XP_varlow) & chrB("&H" & XP_varhigh) 
    Else 
       getByteString = getByteString & chrB(AscB(XP_varchar)) 
    End If 
  Next 
End Function

'把二进制字符串转换成普通字符串函数 
Function getString(StringBin)
   getString =""
   Dim XP_varlen,XP_vargetstr,XP_string,XP_skip
   XP_skip = 0 
   XP_string = "" 
 If Not IsNull(StringBin) Then 
      XP_varlen = LenB(StringBin) 
    For i = 1 To XP_varlen 
      If XP_skip = 0 Then
         XP_vargetstr = MidB(StringBin,i,1) 
         If AscB(XP_vargetstr) > 127 Then 
           XP_string = XP_string & Chr(AscW(MidB(StringBin,i+1,1) & XP_vargetstr)) 
           XP_skip = 1 
         Else 
           XP_string = XP_string & Chr(AscB(XP_vargetstr)) 
         End If 
      Else 
      XP_skip = 0
   End If 
    Next 
 End If 
      getString = XP_string 
End Function 

Function UploadFormRequest(name)
  on error resume next
  if UploadRequest.Item(name) then
    UploadFormRequest = UploadRequest.Item(name).Item("Value")
  end if  
End Function

'Process the upload
UploadQueryString = Replace(Request.QueryString,"GP_upload=true","")
if mid(UploadQueryString,1,1) = "&" then
	UploadQueryString = Mid(UploadQueryString,2)
end if

GP_uploadAction = CStr(Request.ServerVariables("URL")) & "?GP_upload=true"
If (Request.QueryString <> "") Then  
  if UploadQueryString <> "" then
	  GP_uploadAction = GP_uploadAction & "&" & UploadQueryString
  end if 
End If

If (CStr(Request.QueryString("GP_upload")) <> "") Then
  GP_redirectPage = ""
  If (GP_redirectPage = "") Then
    GP_redirectPage = CStr(Request.ServerVariables("URL"))
  end if
    
  RequestBin = Request.BinaryRead(Request.TotalBytes)
  Dim UploadRequest
  Set UploadRequest = CreateObject("Scripting.Dictionary")  

  BuildUploadRequest RequestBin, "../img", "file", "10000000", "uniq"

  
  '*** GP NO REDIRECT
end if  
if UploadQueryString <> "" then
  UploadQueryString = UploadQueryString & "&GP_upload=true"
else  
  UploadQueryString = "GP_upload=true"
end if  


%>
<%
' *** Edit Operations: (Modified for File Upload) declare variables

MM_editAction2 = CStr(Request.ServerVariables("URL")) 'MM_editAction2 = CStr(Request("URL"))
If (UploadQueryString <> "") Then
  MM_editAction2 = MM_editAction2 & "?" & UploadQueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: (Modified for File Upload) set variables

If (CStr(UploadFormRequest("MM_update2_small")) <> "" And CStr(UploadFormRequest("MM_recordId")) <> "") Then'更新图片

  MM_editConnection = MM_conn_STRING
  MM_editTable = "pro_trademark"
  MM_editColumn = "id"
  MM_recordId = "" + UploadFormRequest("MM_recordId") + ""
  MM_editRedirectUrl = "pro_trademark.asp"
  MM_fieldsStr  = "imgwidth|value|imgheight|value|fmimg|value"
  MM_columnsStr = "imgwidth|none,none,NULL|imgheight|none,none,NULL|img|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(UploadFormRequest(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And UploadQueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And UploadQueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & replace(UploadQueryString,"&&GP_upload=true","")
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & replace(UploadQueryString,"&&GP_upload=true","")
    End If
  End If

End If
%>
<%
if UploadFormRequest("oldimg") <> "" then'删除旧图
     Set File = CreateObject("Scripting.FileSystemObject")
	fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
	set fdir = File.getfile(fdir)
	set fdir = fdir.parentfolder
	set fdir = fdir.parentfolder								'获取父父目录
      ImagePath = fdir & "\img\" & UploadFormRequest("oldimg")
	  if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
end if'删除旧图
%>
<%
' *** Update Record: (Modified for File Upload) construct a sql update statement and execute it
 
If (CStr(UploadFormRequest("MM_update2_small")) <> "" And CStr(UploadFormRequest("MM_recordId")) <> "") Then'更新图片

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
' *** Insert Record: set variables

If (CStr(request.form("MM_insert")) = "addtop") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "pro_trademark"
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "name|value"
  MM_columnsStr = "name|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(request.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: set variables

If (CStr(request.form("MM_update")) = "modtop" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "pro_trademark"
  MM_editColumn = "id"
  MM_recordId = "" + request.form("MM_recordId") + ""
  MM_editRedirectUrl = ""
  MM_fieldsStr  = "name|value"
  MM_columnsStr = "name|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(MM_i+1) = CStr(request.form(MM_fields(MM_i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Delete Record: construct a sql delete statement and execute it
if request.form("submit2") <>""  then

' *** Delete Record: declare variables

if (CStr(request.form("MM_delete")) = "form1" And CStr(request.form("id")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "pro_trademark"
  MM_editColumn = "id"
  MM_recordId = "" + request.form("id") + ""
  MM_editRedirectUrl = ""

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If

' *** Delete Record: declare variables

  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the delete
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

end if
%>
<%
' *** Insert Record: construct a sql insert statement and execute it

Dim MM_tableValues
Dim MM_dbValues

If (CStr(request.form("MM_insert")) <> "") Then

  ' create the sql insert statement
  MM_tableValues = ""
  MM_dbValues = ""
  For MM_i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_formVal = MM_fields(MM_i+1)
    MM_typeArray = Split(MM_columns(MM_i+1),",")
    MM_delim = MM_typeArray(0)
    If (MM_delim = "none") Then MM_delim = ""
    MM_altVal = MM_typeArray(1)
    If (MM_altVal = "none") Then MM_altVal = ""
    MM_emptyVal = MM_typeArray(2)
    If (MM_emptyVal = "none") Then MM_emptyVal = ""
    If (MM_formVal = "") Then
      MM_formVal = MM_emptyVal
    Else
      If (MM_altVal <> "") Then
        MM_formVal = MM_altVal
      ElseIf (MM_delim = "'") Then  ' escape quotes
        MM_formVal = "'" & Replace(MM_formVal,"'","''") & "'"
      Else
        MM_formVal = MM_delim + MM_formVal + MM_delim
      End If
    End If
    If (MM_i <> LBound(MM_fields)) Then
      MM_tableValues = MM_tableValues & ","
      MM_dbValues = MM_dbValues & ","
    End If
    MM_tableValues = MM_tableValues & MM_columns(MM_i)
    MM_dbValues = MM_dbValues & MM_formVal
  Next
  MM_editQuery = "insert into " & MM_editTable & " (" & MM_tableValues & ") values (" & MM_dbValues & ")"

  If (Not MM_abortEdit) Then
    ' execute the insert
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%'以上为品牌加、删除%>
<% If (CStr(request.form("MM_update")) <> "" And CStr(request.form("MM_recordId")) <> "") Then
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
end if
%>

<%
' *** Update Record: set variables

If (CStr(request.form("MM_update_3")) <> "" And CStr(request.form("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "pro_trademark"
  MM_editColumn = "id"
  MM_recordId = "" + request.form("MM_recordId") + ""
  MM_editRedirectUrl = "pro_trademark.asp"
  MM_fieldsStr  = "name|value|url|value|content|value"
  MM_columnsStr = "name|',none,''|url|',none,''|content|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(request.form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>

<%
' *** Update Record: construct a sql update statement and execute it

If ((CStr(request.form("MM_update")) <> "") or  (CStr(request.form("MM_update_3")) <> "")) And (CStr(request.form("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rs2CLASS
Dim rs2CLASS_numRows

Set rs2CLASS = Server.CreateObject("ADODB.Recordset")
rs2CLASS.ActiveConnection = MM_conn_STRING
rs2CLASS.Source = "SELECT id,name FROM pro_trademark ORDER BY id ASC"
rs2CLASS.CursorType = 0
rs2CLASS.CursorLocation = 2
rs2CLASS.LockType = 1
rs2CLASS.Open()

rs2CLASS_numRows = 0
%>

<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">品牌总店管理</h1>

<script language="JavaScript">
<!--
function chkselect(form1)
{
    if (document.form1.id.value == "")
		{
		alert("请选择品牌");
		document.form1.id.focus();
		return false;
		}
return true;
}
function chkmodtop(modtop)
{
    if (document.modtop.dlname.value == "")
		{
		alert("请输入内容");
		document.modtop.dlname.focus();
		return false;
		}
}
function chkaddtop(addtop)
{
    if (document.addtop.name.value == "")
		{
		alert("请输入品牌名称");
		document.addtop.name.focus();
		return false;
		}
}
/*
function chknameen()
{
    if (document.form.name_en.value == "")
		{
		alert("必须输入英文品牌名称");
		document.form.name_en.focus();
		return false;
		}
}
*/

function MM_jumpMenu(targ,selObj,restore){ //v3.0
  eval(targ+".location='"+selObj.options[selObj.selectedIndex].value+"'");
  if (restore) selObj.selectedIndex=0;
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
  document.MM_returnValue = (errors == '');
}
//-->
</script>
<table width="100%" border="0" cellspacing="0" cellpadding="5" height=100 style="border:0" class="table0">
  <tr>
          <td class="td0"  valign="top">品牌<%if request.querystring("id")<>""then response.write "：<font color=ff0000>"& rsMODid.Fields.Item("name").Value &"</font>"%><br>
<%if request.form("submit1")<>"" then
redi__2 = MM_editAction_0 & "?id=" & request.form("id")
response.redirect(redi__2)
else%>
            <form name="form1" method="POST" action="<%=(MM_editAction_0)%>" onsubmit="return chkselect(form1)">
<%end if%>
              <% If Not rs2CLASS.EOF Or Not rs2CLASS.BOF Then %>
			  <select name="id" size="30">
              <% 
While (NOT rs2CLASS.EOF)
%>
			  <option value="<%=(rs2CLASS.Fields.Item("id").Value)%>">                <%=(rs2CLASS.Fields.Item("name").Value)%>
			  </option>
              <% 

  rs2CLASS.MoveNext()
Wend
%>
              </select>
              <input type="submit" name="submit1" value="选定这个品牌">
              <input name="Submit2" type="submit" onClick="GP_popupConfirmMsg('删除品牌将删除该品牌所有产品资料');return document.MM_returnValue" value="删除">
              <input type="hidden" name="MM_delete" value="form1">
			  <%else%>
			  <h3>尚无品牌，请添加</h3>
              <% End If ' end Not rs2CLASS.EOF Or NOT rs2CLASS.BOF %>
            </form>
            <form name="addtop" action="<%=(MM_editAction_0)%>" method="POST" onsubmit="return chkaddtop(addtop)">
              <input name="name" type="text" size="15"><input type="submit" value="增加品牌">
              <input type="hidden" name="MM_insert" value="addtop">
            </form>
		</td><% if request.querystring("id") <>"" then'如果选择了品牌%>
<script language="javascript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}
//-->
</script>
<script language="JavaScript">
<!--

function checkFileUpload(form,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  for (var i = 0; i<form.elements.length; i++) {
    field = form.elements[i];
    if (field.type.toUpperCase() != 'FILE') continue;
    checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
} }

function checkOneFileUpload(field,extensions,requireUpload,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight) { //v2.09
  document.MM_returnValue = true;
  if (extensions != '') var re = new RegExp("\.(" + extensions.replace(/,/gi,"|").replace(/\s/gi,"") + ")$","i");
    if (field.value == '') {
      if (requireUpload) {alert('File is required!');document.MM_returnValue = false;field.focus();return;}
    } else {
      if(extensions != '' && !re.test(field.value)) {
        alert('不允许上传这类文件\n只能上传以下格式： ' + extensions + '.\n请选择其他文件');
        document.MM_returnValue = false;field.focus();return;
      }
    document.PU_uploadForm = field.form;
    re = new RegExp(".(gif|jpg|png|bmp|jpeg)$","i");
    if(re.test(field.value) && (sizeLimit != '' || minWidth != '' || minHeight != '' || maxWidth != '' || maxHeight != '' || saveWidth != '' || saveHeight != '')) {
      checkImageDimensions(field,sizeLimit,minWidth,minHeight,maxWidth,maxHeight,saveWidth,saveHeight);
    } }
}

function showImageDimensions(fieldImg) { //v2.09
  var isNS6 = (!document.all && document.getElementById ? true : false);
  var img = (fieldImg && !isNS6 ? fieldImg : this);
  if (img.width > 0 && img.height > 0) {
  if ((img.minWidth != '' && img.minWidth > img.width) || (img.minHeight != '' && img.minHeight > img.height)) {
    alert('上传文件太小!\n应大于 ' + img.minWidth + '（宽） x ' + img.minHeight + '（高）'); return;}
  if ((img.maxWidth != '' && img.width > img.maxWidth) || (img.maxHeight != '' && img.height > img.maxHeight)) {
    alert('上传文件太大!\n不超过 ' + img.maxWidth + '（宽） x ' + img.maxHeight + '（高）'); return;}
  if (img.sizeLimit != '' && img.fileSize > img.sizeLimit) {
    alert('上传文件太大!\n不超过 ' + (img.sizeLimit/1024) + ' KBytes'); return;}
  if (img.saveWidth != '') document.PU_uploadForm[img.saveWidth].value = img.width;
  if (img.saveHeight != '') document.PU_uploadForm[img.saveHeight].value = img.height;
  document.MM_returnValue = true;
} }

function checkImageDimensions(field,sizeL,minW,minH,maxW,maxH,saveW,saveH) { //v2.09
  if (!document.layers) {
    var isNS6 = (!document.all && document.getElementById ? true : false);
    document.MM_returnValue = false; var imgURL = 'file:///' + field.value.replace(/\\/gi,'/').replace(/:/gi,'|').replace(/"/gi,'').replace(/^\//,'');
    if (!field.gp_img || (field.gp_img && field.gp_img.src != imgURL) || isNS6) {field.gp_img = new Image();
		   with (field) {gp_img.sizeLimit = sizeL*1024; gp_img.minWidth = minW; gp_img.minHeight = minH; gp_img.maxWidth = maxW; gp_img.maxHeight = maxH;
  	   gp_img.saveWidth = saveW; gp_img.saveHeight = saveH; gp_img.onload = showImageDimensions; gp_img.src = imgURL; }
	 } else showImageDimensions(field.gp_img);}
}
//-->
</script>

</td><td class="td0"  valign=top style="border-left:1px solid #ccc;padding-left:15px">
品牌概况<br>
<table border=0 cellpadding=2 cellspacing=0 align=center  class="table0">
<form name="form" method="POST" action="<%=MM_editAction%>" onsubmit="return chknameen()">
<tr><td class="td0"  width=126>品牌名称</td><td class="td0"  width=400><input size=40 type=text name="name" style="width:300px" value="<%=rsMODPAGE4("name")%>"></td></tr>
<!-- <tr><td class="td0" >品牌英文名</td><td class="td0" ><input size=40 type=text name="name_en" style="width:300px" value="<%=rsMODPAGE4("name_en")%>"></td></tr>
 --><tr><td class="td0" >网站（品牌网站URL）<br><font color=#ff0000 class=p12>（要包括http://)</font></td><td class="td0" ><input size=40 type=text name="url" style="width:300px" value="<%=rsMODPAGE4("url")%>"></td></tr>
<tr><td class="td0" >简介<br><font color=#ff0000 class=p12>（非html格式）</font></td><td class="td0" ><textarea name="content" rows="8" cols="40" style="width:300px"><%=rsMODPAGE4("content")%></textarea></td></tr>
<tr><td class="td0" >&nbsp;</td><td class="td0" ><input type="submit" name="submit0" value="提交"></td></tr>
<input type="hidden" name="MM_update_3" value="true">
<input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
</form>
</table>
<br>
修改品牌标识图（<font class=p12>高度不超过50像素</font>）：
<form name="form2_small" method="POST" action="<%=MM_editAction2%>" enctype="multipart/form-data" onSubmit="checkFileUpload(this,'GIF,JPG,JPEG',false,200,'','','','50','imgwidth','imgheight');return document.MM_returnValue">
	  <table border="0" cellspacing="0" cellpadding="4" align=center  class="table0">
		  <tr>
			<td class="td0"  width="126">
<% if rsMODPAGE4.Fields.Item("img").Value <> "" then%><a onClick="MM_openBrWindow('../img.asp?img_dir=img&img=<%=(rsMODPAGE4.Fields.Item("img").Value)%>&imgwidth=<%=(rsMODPAGE4.Fields.Item("imgwidth").Value)%>&imgheight=<%=(rsMODPAGE4.Fields.Item("imgheight").Value)%>&title=<%= Server.HTMLEncode((rsMODPAGE4.Fields.Item("name").Value))%>','','scrollbars=yes,resizable=yes,width=300,height=250')" href="javascript:void(null)" onmouseout="window.status='';return true;" 
onmouseover="window.status='单击打开详图';return true;"><img src="../img/<%=(rsMODPAGE4.Fields.Item("img").Value)%>" alt="点击打开详图" border=0></a><%
else
response.write("&nbsp;")
end if%></td>
			<td class="td0"  width="400">
			<input type=hidden name="imgwidth">
			<input type=hidden name="imgheight">
			  <input type="file" name="fmimg" size="40" onChange="checkOneFileUpload(this,'GIF,JPG,JPEG',false,200,'','','','50','imgwidth','imgheight')">
			  <br><font size="-1">请用jpg或gif格式，为了保证网站的兼容性，图片名称请用英文</font></td>
		  </tr>
		  <tr><td class="td0"  width="126">&nbsp;</td><td class="td0"  width="400" nowrap><input type="submit" name="Submit" value="确定" ></td></tr>
<input type=hidden name="oldimg" value="<%=(rsMODPAGE4.Fields.Item("img").Value)%>">
<input type="hidden" name="MM_update2_small" value="true">
<input type="hidden" name="MM_recordId" value="<%= rsMODPAGE4.Fields.Item("id").Value %>">
	</table>
	</form>


	<% end if
	  %></td></tr></table>
<!--#include file ="_bottom.asp"-->
<%
rsMODPAGE4.Close()
set rsMODPAGE4 = nothing
rsMODid.close()
set rsMODid = nothing
%>