<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file="event__config.asp" -->
<%
Dim t_action
t_action =request.querystring("action")

Select Case t_action
Case "add"%>
<table border="1" cellpadding="4" cellspacing="0" width="370" bordercolor="#cccccc" bgcolor="#ffffff" style="border-collapse: collapse;border:dotted;border-width:1px" align=center>	
<form name="add" method="post" action="jobs_event_config.asp?cid=<%=request.querystring("cid")%>&action=add" onsubmit="return validateadd(<%=request.querystring("cid")%>);">
<tr><td nowrap>流程名称</td><td><input type=text name="name" style="width:300px" title="流程名称用于识别，这一信息其他人不会看到。请用您熟悉的词语。"></td>
<tr><td>发送EMAIL</td><td><input type="checkbox" title="如选上，则会给应聘者信箱发送一个邮件" name="email_on" onclick="emailcheck();"></td></tr>
<tr id="email0" style="display:none"><td colspan=2 bgcolor=#eeeeee>EMAIL模板定义（注意这些内容是发给应聘者的。不要定义称呼和签名，它们由系统自动加上。）</td></tr>
<tr id="email1" style="display:none"><td>标题</td><td><input name="email_subject" type=text style="width:300px"></td></tr>
<tr id="email2" style="display:none"><td>内容</td><td><textarea name="email_content" style="width:300px;height:90px;overflow-y:visible"></textarea></td></tr>
<tr><td colspan=2 align=center><input type="submit" value="增 加" class=i80></td></tr>
</form></table>
<%
Case "mod"
	set rs=server.createobject("adodb.recordset")
	sql = "select * from event_type where event_type_id=" & request.querystring("id")
	rs.open sql,MM_conn_STRING,1,1
	If rs("email_on") = True Then
		emailon = True
		emailonstr_1 = " checked"
		emailonstr_2 = " style='display:block'"		
	Else
		emailon = false
		emailonstr_1 = ""
		emailonstr_2 = " style='display:none'"
	
	End if
%>
<table border="1" cellpadding="4" cellspacing="0" width="370" bordercolor="#cccccc" bgcolor="#ffffff" style="border-collapse: collapse;border:dotted;border-width:1px" align="center">
<form name="add" method="post" action="jobs_event_config.asp?id=<%=rs("event_type_id")%>&action=mod" onsubmit="return validateadd(1);">
<tr><td nowrap>流程名称</td><td><input title="流程名称用于识别，这一信息其他人不会看到。请用您熟悉的词语。" type=text name="name" value="<%=rs("name")%>" style="width:300px"<%If rs("not_del") = True Then response.write" disabled"%>></td>
<tr><td>发送EMAIL</td><td><input type="checkbox" title="如选上，则会给应聘者信箱发送一个邮件" name="email_on"<%=emailonstr_1%> onclick="emailcheck();"></td></tr>
<tr id="email0"<%=emailonstr_2%>><td colspan=2 bgcolor=#eeeeee>EMAIL模板定义（注意这些内容是发给应聘者的。不要定义称呼和签名，它们由系统自动加上。）</td></tr>
<tr id="email1"<%=emailonstr_2%>><td>标题</td><td><input name="email_subject" type=text style="width:300px" value="<%=rs("email_subject")%>"></td></tr>
<tr id="email2"<%=emailonstr_2%>><td>内容</td><td><textarea name="email_content" style="width:300px;height:90px;overflow-y:visible"><%=rs("email_content")%></textarea></td></tr>
<tr><td colspan=2 align=center><input type="submit" value="修 改" class=i80></td></tr>
</form></table>
<%
	rs.close()
	Set rs=nothing
End Select
%>