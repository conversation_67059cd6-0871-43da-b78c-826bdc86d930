<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<% If (Session("isadmin") <> true) Then Response.Redirect("../admin/") %> 
<!--#include file="../Connections/conn.asp" -->
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Update Record: set variables

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "other"
  MM_editColumn = "name"
  MM_recordId = "'" + Request.Form("MM_recordId") + "'"
  MM_editRedirectUrl = "links.asp"
  MM_fieldsStr  = "name|value|title|value|this_memo|value"
  MM_columnsStr = "name|',none,''|title|',none,''|this_memo|',none,''"

  ' create the MM_fields and MM_columns arrays
  MM_fields = Split(MM_fieldsStr, "|")
  MM_columns = Split(MM_columnsStr, "|")
  
  ' set the form values
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    MM_fields(i+1) = CStr(Request.Form(MM_fields(i)))
  Next

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If

End If
%>
<%
' *** Update Record: construct a sql update statement and execute it

If (CStr(Request("MM_update")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql update statement
  MM_editQuery = "update " & MM_editTable & " set "
  For i = LBound(MM_fields) To UBound(MM_fields) Step 2
    FormVal = MM_fields(i+1)
    MM_typeArray = Split(MM_columns(i+1),",")
    Delim = MM_typeArray(0)
    If (Delim = "none") Then Delim = ""
    AltVal = MM_typeArray(1)
    If (AltVal = "none") Then AltVal = ""
    EmptyVal = MM_typeArray(2)
    If (EmptyVal = "none") Then EmptyVal = ""
    If (FormVal = "") Then
      FormVal = EmptyVal
    Else
      If (AltVal <> "") Then
        FormVal = AltVal
      ElseIf (Delim = "'") Then  ' escape quotes
        FormVal = "'" & Replace(FormVal,"'","''") & "'"
      Else
        FormVal = Delim + FormVal + Delim
      End If
    End If
    If (i <> LBound(MM_fields)) Then
      MM_editQuery = MM_editQuery & ","
    End If
    MM_editQuery = MM_editQuery & MM_columns(i) & " = " & FormVal
  Next
  MM_editQuery = MM_editQuery & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the update
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
Dim rsLINKPRO__MMColParam
rsLINKPRO__MMColParam = "link_pro"
if (Request("MM_EmptyValue") <> "") then rsLINKPRO__MMColParam = Request("MM_EmptyValue")
%>
<%
set rsLINKPRO = Server.CreateObject("ADODB.Recordset")
rsLINKPRO.ActiveConnection = MM_conn_STRING
rsLINKPRO.Source = "SELECT name, this_memo, title FROM other WHERE name = '" + Replace(rsLINKPRO__MMColParam, "'", "''") + "'"
rsLINKPRO.CursorType = 0
rsLINKPRO.CursorLocation = 2
rsLINKPRO.LockType = 3
rsLINKPRO.Open()
rsLINKPRO_numRows = 0
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">友情链接规则</h1>
	<form ACTION="<%=MM_editAction%>" METHOD="POST" name="form1">
	  <input type="hidden" name="name" value="link_pro">
	  <table width=550 align=center>
		<tr> 
		  <td nowrap bgcolor=<%=(back_menubackcolor)%>>规则名称</td>
		  <td> 
			<input type="text" name="title" size="60" class=i500 value="<%=(rsLINKPRO.Fields.Item("title").Value)%>">
		  </td>
		<tr> 
		  <td valign=top bgcolor=<%=(back_menubackcolor)%>>规则内容</td>
		  <td> 
			<textarea name="this_memo" cols="60" rows="12" class=i500><%=(rsLINKPRO.Fields.Item("this_memo").Value)%></textarea>
		  </td>
		</tr>
		<tr> 
		  <td bgcolor=<%=(back_menubackcolor)%>>&nbsp;</td>
		  <td> 
			<input type="submit" value="更新">
		  </td>
		</tr>
	  </table>
	  <input type="hidden" name="MM_update" value="true">
	  <input type="hidden" name="MM_recordId" value="<%= rsLINKPRO.Fields.Item("name").Value %>">
	</form>
<!--#include file ="_bottom.asp"--><%
rsLINKPRO.Close()
set rsLINKPRO = nothing
%>
