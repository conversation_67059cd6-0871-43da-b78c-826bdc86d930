<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">招聘系统帮助</h1>
<ul id="help">
<p><span class="emph">招聘系统的设计思想</span>：2005年后，传统的人才市场转向网络求职，通过一个好的系统管理网络招聘工作有利于提高人力资源管理效益。本招聘系统通过网站发布招聘信息，收集简历；并在线与应聘者交互，筛选；对于适合者，再发出面试通知；记录面试效果。</p>
<div style="border:#999999 dotted;border-width:1px;margin-bottom:15px;text-align:center">
<h2>公司流程</h2>
<img src="jobs.gif" width="601" height="201" border="0">
<h2>应聘者流程</h2>
</div>

<li>发布招聘信息</li>
<p>点击<a href="jobs_position.asp">“招聘职位”</a>，按部门发布招聘信息，可设置招聘人数、职位要求。<br>
如果在人才网站或者传统媒体发布招聘信息，注意提醒应聘者通过本网站提交简历。</p>

<li>在线过程</li>
<p>发布招聘信息收集简历后，可以与应聘者在线交流，在后台在线处理时，系统自动发EMAIL到应聘者信箱。<span class="emph">本系统可以定义信件模板，能大大提高交互效率</span>。而且回信符合商业规范，给应聘者严谨、礼貌、亲和的企业形象。<br>
应聘者通过EMAIL方式与公司交流，当公司负责人力资源的员工收到EMAIL后，最好通过本招聘系统回复，便于管理。<br>
在线一般包括询问、确认、婉拒、约见、通知、问候等过程，本系统的<span class="emph">具有强大的自定义流程功能</span>。<br>
</p>

<li>面试评价</li>
<p>对于面试过程，本系统也可以辅助管理，面试后将面试人员的评价输入本系统，便于最后确定人选。</p>

<li>加入黑名单</li>
<p>对于极不符合需要的应聘者，可以将其加入黑名单。</p>

<li>储备人才</li>
<p>有的优秀的人才，公司现在可能没有适合的职位，或者因为其他原因，暂不聘用这些人。可以将他们加入人才库，作为公司的储备人才。</p>

<li>招聘系统设置</li>
<p>在使用前，先设置好公司部门、招聘流程。</p>
<p>流程指招聘过程中要进行的不同类别的工作，如：<br>要在招聘过程中给应聘员工发送考试通知。就可以添加一个考试通知的流程，定义邮件主题和内容模板。在处理应聘人员时只要选择这个流程就可以完成这个工作，不需要再去处理工作细节。</p>
<li>其他</li>
<p>预留了接口，公司上其他系统后，可以与其他系统集成。</p>
</ul>

<!--#include file ="_bottom.asp"-->