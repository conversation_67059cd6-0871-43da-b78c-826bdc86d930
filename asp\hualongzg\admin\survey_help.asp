<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<HTML>
<HEAD>
<TITLE>在线调查系统使用帮助</TITLE>
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<style>
BODY { FONT-SIZE: 9pt; COLOR: #333333; FONT-FAMILY: "宋体"}
A {	COLOR: #666666; FONT-FAMILY: 宋体; TEXT-DECORATION: underline }
A:hover { COLOR: #ff0000; FONT-FAMILY: 宋体; TEXT-DECORATION: none }
.plus {	FONT-WEIGHT: bold; COLOR: #003366; FONT-FAMILY: 宋体;font-size:12pt }
HR { FILTER: alpha(opacity=100,finishopacity=0,style=1,startx=0,starty=0,finishx=185,finishy=0); COLOR: #ffffff; HEIGHT: 1px}
td { font-size: 9pt;}
</style>
</HEAD>
<BODY bgcolor="#ffffff" topmargin="5" leftmargin="5">
<a name=top></a>
<span class=plus>在线调查系统使用帮助</span>
<div style="background-color:#eeeeee;padding-top:1px;padding-bottom:1px">
<ul>
<li><a href="#function">简述</a>
<li><a href="#skill">使用方法</a>
<li><a href="#fq">疑难解答</a>
</ul></div>
<HR SIZE=1>

<a name=function></a>
<span class=plus>简述：</span> 
<ul>
<li>在线调查系统，可以在网页发布各种调查，获取客户反馈。</li>
<li>本在线调查系统的前台，是通过FLASH饼图与动态页面、WEB数据库相结合，读取访问者提交的数据，实时描绘调查效果。</li>
<li>本系统后台在一个页面中集成所有的操作，功能强大。其突出特点是：可以同时设定多个调查项目，并且不同的调查项目的切换非常方便。这是已有的调查系统所不具备的。</li>
<li>对每个调查选项可设定初始值。</li>
<li>一个调查项目最多5个选项。</li>
</UL>
<HR SIZE=1>
<a name=skill></a>
<span class=plus> 使用方法：</span>
<UL>
  <LI><b>增加调查项目</b><BR>
      您可以无限制的增加调查项目，这样您可以将未来要开展的调查都设置好。点击“→ 增加调查项目”增加项目。对于要重新使用的调查项目您也不用删除，因为调查结果是一种具有商业价值的数据。
  <LI><b>设置选项</b><BR>
每个调查项目最多5个选项，最少要二个选项（一个选项谈不上调查:-D）。选项的值可以随时修改，当然您可以不改，将它设定为0即可。这是为了满足您的特殊需求。
  <LI><b>打开调查</b><BR>
打开的调查会以不同颜色的“已打开”按钮显示，对于没有设置调查选项的调查项目（即少于2个选项的调查项目），会提示您设置调查选项。打开一个调查时，原来打开的调查会自动关闭。<br>
  <LI><b>在前台不显示调查</b><BR>
如果您关闭了所有的调查项目，在前台将不会显示调查。
</UL>

<HR SIZE=1>

<a name=fq></a>
<span class=plus>疑难解答：</span> 
<p>问：一个调查项目的重复投票限定多长时间？<br>
答：7天。</p>

<p>问：打开一个新的调查项目，会不会像其他调查系统一样，因为访问者已经给以前的调查项目投票，从而使访问者对新的调查项目的提交无效，而且这种无效管理员无法觉察？<br>
答：不会。本调查系统的每个调查项目只要打开后，就会重新写设置值。它是与调查项目的打开时间与序号相关的，所以绝对不会出现上述情况。</p>

<p>问：操作时我要注意什么，比如调查项目的字数限制，初始值的设定？<br>
答：不用。本系统对各类限制作了傻瓜式（智能）处理，您无法在无意中犯错误。那些限制的文字，只是为了提示您而已。</p>
<div style="background-color:#eeeeee;padding-top:3px;padding-bottom:3px;padding-left:3px">
<a href=#top>↑顶部</a></div>
</BODY>
</HTML>
