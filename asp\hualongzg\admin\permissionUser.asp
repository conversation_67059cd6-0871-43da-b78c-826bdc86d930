<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">用户管理</h1>
<%
if not isempty(request("selAnnounce")) then
idlist=request("selAnnounce")
if instr(idlist,",")>0 then
dim idarr
idArr=split(idlist)
dim id
for i = 0 to ubound(idarr)
id=clng(idarr(i))
call deleteannounce(id)
next
else
call deleteannounce(clng(idlist))
end if
end if 
dim totalPut
maxperpage=100
dim CurrentPage
dim TotalPages
dim i,j
if not isempty(request("page")) then
currentPage=cint(request("page"))
else
currentPage=1
end if
dim rs
dim sql
set rs=server.createobject("adodb.recordset") 
sql="select * from admin inner join usergroup on usergroup.group_id = admin.group_id order by admin.group_id,user_id asc"
rs.open sql,MM_conn_STRING,1,1 
if rs.eof and rs.bof then 
response.write "<p align='center'>对不起，没有用户信息</p>" 
else 
totalPut=rs.recordcount 
if currentpage<1 then 
currentpage=1 
end if 
if (currentpage-1)*MaxPerPage>totalput then 
if (totalPut mod MaxPerPage)=0 then 
currentpage= totalPut \ MaxPerPage 
else 
currentpage= totalPut \ MaxPerPage + 1 
end if 
end if 
if currentPage=1 then 
showContent
else 
if (currentPage-1)*MaxPerPage<totalPut then 
rs.move (currentPage-1)*MaxPerPage 
dim bookmark 
bookmark=rs.bookmark 
showContent
else 
currentPage=1 
showContent
end if 
end if 
rs.close 
end if 
set rs=nothing
sub showContent 
%>
<br>
<Form name="search" method="POST" action="permissionUser.asp">
<TABLE border=1 cellPadding=4 cellSpacing=0 width="600" bordercolorlight=<%=back_menubackcolor0%> style="border-collapse: collapse">
<thead>
<TR height=25>
<TD align="left" colspan=3>网站管理员管理(点击用户名及部门进行相应操作)</TD>
<TD align="right" nowrap><font color=#ff0000> <a href="permissionUser+detail.asp?action=add">
<font color="#FF0000">→增加用户</font></a></font></TD>
</TR>
<TR height=28 bgcolor="<%=back_menubackcolor%>"> 
<TD width="25%" align="center">姓名</td>
<TD width="25%" align="center">账号</td>
<TD width="25%" align="center">所属部门或角色</td>
<TD width="12%" align="center"><input type='submit' value='删除' onClick="GP_popupConfirmMsg('确实要删除这个用户？');return document.MM_returnValue" title="系统默认管理员不可删除"></td>
</TR>
</thead>
<%do while not rs.eof%>
<TR height="28" bgcolor="#ffffff"> 
<TD width="8%" align="center"><font face="Arial"><b><a href="permissionUser+detail.asp?user_id=<%=rs("user_id")%>"><%=rs("admin.name")%></a></b></font>　</td>
<td align=center><%=rs("id")%></td>
<TD width="30%" align="center"><a href="permissionGroup+detail.asp?group_id=<%=rs("admin.group_id")%>"><%=rs("usergroup.name")%></a></td>
<TD width="12%" align="center"><%if rs("this_right") <> 99 then%><input type='checkbox' name='selAnnounce' value='<%=cstr(rs("user_id"))%>'><%
else 
response.write("<font color=ff0000 class=p12>默认管理员</font>")
end if%>&nbsp;</td>
</TR>
<% I=I+1
IF I>=MaxPerPage then exit do
RS.movenext
LOOP
%>
<%
end sub

sub deleteannounce(id)
set conn=server.createobject("ADODB.CONNECTION")
conn.open MM_conn_STRING
dim rs,sql
set rs=server.createobject("adodb.recordset")
sql="delete from admin where user_id="&cstr(id)
conn.execute sql
set conn=nothing
End sub
%>
</TABLE>

 
<!--#include file ="_bottom.asp"-->