<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<!--#include file ="../_default.asp"-->
<%
if request.form("modify") <> "" then'写父目录_default.asp

dim fso,fdir,fdata, f, ts
'f11 =  & "test.asp"
set  fso = CreateObject("Scripting.FileSystemObject")
fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
set fdir = fso.getfile(fdir)
set fdir = fdir.parentfolder
set fdir = fdir.parentfolder								'获取父目录
fdata = fdir & "\" & "_default.asp"
fso.CreateTextFile(fdata)									'建立文件

Const ForReading = 1, ForWriting = 2, ForAppending = 3
Const TristateUseDefault = -2, TristateTrue = -1, TristateFalse = 0
Set f = fso.GetFile(fdata)
Set ts = f.OpenAsTextStream(ForWriting, TristateUseDefault)

ts.write("<%dim r_t_1,r_t_2,r_t_1_str,r_t_2_str" & vbcrlf)
ts.write("r_t_1 = " & request.form("r_t_1") & vbcrlf)
ts.write("r_t_2 = " & request.form("r_t_2") & vbcrlf)
ts.write("r_t_3 = " & request.form("r_t_3") & vbcrlf)
ts.write("r_t_4 = " & request.form("r_t_4") & vbcrlf)

ts.write("r_t_1_str = """ & sub_name(request.form("r_t_1")) & """" & vbcrlf)
ts.write("r_t_2_str = """ & sub_name(request.form("r_t_2")) & """" & vbcrlf)
ts.write("r_t_3_str = """ & sub_name(request.form("r_t_3")) & """" & vbcrlf)
ts.write("r_t_4_str = """ & sub_name(request.form("r_t_4")) & """" & vbcrlf)
ts.write(vbCrlf & "%" & ">")			'结束封闭

ts.Close
set ts = nothing

set conn=server.createobject("ADODB.CONNECTION")
conn.open MM_conn_STRING
sql="update other set title='" & request.form("r_t_1") & "',content='" & sub_name(request.form("r_t_1")) & "' where name='r_t_1'"
sql2= "update other set title='" & request.form("r_t_2") & "',content='" & sub_name(request.form("r_t_2")) & "' where name='r_t_2'" 
sql3= "update other set title='" & request.form("r_t_3") & "',content='" & sub_name(request.form("r_t_3")) & "' where name='r_t_3'"
sql4= "update other set title='" & request.form("r_t_4") & "',content='" & sub_name(request.form("r_t_4")) & "' where name='r_t_4'"
conn.execute sql
conn.execute sql2
conn.execute sql3
conn.execute sql4
Set conn=nothing

response.redirect("top_right.asp")
end if

function sub_name(subid)
	dim rs
	set rs=server.createobject("adodb.recordset") 
	sql="select name from subclass where subid = " & subid
	rs.open sql,MM_conn_STRING,1,1 
	sub_name=rs("name")
	rs.close()
	set rs=nothing
end function
%>
<h1 class="b_ch1">首页栏目</h1>

<table width=400 border=0 class=table0>
<form method=post name=form1 onsubmit="return VerifyInput()">
<%
set rs=server.createobject("adodb.recordset") 
sql="select name,subid from subclass where id<> 1 and onlyone=false order by id,subid asc" 
rs.open sql,MM_conn_STRING,1,1 

set rs2=server.createobject("adodb.recordset") 
sql="select name,subid from subclass where id= 10 and onlyone=false order by subid asc" 
rs2.open sql,MM_conn_STRING,1,1 
%>
<tr>
	<td class=td0 width=150>学校首页右上角栏目一</td>
	<td class=td0><select name="r_t_1"><%
	while not rs.eof%>
	<option value="<%=rs("subid")%>"<%if r_t_1= rs("subid") then response.write(" selected")%>><%=rs("name")%></option><%
	rs.movenext()
	wend
	%></select></td>
</tr>
<tr>
	<td class=td0>学校首页右上角栏目二</td>
	<td class=td0><select name="r_t_2"><%
	rs.movefirst()
	while not rs.eof%>
	<option value="<%=rs("subid")%>"<%if r_t_2= rs("subid") then response.write(" selected")%>><%=rs("name")%></option><%
	rs.movenext()
	wend
	rs.close()
set rs=nothing
	%></select>
</td>
</tr>
<tr><td class=td0 colspan=2>&nbsp;</td></tr>
<tr><td class=td0 colspan=2 height=1 bgcolor=<%=back_titlebackcolor%>></td></tr>
<tr><td class=td0 colspan=2>&nbsp;</td></tr>
<tr>
	<td class=td0 width=150>附医首页左边栏目一</td>
	<td class=td0><select name="r_t_3"><%
	while not rs2.eof%>
	<option value="<%=rs2("subid")%>"<%if r_t_3= rs2("subid") then response.write(" selected")%>><%=rs2("name")%></option><%
	rs2.movenext()
	wend
	%></select></td>
</tr>
<tr>
	<td class=td0>附医首页在边栏目二</td>
	<td class=td0><select name="r_t_4"><%
	rs2.movefirst()
	while not rs2.eof%>
	<option value="<%=rs2("subid")%>"<%if r_t_4= rs2("subid") then response.write(" selected")%>><%=rs2("name")%></option><%
	rs2.movenext()
	wend
	rs2.close()
set rs=Nothing
set rs2=nothing
	%></select>
</td>
</tr>
<tr><td class=td0 colspan=2>&nbsp;</td></tr>
<tr><td class=td0></td>
	<td class=td0><input type="submit" name="modify" value="提交" style="width:60px" onClick="GP_popupConfirmMsg('确认修改首页栏目？');return document.MM_returnValue"></td>
</tr>
</form>
<script language="JavaScript">
function VerifyInput()
{
	if (document.form1.r_t_2.value == document.form1.r_t_1.value)
	{
	alert("学校首页二个栏目不能相同");
	return false;
	}
	if (document.form1.r_t_3.value == document.form1.r_t_4.value)
	{
	alert("附医首页二个栏目不能相同");
	return false;
	}
}
</script>
</table>

<!--#include file ="_bottom.asp"-->