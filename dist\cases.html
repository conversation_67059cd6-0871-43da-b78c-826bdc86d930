<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程案例 - 华隆重工</title>
    <meta name="description" content="华隆重工工程案例展示，包括钢铁冶金、电力、矿山、建材等行业的成功项目案例。">
    <meta name="keywords" content="工程案例,华隆重工案例,重型机械项目,成功案例">

    <!-- TailwindCSS (Vite 构建) -->

    <!-- FontAwesome Local -->

    <!-- Custom CSS -->
  <script type="module" crossorigin src="/assets/main-BP7Nwped.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/main-8bX0uDHz.css">
</head>

<body class="font-sans text-dark-gray bg-white">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16 lg:h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center">
                        <span class="text-xl lg:text-2xl font-bold text-primary">华隆重工</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex space-x-8">
                    <a href="index.html"
                        class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">首页</a>
                    <a href="products/"
                        class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">产品中心</a>
                    <a href="services/"
                        class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">客户服务</a>
                    <a href="cases.html" class="text-primary font-medium">工程案例</a>
                    <div class="relative group">
                        <button
                            class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium flex items-center">
                            走进华隆 <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div
                            class="absolute top-full left-0 mt-2 w-48 bg-white shadow-lg rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="about/company.html"
                                class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray hover:text-primary">华隆简介</a>
                            <a href="about/contact.html"
                                class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray hover:text-primary">联系我们</a>
                        </div>
                    </div>
                    <a href="careers/"
                        class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">人才招聘</a>
                </nav>

                <!-- Language Switcher & Contact Button -->
                <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative group">
                        <button
                            class="flex items-center text-secondary hover:text-primary transition-colors duration-200">
                            <i class="fas fa-globe mr-2"></i>
                            <span class="hidden sm:inline">中文</span>
                            <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div
                            class="absolute top-full right-0 mt-2 w-32 bg-white shadow-lg rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">العربية</a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray bg-light-gray">中文</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">English</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Français</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Русский</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Español</a>
                        </div>
                    </div>

                    <!-- Contact Button -->
                    <a href="about/contact.html"
                        class="bg-accent text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200 font-medium">
                        联系我们
                    </a>

                    <!-- Mobile Menu Button -->
                    <button class="lg:hidden text-dark-gray hover:text-primary" id="mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="bg-light-gray py-4" aria-label="面包屑导航">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="index.html" class="text-secondary hover:text-primary">首页</a></li>
                <li><i class="fas fa-chevron-right text-secondary text-xs"></i></li>
                <li class="text-dark-gray font-medium">工程案例</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-primary mb-6">工程案例</h1>
                <p class="text-lg text-secondary max-w-3xl mx-auto">
                    华隆重工在钢铁冶金、电力、矿山、建材等行业拥有丰富的项目经验，为客户提供优质的设备和服务
                </p>
            </div>

            <!-- Filter Tabs -->
            <div class="flex flex-wrap justify-center mb-12">
                <button
                    class="filter-tab active px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="all">
                    全部案例
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="steel">
                    钢铁冶金
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="power">
                    电力行业
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="mining">
                    矿山开采
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="building">
                    建材行业
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="port">
                    港口物流
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200"
                    data-filter="metal">
                    有色金属
                </button>
            </div>

            <!-- Cases Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="cases-grid">
                <!-- Case 1 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="steel">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201018155050148-Cf1fsNRr.jpg" alt="钢铁厂斗轮机项目"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">钢铁冶金</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">某大型钢铁厂斗轮机项目</h3>
                        <p class="text-secondary mb-4">为客户提供了大型斗轮堆取料机设备，显著提高了原料处理效率，年处理能力达到500万吨。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2022-2023年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case1')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 2 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="power">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201018155115303-C7g0cFmi.jpg" alt="火电厂输送设备"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">电力行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">火电厂燃料输送系统</h3>
                        <p class="text-secondary mb-4">为大型火电厂提供了完整的燃料输送系统解决方案，包括卸车、输送、储存等全套设备。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2021-2022年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case2')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 3 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="mining">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201018155138197-D0JAQZRF.jpg" alt="矿山开采设备"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">矿山开采</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">大型矿山堆取料设备</h3>
                        <p class="text-secondary mb-4">为大型露天矿山提供了混匀堆取料机，有效提高了矿石处理效率和质量均匀性。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2022年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case3')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 4 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="building">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__20101916383346-CXPXGo-M.jpg" alt="建材厂矫直设备"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span
                                class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">建材行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">建材厂板材矫直生产线</h3>
                        <p class="text-secondary mb-4">为建材企业提供了先进的板材矫直机，大幅提高了产品质量和生产效率。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2022年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case4')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 5 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="steel">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201019163443828-C0GSgKug.jpg" alt="钢厂卸车机"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">钢铁冶金</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">钢厂原料卸车系统</h3>
                        <p class="text-secondary mb-4">为钢铁企业设计制造了高效的链斗卸车机系统，大幅提升了原料卸车效率。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case5')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 6 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="power">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201019164030240-Dz4LrooJ.jpg" alt="电厂链篦机"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">电力行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">电厂球团生产线</h3>
                        <p class="text-secondary mb-4">为电厂提供了链篦机设备，用于球团生产，设备运行稳定，产品质量优良。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case6')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 7 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="port">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201019164745694-2Xq1Ndxa.jpg" alt="港口散货装卸系统"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">港口物流</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">港口散货装卸系统</h3>
                        <p class="text-secondary mb-4">为沿海大型港口提供了散货装卸系统，包括卸船机、堆取料机、输送系统等。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2019-2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case5')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 8 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="metal">
                    <div class="relative overflow-hidden">
                        <img src="/assets/img__201019163622963-BPREHedX.jpg" alt="有色金属冶炼配套"
                            class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span
                                class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">有色金属</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">有色金属冶炼配套</h3>
                        <p class="text-secondary mb-4">为有色金属冶炼企业提供了原料处理和产品输送的配套设备。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2020-2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm"
                                onclick="openCaseModal('case6')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="mt-16 bg-primary rounded-lg p-8 text-white">
                <h2 class="text-3xl font-bold mb-8 text-center">项目成果</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">100+</div>
                        <div class="text-blue-200">成功项目</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">50+</div>
                        <div class="text-blue-200">合作客户</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">20+</div>
                        <div class="text-blue-200">年行业经验</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">99%</div>
                        <div class="text-blue-200">客户满意度</div>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="mt-16 text-center">
                <h2 class="text-3xl font-bold text-primary mb-4">开始您的项目</h2>
                <p class="text-lg text-secondary mb-8 max-w-2xl mx-auto">
                    如果您有重型机械设备需求，欢迎联系我们，我们将为您提供专业的解决方案。
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="about/contact.html"
                        class="bg-accent text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors duration-200 font-medium">
                        <i class="fas fa-phone mr-2"></i>
                        联系我们
                    </a>
                    <a href="products/"
                        class="bg-primary text-white px-8 py-3 rounded-lg hover:bg-blue-800 transition-colors duration-200 font-medium">
                        <i class="fas fa-cog mr-2"></i>
                        查看产品
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Case Modal -->
    <div id="case-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b">
                    <h2 id="modal-title" class="text-2xl font-bold text-primary"></h2>
                    <div class="flex items-center space-x-4">
                        <button id="prev-case" class="text-secondary hover:text-primary transition-colors">
                            <i class="fas fa-chevron-left text-xl"></i>
                        </button>
                        <button id="next-case" class="text-secondary hover:text-primary transition-colors">
                            <i class="fas fa-chevron-right text-xl"></i>
                        </button>
                        <button id="close-modal" class="text-secondary hover:text-primary transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Modal Content -->
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Image -->
                        <div>
                            <img id="modal-image" src="" alt="" class="w-full rounded-lg shadow-lg">
                        </div>

                        <!-- Details -->
                        <div>
                            <div class="mb-6">
                                <span id="modal-category"
                                    class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium"></span>
                            </div>

                            <div id="modal-description" class="text-secondary mb-6"></div>

                            <!-- Project Details -->
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-semibold text-primary mb-2">项目规模</h4>
                                        <p id="modal-scale" class="text-secondary"></p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-primary mb-2">投资金额</h4>
                                        <p id="modal-investment" class="text-secondary"></p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-semibold text-primary mb-2">实施周期</h4>
                                        <p id="modal-duration" class="text-secondary"></p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-primary mb-2">客户评价</h4>
                                        <p id="modal-rating" class="text-secondary"></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Technical Parameters -->
                            <div class="mt-6">
                                <h4 class="font-semibold text-primary mb-3">关键技术参数</h4>
                                <div id="modal-parameters" class="bg-light-gray p-4 rounded-lg"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark-gray text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-1">
                    <div class="flex items-center mb-4">
                        <img src="data:image/png;base64,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" alt="华隆重工" class="h-8 w-auto">
                        <span class="ml-3 text-xl font-bold">华隆重工</span>
                    </div>
                    <p class="text-gray-400 mb-4">专业致力于重型机械设备的设计、研发、制造、销售和服务</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="about/company.html"
                                class="text-gray-400 hover:text-white transition-colors duration-200">华隆简介</a></li>
                        <li><a href="products/"
                                class="text-gray-400 hover:text-white transition-colors duration-200">产品中心</a></li>
                        <li><a href="cases.html"
                                class="text-gray-400 hover:text-white transition-colors duration-200">工程案例</a></li>
                        <li><a href="careers/"
                                class="text-gray-400 hover:text-white transition-colors duration-200">人才招聘</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">客户服务</h3>
                    <ul class="space-y-2">
                        <li><a href="services/technical-renovation.html"
                                class="text-gray-400 hover:text-white transition-colors duration-200">技术改造</a></li>
                        <li><a href="services/spare-parts.html"
                                class="text-gray-400 hover:text-white transition-colors duration-200">产品备件</a></li>
                        <li><a href="about/contact.html"
                                class="text-gray-400 hover:text-white transition-colors duration-200">联系我们</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系信息</h3>
                    <div class="space-y-2 text-gray-400">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-3"></i>
                            <span>湖南省环保科技产业园内职教城路8号</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-3"></i>
                            <span>0731-84477152</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-fax mr-3"></i>
                            <span>0731-85386778</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-3"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 长沙华隆重型机器制造有限公司. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function () {
            const filterTabs = document.querySelectorAll('.filter-tab');
            const caseCards = document.querySelectorAll('.case-card');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', function () {
                    const filter = this.getAttribute('data-filter');

                    // Update active tab
                    filterTabs.forEach(t => {
                        t.classList.remove('active', 'bg-primary', 'text-white');
                        t.classList.add('bg-light-gray', 'text-secondary');
                    });
                    this.classList.add('active', 'bg-primary', 'text-white');
                    this.classList.remove('bg-light-gray', 'text-secondary');

                    // Filter cases
                    caseCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                            card.classList.add('animate-fade-in-up');
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Initialize first tab as active
            filterTabs[0].classList.add('active', 'bg-primary', 'text-white');
            filterTabs.forEach((tab, index) => {
                if (index > 0) {
                    tab.classList.add('bg-light-gray', 'text-secondary');
                }
            });
        });

        // Case data
        const casesData = {
            case1: {
                title: '某大型钢铁厂斗轮机项目',
                category: '钢铁冶金',
                image: 'images/products/img__201018155050148.jpg',
                description: '为客户提供了大型斗轮堆取料机设备，显著提高了原料处理效率，年处理能力达到500万吨。该项目采用了我公司最新的技术成果，实现了全自动化操作，大幅降低了人工成本。',
                scale: '2台斗轮堆取料机，年处理能力500万吨',
                investment: '8000万-1.2亿元',
                duration: '2022年3月 - 2023年8月',
                rating: '★★★★★ 客户满意度98%',
                parameters: '斗轮直径: 18m<br>堆取能力: 3000t/h<br>臂架长度: 65m<br>回转半径: 45m'
            },
            case2: {
                title: '火电厂燃料输送系统',
                category: '电力行业',
                image: 'images/products/img__201018155115303.jpg',
                description: '为大型火电厂提供了完整的燃料输送系统解决方案，包括卸车、输送、储存等全套设备，确保电厂燃料供应的连续性和可靠性。',
                scale: '输送能力2000t/h，储煤场容量10万吨',
                investment: '5000万-8000万元',
                duration: '2021年6月 - 2022年12月',
                rating: '★★★★★ 零故障运行记录',
                parameters: '输送带宽度: 1400mm<br>输送速度: 3.15m/s<br>输送距离: 2.5km<br>提升高度: 45m'
            },
            case3: {
                title: '矿山原料处理系统',
                category: '矿山开采',
                image: 'images/products/img__201018155138197.jpg',
                description: '为大型铁矿山提供了原料破碎、筛分、输送的完整解决方案，大幅提高了矿山的生产效率和自动化水平。',
                scale: '日处理原矿5万吨，3条生产线',
                investment: '1.5亿-2亿元',
                duration: '2020年9月 - 2022年5月',
                rating: '★★★★☆ 行业标杆项目',
                parameters: '破碎能力: 2000t/h<br>筛分精度: ±5%<br>输送距离: 3.2km<br>自动化程度: 95%'
            },
            case4: {
                title: '水泥厂原料预均化',
                category: '建材行业',
                image: 'images/products/img__201018155259997.jpg',
                description: '为大型水泥厂提供了原料预均化系统，采用先进的混匀技术，确保水泥生产原料的质量稳定性。',
                scale: '预均化堆场容量8万吨',
                investment: '3000万-5000万元',
                duration: '2021年1月 - 2021年10月',
                rating: '★★★★★ 质量稳定性显著提升',
                parameters: '堆料能力: 1500t/h<br>取料能力: 1200t/h<br>混匀效果: σ≤3%<br>堆场尺寸: 200m×80m'
            },
            case5: {
                title: '港口散货装卸系统',
                category: '港口物流',
                image: 'images/products/img__201019164745694.jpg',
                description: '为沿海大型港口提供了散货装卸系统，包括卸船机、堆取料机、输送系统等，大幅提升了港口作业效率。',
                scale: '年吞吐量1000万吨，4个泊位',
                investment: '2亿-3亿元',
                duration: '2019年5月 - 2021年3月',
                rating: '★★★★★ 作业效率提升40%',
                parameters: '卸船能力: 2500t/h<br>堆取能力: 2000t/h<br>输送距离: 1.8km<br>作业水深: -18m'
            },
            case6: {
                title: '有色金属冶炼配套',
                category: '有色金属',
                image: 'images/products/img__201019163622963.jpg',
                description: '为有色金属冶炼企业提供了原料处理和产品输送的配套设备，确保冶炼生产的连续性和高效性。',
                scale: '年处理有色金属原料200万吨',
                investment: '6000万-9000万元',
                duration: '2020年3月 - 2021年8月',
                rating: '★★★★☆ 环保达标运行',
                parameters: '处理能力: 800t/h<br>精矿品位: >65%<br>回收率: >95%<br>环保等级: 超低排放'
            }
        };

        let currentCaseId = '';
        const caseIds = Object.keys(casesData);

        // Case modal functionality
        function openCaseModal(caseId) {
            currentCaseId = caseId;
            const caseData = casesData[caseId];

            if (!caseData) return;

            // Update modal content
            document.getElementById('modal-title').textContent = caseData.title;
            document.getElementById('modal-category').textContent = caseData.category;
            document.getElementById('modal-image').src = caseData.image;
            document.getElementById('modal-image').alt = caseData.title;
            document.getElementById('modal-description').textContent = caseData.description;
            document.getElementById('modal-scale').textContent = caseData.scale;
            document.getElementById('modal-investment').textContent = caseData.investment;
            document.getElementById('modal-duration').textContent = caseData.duration;
            document.getElementById('modal-rating').innerHTML = caseData.rating;
            document.getElementById('modal-parameters').innerHTML = caseData.parameters;

            // Show modal
            document.getElementById('case-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeCaseModal() {
            document.getElementById('case-modal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function showPrevCase() {
            const currentIndex = caseIds.indexOf(currentCaseId);
            const prevIndex = currentIndex > 0 ? currentIndex - 1 : caseIds.length - 1;
            openCaseModal(caseIds[prevIndex]);
        }

        function showNextCase() {
            const currentIndex = caseIds.indexOf(currentCaseId);
            const nextIndex = currentIndex < caseIds.length - 1 ? currentIndex + 1 : 0;
            openCaseModal(caseIds[nextIndex]);
        }

        // Modal event listeners
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('close-modal').addEventListener('click', closeCaseModal);
            document.getElementById('prev-case').addEventListener('click', showPrevCase);
            document.getElementById('next-case').addEventListener('click', showNextCase);

            // Close modal when clicking outside
            document.getElementById('case-modal').addEventListener('click', function (e) {
                if (e.target === this) {
                    closeCaseModal();
                }
            });

            // Close modal with ESC key
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    closeCaseModal();
                }
            });
        });
    </script>
</body>

</html>