(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))i(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const s of c.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&i(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(o){if(o.ep)return;o.ep=!0;const c=n(o);fetch(o.href,c)}})();document.addEventListener("DOMContentLoaded",function(){g(),h(),b(),v(),x(),L()});function g(){const e=document.getElementById("mobile-menu-button"),t=document.getElementById("mobile-menu");e&&t&&e.addEventListener("click",function(){t.classList.toggle("hidden");const i=!t.classList.contains("hidden");e.setAttribute("aria-expanded",i);const o=e.querySelector("i");o&&(o.className=i?"fas fa-times text-xl":"fas fa-bars text-xl")}),document.addEventListener("click",function(i){if(t&&!t.contains(i.target)&&!e.contains(i.target)){t.classList.add("hidden"),e.setAttribute("aria-expanded","false");const o=e.querySelector("i");o&&(o.className="fas fa-bars text-xl")}}),document.querySelectorAll("[data-dropdown]").forEach(i=>{i.addEventListener("keydown",function(o){(o.key==="Enter"||o.key===" ")&&(o.preventDefault(),toggleDropdown(this))})})}function C(e){const t=document.getElementById(e+"-submenu"),n=event.target,i=n.querySelector("i");if(t){t.classList.toggle("hidden");const o=!t.classList.contains("hidden");i&&(i.className=o?"fas fa-chevron-up":"fas fa-chevron-down"),n.setAttribute("aria-expanded",o)}}function h(){document.querySelectorAll("[data-lang]").forEach(t=>{t.addEventListener("click",function(n){n.preventDefault();const i=this.getAttribute("data-lang");p(i)})})}function p(e){localStorage.setItem("preferred-language",e),document.documentElement.lang=e,m(`语言已切换到: ${I(e)}`)}function I(e){return{ar:"العربية",zh:"中文",en:"English",fr:"Français",ru:"Русский",es:"Español"}[e]||e}function b(){document.querySelectorAll('a[href^="#"]').forEach(o=>{o.addEventListener("click",function(c){c.preventDefault();const s=this.getAttribute("href").substring(1),a=document.getElementById(s);a&&a.scrollIntoView({behavior:"smooth",block:"start"})})});const t={threshold:.1,rootMargin:"0px 0px -50px 0px"},n=new IntersectionObserver(function(o){o.forEach(c=>{c.isIntersecting&&(c.target.classList.add("animate-fade-in-up"),n.unobserve(c.target))})},t);document.querySelectorAll(".product-card, .service-card, .case-card").forEach(o=>n.observe(o))}function v(){document.querySelectorAll(".gallery-item").forEach(t=>{t.addEventListener("click",function(){const n=this.querySelector("img");n&&y(n.src,n.alt)}),t.addEventListener("keydown",function(n){(n.key==="Enter"||n.key===" ")&&(n.preventDefault(),this.click())})})}function y(e,t){const n=document.createElement("div");n.className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",n.setAttribute("role","dialog"),n.setAttribute("aria-label","图片查看器");const i=document.createElement("div");i.className="relative max-w-4xl max-h-full p-4";const o=document.createElement("img");o.src=e,o.alt=t,o.className="max-w-full max-h-full object-contain";const c=document.createElement("button");c.innerHTML='<i class="fas fa-times"></i>',c.className="absolute top-4 right-4 text-white text-2xl hover:text-gray-300 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center",c.setAttribute("aria-label","关闭图片查看器"),i.appendChild(o),i.appendChild(c),n.appendChild(i),document.body.appendChild(n);const s=()=>{document.body.removeChild(n),document.body.style.overflow=""};c.addEventListener("click",s),n.addEventListener("click",function(a){a.target===n&&s()}),document.addEventListener("keydown",function a(d){d.key==="Escape"&&(s(),document.removeEventListener("keydown",a))}),document.body.style.overflow="hidden",c.focus()}function x(){document.querySelectorAll("form").forEach(t=>{t.addEventListener("submit",function(n){n.preventDefault(),A(this)})})}function A(e){new FormData(e);const t=e.querySelector('button[type="submit"]');t&&(t.textContent,t.innerHTML='<span class="loading"></span> 提交中...',t.disabled=!0),setTimeout(()=>{m("表单提交成功！我们会尽快与您联系。","success"),e.reset(),t&&(t.textContent=originalText,t.disabled=!1)},2e3)}function m(e,t="info"){const n=document.createElement("div");n.className=`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-sm ${T(t)}`,n.textContent=e;const i=document.createElement("button");i.innerHTML='<i class="fas fa-times"></i>',i.className="ml-2 text-current opacity-70 hover:opacity-100",i.addEventListener("click",()=>f(n)),n.appendChild(i),document.body.appendChild(n),setTimeout(()=>f(n),5e3)}function T(e){const t={info:"bg-blue-500 text-white",success:"bg-green-500 text-white",warning:"bg-yellow-500 text-black",error:"bg-red-500 text-white"};return t[e]||t.info}function f(e){e&&e.parentNode&&(e.style.opacity="0",e.style.transform="translateX(100%)",setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},300))}function L(){const e=document.createElement("a");e.href="#main-content",e.textContent="跳转到主要内容",e.className="skip-link",document.body.insertBefore(e,document.body.firstChild);const t=document.querySelector("main");t&&(t.id="main-content",t.setAttribute("tabindex","-1")),N()}function N(){document.querySelectorAll(".product-card, .service-card, .gallery-item").forEach(t=>{t.hasAttribute("tabindex")||t.setAttribute("tabindex","0"),t.addEventListener("keydown",function(n){(n.key==="Enter"||n.key===" ")&&(n.preventDefault(),this.click())})})}function q(e,t){let n;return function(...o){const c=()=>{clearTimeout(n),e(...o)};clearTimeout(n),n=setTimeout(c,t)}}function E(e,t){let n;return function(){const i=arguments,o=this;n||(e.apply(o,i),n=!0,setTimeout(()=>n=!1,t))}}function w(){document.querySelectorAll(".product-carousel").forEach(t=>{const n=t.querySelectorAll(".carousel-image"),i=t.querySelector(".carousel-prev"),o=t.querySelector(".carousel-next"),c=t.querySelectorAll(".carousel-indicator");let s=0;function a(r){n.forEach((l,u)=>{l.classList.toggle("active",u===r)}),c.forEach((l,u)=>{l.classList.toggle("active",u===r)}),s=r}function d(){const r=(s+1)%n.length;a(r)}function S(){const r=(s-1+n.length)%n.length;a(r)}o&&o.addEventListener("click",d),i&&i.addEventListener("click",S),c.forEach((r,l)=>{r.addEventListener("click",()=>a(l))}),setInterval(d,5e3)})}function k(){const e=document.querySelectorAll("img[data-src]"),t=new IntersectionObserver((n,i)=>{n.forEach(o=>{if(o.isIntersecting){const c=o.target;c.src=c.dataset.src,c.classList.remove("lazy"),i.unobserve(c)}})});e.forEach(n=>t.observe(n))}function B(){const e=document.getElementById("search-input"),t=document.getElementById("search-results");if(!e)return;const n=[{title:"斗轮堆取料机",url:"products/bucket-wheel-stacker.html",category:"产品"},{title:"混匀堆取料机",url:"products/blending-stacker.html",category:"产品"},{title:"板材矫直机",url:"products/plate-straightener.html",category:"产品"},{title:"技术改造",url:"services/technical-renovation.html",category:"服务"},{title:"华隆简介",url:"about/company.html",category:"关于"},{title:"联系我们",url:"about/contact.html",category:"关于"}];e.addEventListener("input",q(function(i){const o=i.target.value.toLowerCase().trim();if(o.length<2){t.innerHTML="",t.classList.add("hidden");return}const c=n.filter(s=>s.title.toLowerCase().includes(o)||s.category.toLowerCase().includes(o));c.length>0?(t.innerHTML=c.map(s=>`
                <a href="${s.url}" class="block px-4 py-2 hover:bg-light-gray">
                    <div class="font-medium">${s.title}</div>
                    <div class="text-sm text-secondary">${s.category}</div>
                </a>
            `).join(""),t.classList.remove("hidden")):(t.innerHTML='<div class="px-4 py-2 text-secondary">未找到相关结果</div>',t.classList.remove("hidden"))},300)),document.addEventListener("click",function(i){!e.contains(i.target)&&!t.contains(i.target)&&t.classList.add("hidden")})}function z(){const e=document.querySelector("header"),t=e.offsetHeight;window.addEventListener("scroll",E(function(){window.scrollY>t?e.classList.add("shadow-xl"):e.classList.remove("shadow-xl")},100))}function M(){const e=document.createElement("button");e.innerHTML='<i class="fas fa-chevron-up"></i>',e.className="fixed bottom-8 right-8 bg-accent text-white w-12 h-12 rounded-full shadow-lg hover:bg-orange-600 transition-all duration-300 opacity-0 invisible z-50",e.setAttribute("aria-label","返回顶部"),document.body.appendChild(e),window.addEventListener("scroll",E(function(){window.scrollY>500?e.classList.remove("opacity-0","invisible"):e.classList.add("opacity-0","invisible")},100)),e.addEventListener("click",function(){window.scrollTo({top:0,behavior:"smooth"})})}function H(){if(localStorage.getItem("cookieConsent"))return;const e=document.createElement("div");e.className="fixed bottom-0 left-0 right-0 bg-dark-gray text-white p-4 z-50 transform translate-y-full transition-transform duration-300",e.innerHTML=`
        <div class="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
            <div class="text-sm">
                <p>我们使用Cookie来改善您的浏览体验。继续使用本网站即表示您同意我们的Cookie政策。</p>
            </div>
            <div class="flex gap-2">
                <button id="accept-cookies" class="bg-accent text-white px-4 py-2 rounded text-sm hover:bg-orange-600 transition-colors">
                    接受
                </button>
                <button id="decline-cookies" class="border border-white text-white px-4 py-2 rounded text-sm hover:bg-white hover:text-dark-gray transition-colors">
                    拒绝
                </button>
            </div>
        </div>
    `,document.body.appendChild(e),setTimeout(()=>{e.classList.remove("translate-y-full")},1e3),document.getElementById("accept-cookies").addEventListener("click",function(){localStorage.setItem("cookieConsent","accepted"),e.remove()}),document.getElementById("decline-cookies").addEventListener("click",function(){localStorage.setItem("cookieConsent","declined"),e.remove()})}function O(){const e=document.createElement("div");e.id="page-loader",e.className="fixed inset-0 bg-white z-50 flex items-center justify-center",e.innerHTML=`
        <div class="text-center">
            <div class="loading mb-4"></div>
            <p class="text-primary font-medium">加载中...</p>
        </div>
    `,document.body.appendChild(e),window.addEventListener("load",function(){setTimeout(()=>{e.style.opacity="0",setTimeout(()=>e.remove(),300)},500)})}document.addEventListener("DOMContentLoaded",function(){g(),h(),b(),v(),x(),L(),w(),k(),B(),z(),M(),H()});document.readyState==="loading"&&O();window.HualongWebsite={toggleMobileSubmenu:C,switchLanguage:p,openLightbox:y,showNotification:m,initializeProductCarousel:w,initializeLazyLoading:k};
