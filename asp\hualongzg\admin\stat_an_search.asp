<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
'if session.Contents("master")=false and mlevel<4 then Response.Redirect "stat_help.asp?id=004&error=您没有查看自定义检索记录和进行自定义检索的的权限。"
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>自定义统计</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>在下面的表单填写查询条件，支持组合查询和模糊查询。</font>
  </td></tr>
</table>
<br>
<%
'检查权限
if session.Contents("master")=true or mlevel>=5 then
%>
<%
dim OS(7)
	OS(0)="Win 2000"
	OS(1)="Win XP"
	OS(2)="Win NT"
	OS(3)="Win 9x"
	OS(4)="类Unix"
	OS(5)="Mac"
	OS(6)="Other"

dim soft(7)
	soft(0)="NetCaptor"
	soft(1)="MSIE 6.x"
	soft(2)="MSIE 5.x"
	soft(3)="MSIE 4.x"
	soft(4)="Netscape"
	soft(5)="Opera"
	soft(6)="Other"
%>
<table border="0" cellpadding="0" cellspacing="0" width="540" align=center class=table0>
<form action="stat_an_searchgo.asp" method="post" name="search">
<tr><td class=td0><div class=p12>
	时 间 段：<input name="onyear" class="input" size="5"> 年
			  <input name="onmonth" class="input" size="3"> 月
			  <input name="onday" class="input" size="3"> 日
		   ～ <input name="offyear" class="input" size="5"> 年
			  <input name="offmonth" class="input" size="3"> 月
			  <input name="offday" class="input" size="3"> 日
<br>IP 地 址：<input name="vip" class="input" size="16">
<br>来自地区：<input name="vwhere" class="input" size="25">
<br>客 户 端：<SELECT name="vOS1" class="input" style="width:70" id="vos1">
			  <OPTION value="" selected>操作系统</OPTION><%for i=0 to 6%>
			  <OPTION value="<%=OS(i)%>"><%=OS(i)%></OPTION><%next%>
			  </SELECT>
			  <INPUT name="vOS2" class="input" size="10" id="vos">&nbsp; 

			  <SELECT name="vsoft1" class="input" style="width:70">
			  <OPTION value="" selected>浏览器</OPTION><%for i=0 to 6%>
			  <OPTION value="<%=soft(i)%>"><%=soft(i)%></OPTION><%next%>
			  </SELECT>
			  <INPUT name="vsoft2" class="input" size="10">

<br>来自网页：<input name="vcome" class="input" size="35">
<br>被访网页：<input name="vpage" class="input" size="35">
<br>输出类型：<INPUT type="checkbox" name="outtype" value="小时">小时　　
				<INPUT type="checkbox" name="outtype" value="日">日　　　
				<INPUT type="checkbox" name="outtype" value="周">周　　　
				<INPUT type="checkbox" name="outtype" value="月">月
				<br>　　　　　<INPUT type="checkbox" name="outtype" value="年">年　　　
				<INPUT type="checkbox" name="outtype" value="IP">IP　　　
				<INPUT type="checkbox" name="outtype" value="地区">地区　　
				<INPUT type="checkbox" name="outtype" value="浏览器">浏览器　
				<br>　　　　　<INPUT type="checkbox" name="outtype" value="操作系统">操作系统
				<INPUT type="checkbox" name="outtype" value="来路">来路　　
				<INPUT type="checkbox" name="outtype" value="页面">页面　　
				<INPUT type="checkbox" name="outtype" value="详细">详细
</div></td></tr>
<tr><td class=td0><br><input type="submit" name="search" class="backc2" value="提交查询"></td></tr>
</form>
</table>
<br>
<%
end if		'检查权限

'显示原来保存的自定义检索
'打开数据库
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")

sql="select * from save"
rs.Open sql,conn,1,1

if not rs.EOF then
%>
<table width="540" border="0" cellspacing=0 cellpadding=0 align=center class=table0>
  <tr><td colspan=2 class=td0 width="100%"><hr class=hr4></td></tr>
  <tr><td colspan=2 class=td0 width="100%"><font class=p14><u>已保存的检索条件</u></font></td></tr>
  <tr height="1"><td class=td0 colspan=2><img src="stat_images/touming.gif" width="1" height="1"></td></tr>
<%do while not rs.EOF%>
  <form action="stat_an_searchgo.asp" method=post name="frm_s_<%=rs("id")%>">
  <tr height="28">
    <td class=td0>
      □ <font class=p12><%=rs("name")%></font>
    </td>
    <td class=td0 align=right>
      <input name="content" size="50" type="hidden" value="<%=rs("content")%>"
      ><input type="hidden" name="wherestr" value="<%=rs("wherestr")%>"
      ><input type="hidden" name="outtype" value="<%=rs("outtype")%>"
      ><input type="hidden" name="name" value="<%=rs("name")%>"
      ><%if session.Contents("master")=true or mlevel>=6 then%><a href="stat_an_save_del.asp?id=<%=rs("id")%>"><font class=p12>删除</font></a> <%end if%> <a href='javascript:document.frm_s_<%=rs("id")%>.submit();'><font class=p12>查看结果</font></a>
    </td>
  </tr>
  <%if rs("content") <> "" then%>
  <tr>
    <td class=td0 colspan=2><font class=p12>　 备注：<%=rs("content")%></font></td>
  </tr>
  <%end if%>
  <tr height="1"><td class=td0 colspan=2><img src="stat_images/touming.gif" width="1" height="1"></td></tr>
  </form>
<%
rs.MoveNext
loop%>
</table>
<%
end if

rs.Close
set rs=nothing
conn.Close
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->