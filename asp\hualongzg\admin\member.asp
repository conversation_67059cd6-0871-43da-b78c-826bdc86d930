<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) AND (inint(session("flag"),"8") = false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%if request.querystring("id") <> "" then
	this_top = request.querystring("id")
end if%>
<%
' *** Edit Operations: declare variables

MM_editAction = CStr(Request("URL"))
If (Request.QueryString <> "") Then
  MM_editAction = MM_editAction & "?" & Request.QueryString
End If

' boolean to abort record edit
MM_abortEdit = false

' query string to execute
MM_editQuery = ""
%>
<%
' *** Delete Record: declare variables

if (CStr(Request("MM_delete")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  MM_editConnection = MM_conn_STRING
  MM_editTable = "member"
  MM_editColumn = "id"
  MM_recordId = "" + Request.Form("MM_recordId") + ""
  MM_editRedirectUrl = "member.asp"

  ' append the query string to the redirect URL
  If (MM_editRedirectUrl <> "" And Request.QueryString <> "") Then
    If (InStr(1, MM_editRedirectUrl, "?", vbTextCompare) = 0 And Request.QueryString <> "") Then
      MM_editRedirectUrl = MM_editRedirectUrl & "?" & Request.QueryString
    Else
      MM_editRedirectUrl = MM_editRedirectUrl & "&" & Request.QueryString
    End If
  End If
  
End If
%>
<%
' *** Delete Record: construct a sql delete statement and execute it

If (CStr(Request("MM_delete")) <> "" And CStr(Request("MM_recordId")) <> "") Then

  ' create the sql delete statement
  MM_editQuery = "delete from " & MM_editTable & " where " & MM_editColumn & " = " & MM_recordId

  If (Not MM_abortEdit) Then
    ' execute the delete
    Set MM_editCmd = Server.CreateObject("ADODB.Command")
    MM_editCmd.ActiveConnection = MM_editConnection
    MM_editCmd.CommandText = MM_editQuery
    MM_editCmd.Execute
    MM_editCmd.ActiveConnection.Close

    If (MM_editRedirectUrl <> "") Then
      Response.Redirect(MM_editRedirectUrl)
    End If
  End If

End If
%>
<%
set rsMEMBERLIST = Server.CreateObject("ADODB.Recordset")
rsMEMBERLIST.ActiveConnection = MM_conn_STRING
if  request.querystring("id") <> "" then
	rsMEMBERLIST.Source = "SELECT * FROM member where xlid=" & request.querystring("id") & " ORDER BY add_data DESC"
	else
	rsMEMBERLIST.Source = "SELECT * FROM member ORDER BY add_data DESC"
end if
rsMEMBERLIST.CursorType = 0
rsMEMBERLIST.CursorLocation = 2
rsMEMBERLIST.LockType = 3
rsMEMBERLIST.Open()
rsMEMBERLIST_numRows = 0
%>
<%
Dim Repeat1__numRows
Repeat1__numRows = -1
Dim Repeat1__index
Repeat1__index = 0
rsMEMBERLIST_numRows = rsMEMBERLIST_numRows + Repeat1__numRows
%>
<%'分页处理
If Not rsMEMBERLIST.EOF Or Not rsMEMBERLIST.BOF Then
'  *** Recordset Stats, Move To Record, and Go To Record: declare stats variables

' set the record count
rsMEMBERLIST_total = rsMEMBERLIST.RecordCount

' set the number of rows displayed on this page
If (rsMEMBERLIST_numRows < 0) Then
  rsMEMBERLIST_numRows = rsMEMBERLIST_total
Elseif (rsMEMBERLIST_numRows = 0) Then
  rsMEMBERLIST_numRows = 1
End If

' set the first and last displayed record
rsMEMBERLIST_first = 1
rsMEMBERLIST_last  = rsMEMBERLIST_first + rsMEMBERLIST_numRows - 1

' if we have the correct record count, check the other stats
If (rsMEMBERLIST_total <> -1) Then
  If (rsMEMBERLIST_first > rsMEMBERLIST_total) Then rsMEMBERLIST_first = rsMEMBERLIST_total
  If (rsMEMBERLIST_last > rsMEMBERLIST_total) Then rsMEMBERLIST_last = rsMEMBERLIST_total
  If (rsMEMBERLIST_numRows > rsMEMBERLIST_total) Then rsMEMBERLIST_numRows = rsMEMBERLIST_total
End If
%>
<%
' *** Recordset Stats: if we don't know the record count, manually count them

If (rsMEMBERLIST_total = -1) Then

  ' count the total records by iterating through the recordset
  rsMEMBERLIST_total=0
  While (Not rsMEMBERLIST.EOF)
    rsMEMBERLIST_total = rsMEMBERLIST_total + 1
    rsMEMBERLIST.MoveNext
  Wend

  ' reset the cursor to the beginning
  If (rsMEMBERLIST.CursorType > 0) Then
    rsMEMBERLIST.MoveFirst
  Else
    rsMEMBERLIST.Requery
  End If

  ' set the number of rows displayed on this page
  If (rsMEMBERLIST_numRows < 0 Or rsMEMBERLIST_numRows > rsMEMBERLIST_total) Then
    rsMEMBERLIST_numRows = rsMEMBERLIST_total
  End If

  ' set the first and last displayed record
  rsMEMBERLIST_first = 1
  rsMEMBERLIST_last = rsMEMBERLIST_first + rsMEMBERLIST_numRows - 1
  If (rsMEMBERLIST_first > rsMEMBERLIST_total) Then rsMEMBERLIST_first = rsMEMBERLIST_total
  If (rsMEMBERLIST_last > rsMEMBERLIST_total) Then rsMEMBERLIST_last = rsMEMBERLIST_total

End If
%>
<%
' *** Move To Record and Go To Record: declare variables

Set MM_rs    = rsMEMBERLIST
MM_rsCount   = rsMEMBERLIST_total
MM_size      = rsMEMBERLIST_numRows
MM_uniqueCol = ""
MM_paramName = ""
MM_offset = 0
MM_atTotal = false
MM_paramIsDefined = false
If (MM_paramName <> "") Then
  MM_paramIsDefined = (Request.QueryString(MM_paramName) <> "")
End If
%>
<%
' *** Move To Record: handle 'index' or 'offset' parameter

if (Not MM_paramIsDefined And MM_rsCount <> 0) then

  ' use index parameter if defined, otherwise use offset parameter
  r = Request.QueryString("index")
  If r = "" Then r = Request.QueryString("offset")
  If r <> "" Then MM_offset = Int(r)

  ' if we have a record count, check if we are past the end of the recordset
  If (MM_rsCount <> -1) Then
    If (MM_offset >= MM_rsCount Or MM_offset = -1) Then  ' past end or move last
      If ((MM_rsCount Mod MM_size) > 0) Then         ' last page not a full repeat region
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' move the cursor to the selected record
  i = 0
  While ((Not MM_rs.EOF) And (i < MM_offset Or MM_offset = -1))
    MM_rs.MoveNext
    i = i + 1
  Wend
  If (MM_rs.EOF) Then MM_offset = i  ' set MM_offset to the last possible record

End If
%>
<%
' *** Move To Record: if we dont know the record count, check the display range

If (MM_rsCount = -1) Then

  ' walk to the end of the display range for this page
  i = MM_offset
  While (Not MM_rs.EOF And (MM_size < 0 Or i < MM_offset + MM_size))
    MM_rs.MoveNext
    i = i + 1
  Wend

  ' if we walked off the end of the recordset, set MM_rsCount and MM_size
  If (MM_rs.EOF) Then
    MM_rsCount = i
    If (MM_size < 0 Or MM_size > MM_rsCount) Then MM_size = MM_rsCount
  End If

  ' if we walked off the end, set the offset based on page size
  If (MM_rs.EOF And Not MM_paramIsDefined) Then
    If (MM_offset > MM_rsCount - MM_size Or MM_offset = -1) Then
      If ((MM_rsCount Mod MM_size) > 0) Then
        MM_offset = MM_rsCount - (MM_rsCount Mod MM_size)
      Else
        MM_offset = MM_rsCount - MM_size
      End If
    End If
  End If

  ' reset the cursor to the beginning
  If (MM_rs.CursorType > 0) Then
    MM_rs.MoveFirst
  Else
    MM_rs.Requery
  End If

  ' move the cursor to the selected record
  i = 0
  While (Not MM_rs.EOF And i < MM_offset)
    MM_rs.MoveNext
    i = i + 1
  Wend
End If
%>
<%
' *** Move To Record: update recordset stats

' set the first and last displayed record
rsMEMBERLIST_first = MM_offset + 1
rsMEMBERLIST_last  = MM_offset + MM_size
If (MM_rsCount <> -1) Then
  If (rsMEMBERLIST_first > MM_rsCount) Then rsMEMBERLIST_first = MM_rsCount
  If (rsMEMBERLIST_last > MM_rsCount) Then rsMEMBERLIST_last = MM_rsCount
End If

' set the boolean used by hide region to check if we are on the last record
MM_atTotal = (MM_rsCount <> -1 And MM_offset + MM_size >= MM_rsCount)
%>
<%
' *** Go To Record and Move To Record: create strings for maintaining URL and Form parameters

' create the list of parameters which should not be maintained
MM_removeList = "&index="
If (MM_paramName <> "") Then MM_removeList = MM_removeList & "&" & MM_paramName & "="
MM_keepURL="":MM_keepForm="":MM_keepBoth="":MM_keepNone=""

' add the URL parameters to the MM_keepURL string
For Each Item In Request.QueryString
  NextItem = "&" & Item & "="
  If (InStr(1,MM_removeList,NextItem,1) = 0) Then
    MM_keepURL = MM_keepURL & NextItem & Server.URLencode(Request.QueryString(Item))
  End If
Next

' add the Form variables to the MM_keepForm string
For Each Item In Request.Form
  NextItem = "&" & Item & "="
  If (InStr(1,MM_removeList,NextItem,1) = 0) Then
    MM_keepForm = MM_keepForm & NextItem & Server.URLencode(Request.Form(Item))
  End If
Next

' create the Form + URL string and remove the intial '&' from each of the strings
MM_keepBoth = MM_keepURL & MM_keepForm
if (MM_keepBoth <> "") Then MM_keepBoth = Right(MM_keepBoth, Len(MM_keepBoth) - 1)
if (MM_keepURL <> "")  Then MM_keepURL  = Right(MM_keepURL, Len(MM_keepURL) - 1)
if (MM_keepForm <> "") Then MM_keepForm = Right(MM_keepForm, Len(MM_keepForm) - 1)

' a utility function used for adding additional parameters to these strings
Function MM_joinChar(firstItem)
  If (firstItem <> "") Then
    MM_joinChar = "&"
  Else
    MM_joinChar = ""
  End If
End Function
%>
<%
' *** Move To Record: set the strings for the first, last, next, and previous links

MM_keepMove = MM_keepBoth
MM_moveParam = "index"

' if the page has a repeated region, remove 'offset' from the maintained parameters
If (MM_size > 0) Then
  MM_moveParam = "offset"
  If (MM_keepMove <> "") Then
    params = Split(MM_keepMove, "&")
    MM_keepMove = ""
    For i = 0 To UBound(params)
      nextItem = Left(params(i), InStr(params(i),"=") - 1)
      If (StrComp(nextItem,MM_moveParam,1) <> 0) Then
        MM_keepMove = MM_keepMove & "&" & params(i)
      End If
    Next
    If (MM_keepMove <> "") Then
      MM_keepMove = Right(MM_keepMove, Len(MM_keepMove) - 1)
    End If
  End If
End If

' set the strings for the move to links
If (MM_keepMove <> "") Then MM_keepMove = MM_keepMove & "&"
urlStr = Request.ServerVariables("URL") & "?" & MM_keepMove & MM_moveParam & "="
MM_moveFirst = urlStr & "0"
MM_moveLast  = urlStr & "-1"
MM_moveNext  = urlStr & Cstr(MM_offset + MM_size)
prev = MM_offset - MM_size
If (prev < 0) Then prev = 0
MM_movePrev  = urlStr & Cstr(prev)
end if '分页处理
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<h1 class="b_ch1">在线报名管理<font class="p12 nopr" style="font-family: '宋体'" color=#ff0000>（提示：单击姓名查看详细信息）</font></h1>
<div class="nopr" style="padding-right:30px" align=right><a href="javascript:window.print()">&lt;&lt; 打印</a></div>
<script language="javascript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
function GP_popupConfirmMsg(msg) { //v1.0
  document.MM_returnValue = confirm(msg);
}
//-->
</script>
<select style="width:200px" name="select"onChange="MM_jumpMenu('self',this,0)" >
<option value=member.asp>所有学历</option>
<%
Set rs = Server.CreateObject("ADODB.Recordset")
sql = "SELECT xueli.xlid as id, xueli.xlname as name,count(member.id) as shl FROM (xueli left join member on xueli.xlid=member.xlid) group by xueli.xlid, xueli.xlname, member.xlid ORDER BY xueli.xlid asc"
rs.open sql,MM_conn_STRING,1,1
while not rs.eof%>
<option value="member.asp?id=<%=(rs("id"))%>"<%if cint(this_top)=rs("id") then response.write(" selected style='color:#ff0000'")%>><%=(rs("name"))%>(<%=(rs("shl"))%>)</option>
<% 
  rs.MoveNext()
Wend
rs.Close()
Set rs = Nothing
%></select>
	  <% If Not rsMEMBERLIST.EOF Or Not rsMEMBERLIST.BOF Then %>
	  <table width="655" border="1" cellspacing="0" cellpadding="4" bordercolor="#AD9E63">
		<tr bgcolor="eeeeee" align="center"> 
		  <td class=p12 height="27" class=pr>姓名</td>
		  <td class=p12 height="27">性别</td>
		  <td class=p12 height="27">民族</td>
<!-- 		  <td class=p12 height="27">电子信箱</td> -->
		  <td class=p12 height="27">考生号（准考证号）</td>
<!-- 		  <td class=p12 height="27">身份证号</td> -->
		  <td class=p12 height="27">联系电话</td>
		  <td class=p12 height="27">通讯地址</td>
		  <td class=p12 height="27">第一志愿</td>
		  <td class=p12 height="27">第二志愿</td>		  		  
		  <td class=p12 height="27" class=nopr>&nbsp;</td>
		</tr>
		<% 
While ((Repeat1__numRows <> 0) AND (NOT rsMEMBERLIST.EOF)) 
%>
		<form name="form1" method="POST" action="<%=MM_editAction%>">
		  <input type="hidden" name="MM_recordId" value="<%= rsMEMBERLIST.Fields.Item("id").Value %>">
		  <tr> 
			<td class=p12 nowrap> 
			  <%
	dim strNAME
	strNAME=rsMEMBERLIST.Fields.Item("this_name").Value
	if trim(strName) <> "" then %>
	<a title="单击查看这位学生的详细信息" href="member_detail.asp?id=<%=(rsMEMBERLIST.Fields.Item("id").Value)%>"><%=(strNAME)%></a>
	<%else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td class=p12> 
			  <%
	dim strJOB
	strJOB = rsMEMBERLIST.Fields.Item("sex").Value
	if trim(strJOB) <> "" then
	response.write(strJOB)
	else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td class=p12><%=rsMEMBERLIST.Fields.Item("this_mingzu").Value%></td>
<!-- 			<td class=p12> 
			  <%
	dim strEMAIL
	strEMAIL = rsMEMBERLIST.Fields.Item("email").Value
	if trim(strEMAIL) <> "" then
	response.write "<a title='发信给这位学生' href='mailto:" & strEMAIL &"'>" & strEMAIL & "</a>"
	else
	response.write "&nbsp;"
	end if
	%>
			</td> -->
			<td class=p12> 
			  <%
	if trim(rsMEMBERLIST.Fields.Item("this_no").Value) = "" or isnull(rsMEMBERLIST.Fields.Item("this_no").Value) then
	response.write "&nbsp;"
	else
		response.write(rsMEMBERLIST.Fields.Item("this_no").Value)
	end if
	%>
			</td>
<!-- 		<td class=p12> 
			  <%
	dim strCOM
	strCOM = rsMEMBERLIST.Fields.Item("sfzhm").Value
	if trim(strCOM) = "" then
	response.write "&nbsp;"
	else
		response.write strCOM
	end if
	%>
			</td> -->
			<td class=p12> 
			  <%
	if trim(rsMEMBERLIST.Fields.Item("phome").Value) <> "" then
	response.write(trim(rsMEMBERLIST.Fields.Item("phome").Value))
	else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td class=p12> 
			  <%
	if trim(rsMEMBERLIST.Fields.Item("address").Value) <> "" then
	response.write(trim(rsMEMBERLIST.Fields.Item("address").Value))
	else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td class=p12> 
			  <%
	if trim(rsMEMBERLIST.Fields.Item("zhuanye1").Value) <> "" then
	response.write(trim(rsMEMBERLIST.Fields.Item("zhuanye1").Value))
	else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td class=p12> 
			  <%
	if trim(rsMEMBERLIST.Fields.Item("zhuanye2").Value) <> "" then
	response.write(trim(rsMEMBERLIST.Fields.Item("zhuanye2").Value))
	else
	response.write "&nbsp;"
	end if
	%>
			</td>
			<td align=center class="p12 nopr"> 
			  <input name="submit" type="submit" value="删除" onClick="GP_popupConfirmMsg('确定删除考生“<%=rsMEMBERLIST.Fields.Item("this_name").Value%>”的资料？');return document.MM_returnValue">
			  <input type="hidden" name="MM_delete" value="true">
			</td>
		  </tr>
		</form>
		<% 
  Repeat1__index=Repeat1__index+1
  Repeat1__numRows=Repeat1__numRows-1
  rsMEMBERLIST.MoveNext()
Wend
%>
	  </table>
	  <br>
	  <%else%>
	  <br><br>还没有在线报名的学生 
	  <% End If ' end Not rsMEMBERLIST.EOF Or NOT rsMEMBERLIST.BOF %>
	  <% if rsMEMBERLIST_total > MM_size then
For i = 1 to rsMEMBERLIST_total Step MM_size
TM_counter = TM_counter + 1
TM_PageEndCount = i + MM_size - 1
if TM_PageEndCount > rsMEMBERLIST_total Then TM_PageEndCount = rsMEMBERLIST_total
if i <> MM_offset + 1 then
Response.Write("<a class=p12 href=" & Request.ServerVariables("URL") & "?" & MM_keepMove & "offset=" & i-1 & ">")
Response.Write("第" & TM_counter & "页</a>")
else
Response.Write("<b><font class=p12>第" & TM_counter & "页</font></b>")
End if
if(TM_PageEndCount <> rsMEMBERLIST_total) then Response.Write(" | ")
next
end if
 %>
<div style="padding-right:30px" align=right><a href="javascript:window.print()">&lt;&lt; 打印</a></div>
<!--#include file ="_bottom.asp"-->
<%
rsMEMBERLIST.Close()
%>
