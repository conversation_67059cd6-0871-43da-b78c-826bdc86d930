<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%Response.CharSet = "utf-8"
pStr = "private, no-cache, must-revalidate" 
Response.ExpiresAbsolute = #2000-01-01# 
Response.AddHeader "pragma", "no-cache" 
Response.AddHeader "cache-control", pStr 
%>
<% If (Session("isadmin") <> true) Then
response.write"<div class=""p14"">登录过期，或者没有权限，请重新登录</div>"
response.end
End if%> 
<!--#include file="../Connections/conn.asp" -->
<!--#include file ="_config.asp"-->
<%If request.querystring("action") = "ok" Then
	If request.querystring("id") <> "" then
		sql_command("update list inner join area on list.cate_id=area.cate_id set list.is_top = true where list.id in(" & request.querystring("id") & ") and left(area.cate_lcode,len('" & request.querystring("lcode") & "'))='" & request.querystring("lcode") & "'")
		sql_command("update list inner join area on list.cate_id=area.cate_id set list.is_top = false where list.id not in(" & request.querystring("id") & ") and left(area.cate_lcode,len('" & request.querystring("lcode") & "'))='" & request.querystring("lcode") & "'")
	Else
		sql_command("update list inner join area on list.cate_id=area.cate_id set list.is_top = false where left(area.cate_lcode,len('" & request.querystring("lcode") & "'))='" & request.querystring("lcode") & "'")
	End if
End If
%>