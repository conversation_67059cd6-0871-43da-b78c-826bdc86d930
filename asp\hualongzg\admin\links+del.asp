<% @LANGUAGE="VBSCRIPT" %>
<% If (Session("isadmin") <> true) Then Response.Redirect("../admin/") %>
<!--#include file="../Connections/conn.asp" -->
<%
dim strID
strID=Request("id")
if strID <>"" then

call del_page_img(strID,"img")

sub del_page_img(pageids,folder)
	'删除页面里的文件，参数为字符串pageid组,父目录名称（不带\）
	dim rs,sql,imgs,i
	imgs = ""
	set rs=server.createobject("adodb.recordset") 
	sql="select img from link_exchang WHERE id IN (" & pageids & ")"
	rs.open sql,MM_conn_STRING,1,1 
	while not rs.eof
		for i=0 to 0
			if rs(i) <> "" AND not isnull(rs(i)) then
				if imgs <> "" then
				imgs = imgs & "|" & rs(i)
				else
				imgs = rs(i)
				end if
			end if
		next
		rs.movenext()
	wend
	rs.close()
	set rs=nothing

	if imgs <> "" then
		dim File,fdir,ImagePath
		Set File = CreateObject("Scripting.FileSystemObject")
		fdir = server.mappath(Request.ServerVariables("PATH_INFO"))
		set fdir = File.getfile(fdir)
		set fdir = fdir.parentfolder
		set fdir = fdir.parentfolder								'获取父父目录

		dim imgs_str
		imgs_str = split(imgs,"|")
		for i = lbound(imgs_str) to ubound(imgs_str)
			ImagePath = fdir & "\" & folder & "\" & imgs_str(i)
			if file.FileExists(ImagePath) then File.DeleteFile(ImagePath)
		next
	end if'删除旧图或者媒体文件
end sub

dim sql
sql="DELETE FROM link_exchang WHERE id IN "   
sql=sql &"(" & strID & ")"
set Command1 = Server.CreateObject("ADODB.Command")
Command1.ActiveConnection = MM_conn_STRING
Command1.CommandText = sql
Command1.Execute()
Response.Redirect("links3.asp")
 else Response.redirect("links3.asp")
 end if %>
<html>
<head>
<title>ok</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>

<body bgcolor="#FFFFFF" text="#000000">
</body>
</html>
