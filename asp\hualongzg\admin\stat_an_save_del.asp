<%@LANGUAGE="VBSCRIPT" codepage="65001"%>

<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'权限检查
if session.Contents("master")=false and mlevel< 6 then Response.Redirect "stat_help.asp?id=004&error=您没有管理自定义检索条件库的权限。"

'获取数据
id			=trim(Request("id"))
isok		=trim(Request("isok"))
if isok="" then isok=0
isok=cbool(isok)

'检查数据
if id="" then Response.Redirect "stat_help.asp?id=003&error=请指明您要删除哪条记录。"
'打开数据库
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath
Set rs = Server.CreateObject("ADODB.Recordset")

sql="select * from save where id=" & id
rs.Open sql,conn,3,2

if rs.EOF then
	rs.Close
	set rs=nothing
	conn.Close
	set conn=nothing
	Response.Redirect "stat_help.asp?id=003&error=您要删除的记录并不存在。"
end if

%>
<!--#include file="stat__head.asp"-->
<%
if isok=false then
%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  <form action="stat_an_save_del.asp" method=post id=form1 name=form1>
  <tr height="30">
    <td class=td0 width="100%"><font class=p14><u>删除检索条件</u><br><br></font><font color=ff0000 class=p12>注　　意：
	您即将删除名为“<%=rs("name")%>”的检索条件记录</font><br><br><% if ssql_view =1 then%>
<font class=p12>检索条件</font>&nbsp;
	<%if rs("wherestr")="" then%><font class=p12>没有检索条件</font><%else%><font class=p12><%=rs("wherestr")%></font><%end if%><br><%end if'ssql_view=1%>
<font class=p12>查询项目&nbsp; <%=rs("outtype")%></font>
	<p class="p1" style='margin-top: 0;'><font class=p12>说　　明&nbsp; <%=rs("content")%></font><br>
	<input name="id" size="50" type="hidden" value="<%=id%>">
	<input name="isok" size="50" type="hidden" value="1"><br>
	</td></tr>
<tr><td class=td014 align=right>
	<a href='javascript:history.back()'><font class=p14>取消</font></a> 
	<a href='javascript:document.form1.submit();'><font class=p14>确认删除</font></a>
</td>
  </tr>
  </form>

</table>
<br>
<%
rs.Close
else

	rs.delete

	rs.Update
	rs.Close
%>
<table class=table0 width="540" cellspacing="0" align="center" cellpadding="0" border="0">
  <form action="stat_an_save.asp" method=post id=form1 name=form1>
  <tr height="30">
    <td class=td0 width="100%"><font class=p14>成功的删除该检索条件</font>
	<p class="p14" align="right"><a href='stat_an_search.asp'>继续</a></td>
  </tr>
  </form>

</table>
<%
end if

set rs=nothing
conn.Close
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->