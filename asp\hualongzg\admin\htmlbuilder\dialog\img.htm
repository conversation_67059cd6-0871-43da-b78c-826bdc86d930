<HTML>
<HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style type="text/css">
body, a, table, div, span, td, th, input, select{font:9pt;font-family: "宋体", Verdana, Arial, Helvetica, sans-serif;}
body {padding:5px}
</style>

<script language="JavaScript" src="dialog.js"></script>

<script language="JavaScript">
var sAction = "INSERT";
var sTitle = "插入";

var oControl;
var oSeletion;
var sRangeType;

var sFromUrl = "http://";
var sAlt = "";
var sBorder = "0";
var sBorderColor = "";
var sFilter = "";
var sAlign = "";
var sWidth = "";
var sHeight = "";
var sVSpace = "";
var sHSpace = "";

var sCheckFlag = "file";

oSelection = dialogArguments.htmlbuilder.document.selection.createRange();
sRangeType = dialogArguments.htmlbuilder.document.selection.type;

if (sRangeType == "Control") {
	if (oSelection.item(0).tagName == "IMG"){
		sAction = "MODI";
		sTitle = "修改";
		sCheckFlag = "url";
		oControl = oSelection.item(0);
		sFromUrl = oControl.getAttribute("src", 2);
		sAlt = oControl.alt;
		sBorder = oControl.border;
		sBorderColor = oControl.style.borderColor;
		sFilter = oControl.style.filter;
		sAlign = oControl.align;
		sWidth = oControl.width;
		sHeight = oControl.height;
		sVSpace = oControl.vspace;
		sHSpace = oControl.hspace;
	}
}


document.write("<title>图片属性（" + sTitle + "）</title>");


// 初始值
function InitDocument(){
	SearchSelectValue(d_filter, sFilter);
	SearchSelectValue(d_align, sAlign.toLowerCase());

	d_fromurl.value = sFromUrl;
	d_alt.value = sAlt;
	d_border.value = sBorder;
	d_bordercolor.value = sBorderColor;
	s_bordercolor.style.backgroundColor = sBorderColor;
	d_width.value = sWidth;
	d_height.value = sHeight;
	d_vspace.value = sVSpace;
	d_hspace.value = sHSpace;
}


// 图片来源单选点击事件
function RadioClick(what){
	if (what=="url"){
		d_checkfromfile.checked=false;
		d_fromurl.disabled=false;
		d_checkfromurl.checked=true;
		d_file.myform.uploadfile.disabled=true;
	}else{
		d_checkfromurl.checked=false;
		d_file.myform.uploadfile.disabled=false;
		d_checkfromfile.checked=true;
		d_fromurl.disabled=true;
	}
}

// 上传帧调入完成时执行
function UploadLoaded(){
	// 初始radio
	RadioClick(sCheckFlag);
}

// 上传错误
function UploadError(sErrDesc){
	AbleItems();
	RadioClick('file');
	divProcessing.style.display="none";
	try {
		BaseAlert(d_file.myform.uploadfile,sErrDesc);
	}
	catch(e){}
}

// 文件上传完成时执行,带入上传文件名
function UploadSaved(sPathFileName){
	d_fromurl.value = sPathFileName;
	ReturnValue();
}

// 本窗口返回值
function ReturnValue(){
	sFromUrl = d_fromurl.value;
	sAlt = d_alt.value;
	sBorder = d_border.value;
	sBorderColor = d_bordercolor.value;
	sFilter = d_filter.options[d_filter.selectedIndex].value;
	sAlign = d_align.value;
	sWidth = d_width.value;
	sHeight = d_height.value;
	sVSpace = d_vspace.value;
	sHSpace = d_hspace.value;

	if (sAction == "MODI") {
		oControl.src = sFromUrl;
		oControl.alt = sAlt;
		oControl.border = sBorder;
		oControl.style.borderColor = sBorderColor;
		oControl.style.filter = sFilter;
		oControl.align = sAlign;
		oControl.width = sWidth;
		oControl.height = sHeight;
		oControl.style.width = sWidth;
		oControl.style.height = sHeight;
		oControl.vspace = sVSpace;
		oControl.hspace = sHSpace;
	}else{
		var sHTML = '';
		if (sFilter!=""){
			sHTML=sHTML+'filter:'+sFilter+';';
		}
		if (sBorderColor!=""){
			sHTML=sHTML+'border-color:'+sBorderColor+';';
		}
		if (sHTML!=""){
			sHTML=' style="'+sHTML+'"';
		}
		sHTML = '<img id=htmlbuilder_TempElement_Img src="'+sFromUrl+'"'+sHTML;
		if (sBorder!=""){
			sHTML=sHTML+' border="'+sBorder+'"';
		}
		if (sAlt!=""){
			sHTML=sHTML+' alt="'+sAlt+'"';
		}
		if (sAlign!=""){
			sHTML=sHTML+' align="'+sAlign+'"';
		}
		if (sWidth!=""){
			sHTML=sHTML+' width="'+sWidth+'"';
		}
		if (sHeight!=""){
			sHTML=sHTML+' height="'+sHeight+'"';
		}
		if (sVSpace!=""){
			sHTML=sHTML+' vspace="'+sVSpace+'"';
		}
		if (sHSpace!=""){
			sHTML=sHTML+' hspace="'+sHSpace+'"';
		}
		sHTML=sHTML+'>';
		dialogArguments.insertHTML(sHTML);

		var oTempElement = dialogArguments.htmlbuilder.document.getElementById("htmlbuilder_TempElement_Img");
		oTempElement.src = sFromUrl;
		oTempElement.removeAttribute("id");

	}

	window.returnValue = null;
	window.close();
}

// 点确定时执行
function ok(){
	// 数字型输入的有效性
	d_border.value = ToInt(d_border.value);
	d_width.value = ToInt(d_width.value);
	d_height.value = ToInt(d_height.value);
	d_vspace.value = ToInt(d_vspace.value);
	d_hspace.value = ToInt(d_hspace.value);
	// 边框颜色的有效性
	if (!IsColor(d_bordercolor.value)){
		BaseAlert(d_bordercolor,'提示：\n\n无效的边框颜色值！');
		return false;
	}
	
	if (d_checkfromurl.checked){
		// 返回值
		ReturnValue();
	}else{
		// 上传文件判断
		if (!d_file.CheckUploadForm()) return false;
		// 使各输入框无效
		DisableItems();
		// 显示正在上传图片
		divProcessing.style.display="";
		// 上传表单提交
		d_file.myform.submit();
	}
}

// 使所有输入框无效
function DisableItems(){
	d_checkfromfile.disabled=true;
	d_checkfromurl.disabled=true;
	d_fromurl.disabled=true;
	d_alt.disabled=true;
	d_border.disabled=true;
	d_bordercolor.disabled=true;
	d_filter.disabled=true;
	d_align.disabled=true;
	d_width.disabled=true;
	d_height.disabled=true;
	d_vspace.disabled=true;
	d_hspace.disabled=true;
	Ok.disabled=true;
}

// 使所有输入框有效
function AbleItems(){
	d_checkfromfile.disabled=false;
	d_checkfromurl.disabled=false;
	d_fromurl.disabled=false;
	d_alt.disabled=false;
	d_border.disabled=false;
	d_bordercolor.disabled=false;
	d_filter.disabled=false;
	d_align.disabled=false;
	d_width.disabled=false;
	d_height.disabled=false;
	d_vspace.disabled=false;
	d_hspace.disabled=false;
	Ok.disabled=false;
}

</script>

<BODY bgColor=menu onload="InitDocument()">

<table border=0 cellpadding=0 cellspacing=0 align=center>
<tr>
	<td>
	<fieldset>
	<legend>图片来源</legend>
	<table border=0 cellpadding=0 cellspacing=0>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td width=54 align=right onclick="RadioClick('file')"><input type=radio id="d_checkfromfile" value="1" onclick="RadioClick('file')">上传:</td>
		<td width=5></td>
		<td colspan=5>
		<Script Language=JavaScript>
		document.write('<iframe id=d_file frameborder=0 src="../upload.asp?type=image&style=' + config.StyleName + '" width="100%" height="22" scrolling=no></iframe>');
		</Script>
		</td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td width=54 align=right onclick="RadioClick('url')"><input type=radio id="d_checkfromurl" value="1" onclick="RadioClick('url')">网络:</td>
		<td width=5></td>
		<td colspan=5><input type=text id="d_fromurl" style="width:243px" size=30 value=""></td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	</table>
	</fieldset>
	</td>
</tr>
<tr><td height=5></td></tr>
<tr>
	<td>
	<fieldset>
	<legend>显示效果</legend>
	<table border=0 cellpadding=0 cellspacing=0>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td>说明文字:</td>
		<td width=5></td>
		<td colspan=5><input type=text id=d_alt size=38 value="" style="width:243px"></td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td noWrap>边框粗细:</td>
		<td width=5></td>
		<td><input type=text id=d_border size=10 value="" ONKEYPRESS="event.returnValue=IsDigit();"></td>
		<td width=40></td>
		<td noWrap>边框颜色:</td>
		<td width=5></td>
		<td><table border=0 cellpadding=0 cellspacing=0><tr><td><input type=text id=d_bordercolor size=7 value=""></td><td><img border=0 src="../../../icon/button/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="SelectColor('bordercolor')"></td></tr></table></td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td>特殊效果:</td>
		<td width=5></td>
		<td>
			<select id=d_filter style="width:72px" size=1>
			<option value='' selected>无</option>
			<option value='Alpha(Opacity=50)'>半透明</option>
			<option value='Alpha(Opacity=0, FinishOpacity=100, Style=1, StartX=0, StartY=0, FinishX=100, FinishY=140)'>线型透明</option>
			<option value='Alpha(Opacity=10, FinishOpacity=100, Style=2, StartX=30, StartY=30, FinishX=200, FinishY=200)'>放射透明</option>
			<option value='blur(add=1,direction=14,strength=15)'>模糊效果</option><option value='blur(add=true,direction=45,strength=30)'>风动模糊</option>
			<option value='Wave(Add=0, Freq=60, LightStrength=1, Phase=0, Strength=3)'>正弦波纹</option>
			<option value='gray'>黑白照片</option><option value='Chroma(Color=#FFFFFF)'>白色透明</option>
			<option value='DropShadow(Color=#999999, OffX=7, OffY=4, Positive=1)'>投射阴影</option>
			<option value='Shadow(Color=#999999, Direction=45)'>阴影</option>
			<option value='Glow(Color=#ff9900, Strength=5)'>发光</option>
			<option value='flipv'>垂直翻转</option>
			<option value='fliph'>左右翻转</option>
			<option value='grays'>降低彩色</option>
			<option value='xray'>X光照片</option>
			<option value='invert'>底片</option>
            </select>		
		</td>
		<td width=40></td>
		<td>对齐方式:</td>
		<td width=5></td>
		<td>
			<select id=d_align size=1 style="width:72px">
			<option value='' selected>默认</option>
			<option value='left'>居左</option>
			<option value='right'>居右</option>
			<option value='top'>顶部</option>
			<option value='middle'>中部</option>
			<option value='bottom'>底部</option>
			<option value='absmiddle'>绝对居中</option>
			<option value='absbottom'>绝对底部</option>
			<option value='baseline'>基线</option>
			<option value='texttop'>文本顶部</option>
			</select>
		</td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td>图片宽度:</td>
		<td width=5></td>
		<td><input type=text id=d_width size=10 value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=4></td>
		<td width=40></td>
		<td>图片高度:</td>
		<td width=5></td>
		<td><input type=text id=d_height size=10 value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=4></td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	<tr>
		<td width=7></td>
		<td>上下间距:</td>
		<td width=5></td>
		<td><input type=text id=d_vspace size=10 value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=2></td>
		<td width=40></td>
		<td>左右间距:</td>
		<td width=5></td>
		<td><input type=text id=d_hspace size=10 value="" ONKEYPRESS="event.returnValue=IsDigit();" maxlength=2></td>
		<td width=7></td>
	</tr>
	<tr><td colspan=9 height=5></td></tr>
	</table>
	</fieldset>
	</td>
</tr>
<tr><td height=5></td></tr>
<tr><td align=right><input type=submit value='  确定  ' id=Ok onclick="ok()">&nbsp;&nbsp;<input type=button value='  取消  ' onclick="window.close();"></td></tr>
</table>

<div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:100px;display:none">
<table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#0046D5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF>...图片上传中...请等待...</font></marquee></td></tr></table>
</div>

</body>
</html>