<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<!--#include file="stat__config.asp"-->
<!--#include file="stat__conn.asp"-->
<%
'计算指定日期的访问量
function vdaycon(theday)
    theday=cdate(theday)
    thetday=cdate(theday+1)
    tmprs=conn.execute("Select count(id) as vdaycon from view where" & _
		" vtime>=datevalue('" & theday & "') and vtime<=datevalue('" & thetday & "')")
    vdaycon=tmprs("vdaycon")
	if isnull(vdaycon) then vdaycon=0
end function

'将星期序号翻译为汉字
function findweek(theweek)
	select case theweek
	case 1
		findweek="日"
	case 2
		findweek="一"
	case 3
		findweek="二"
	case 4
		findweek="三"
	case 5
		findweek="四"
	case 6
		findweek="五"
	case 7
		findweek="六"
	end select
end function
%>
<%
'权限检查
if session.Contents("master")=false and mlevel<2 then Response.Redirect "stat_help.asp?id=004&error=您没有查看周访问统计的权限。"
%>
<!--#include file="stat__head.asp"-->
<table class=table0 width=540 cellspacing=0 align=center>
  <tr><td class=td0><font class=p14><b>周访问量统计</b></font></td><td class=td0 align=right><font color=#ff0000 class=p12>注意：用鼠标点指图形柱或者图形柱下的数字可以看到对应的访问量</font>
  </td></tr>
</table>
<br>
<table class=table0 width="100%" align=center><tr><td class=td0>
<table class=table0 border="0" cellpadding="0" cellspacing="0" width="175" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="101">
<%
set conn=server.createobject("adodb.connection")
DBPath = connpath
conn.Open "driver={Microsoft Access Driver (*.mdb)};dbq=" & DBPath

'找到开始统计天数，如果天数不足7天，则跳过前面的空间
tmprs=conn.execute("Select first(vtime) as vfirst from view")
vfirst=tmprs("vfirst")
if isnull(vfirst) then vfirst=now()
vdays=int(date()-vfirst+1)

'声明二维数组，voutday(*,0)为访问量,voutday(*,1)为日期,voutday(*,2)为星期
dim vday(7,3),voutday(7,3)
maxday=0
sumday=0
for i=0 to 6
	vday(i,0)=vdaycon(date()-6+i)
	if vday(i,0)>maxday then maxday=vday(i,0)
	sumday=sumday+vday(i,0)
	vday(i,1)=day(date()-6+i)
	vday(i,2)=weekday(date()-6+i)
next
'防止除数为0而出错
if maxday=0 then maxday=1
if sumday=0 then sumday=1

'根据已统计天数将数值左移
if vdays>=7 then
	for i=0 to 6
		voutday(i,0)=vday(i,0)
		voutday(i,1)=vday(i,1)
		voutday(i,2)=vday(i,2)
	next
else
	for i=0 to 6
		if i<=vdays then
			voutday(i,0)=vday(i+6-vdays,0)
			voutday(i,1)=vday(i+6-vdays,1)
			voutday(i,2)=vday(i+6-vdays,2)
		else
			voutday(i,0)=0
			voutday(i,1)=""
			voutday(i,2)=0
		end if
	next
end if
%>
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxday*10+0.5)/10%></font></p>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxday*10/4+0.5)/10%></font></p>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxday*10/2+0.5)/10%></font></p>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxday*10/4+0.5)/10%><br></font></p></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 6
%>
<td class=td0 width=15 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid" src="<%=(stat_chart_gif)%>"
	height="<%=(voutday(i,0)/maxday)*100%>" width="9"
	alt="<%=voutday(i,1)%>日，星期<%=findweek(clng(voutday(i,2)))%>，访问<%=voutday(i,0)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(voutday(i,0)*1000/sumday+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>

<tr height="18">
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></p></td>
<td class=td0 width=10></td>
<%
for i= 0 to 6
%>
<td class=td0 width=15 align=center>
<a title="<%=voutday(i,1)%>日，星期<%=findweek(clng(voutday(i,2)))%>，访问<%=voutday(i,0)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(voutday(i,0)*1000/sumday+0.5)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"><%
'根据当天的日期用不同的颜色区分日期，周六用绿色，周日用红色
select case voutday(i,2)
case 1%>
<font class=p12 style="letter-spacing: -1" color="red">
<%case 7%>
<font class=p12 style="letter-spacing: -1" class=p12>
<%case else%>
<font class=p12 style="letter-spacing: -1">
<%end select%>
<%=findweek(voutday(i,2))%></font></a></td>
<%
next
%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5"><td class=td0></td></tr>
</table>

</td><td class=td0>

<table class=table0 border="0" cellpadding="0" cellspacing="0" width="175" align=center>
<tr height="9"><td class=td0></td></tr>
<tr height="101">
<%
Set rs = Server.CreateObject("ADODB.Recordset")
sql="select vweek,count(id) as allweek from view group by vweek"
rs.Open sql,conn,1,1

dim vallweek(7)
maxallweek=0
sumallweek=0
do while not rs.EOF
	vallweek(clng(rs("vweek"))-1)=clng(rs("allweek"))
	if vallweek(clng(rs("vweek"))-1)>maxallweek then maxallweek=vallweek(clng(rs("vweek"))-1)
	sumallweek=sumallweek+vallweek(clng(rs("vweek"))-1)
	rs.MoveNext
loop
'防止除数为0而出错
if maxallweek=0 then maxallweek=1
if sumallweek=0 then sumallweek=1

%>
<td class=td0 align=right valign=top>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallweek*10+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(3*maxallweek*10/4+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 8">
<font class=p12><%=int(maxallweek*10/2+0.5)/10%></font>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12><%=int(maxallweek*10/4+0.5)/10%><br></font></td>
<td class=td0 width=10><img src="stat_images/chart_l.gif"></td>
<%
for i= 0 to 6
%>
<td class=td0 width=15 valign=bottom background="stat_images/chart_b.gif" align=center>
<img style="BORDER-BOTTOM: #000000 1px solid;" src="<%=(stat_chart_gif)%>"
	height="<%=(vallweek(i)/maxallweek)*100%>" width="9"
	alt="星期<%=findweek(i+1)%>，访问<%=vallweek(i)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallweek(i)*1000/sumallweek)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%"></td>
<%
next
%>
<td class=td0 width=10><img src="stat_images/chart_r.gif"></td>
<td class=td0 width=10></td>
</tr>
<tr height="18">
<td class=td0 align=right>
<p style="line-height: 100%; margin-right: 2; margin-top: 0; margin-bottom: 0">
<font class=p12>0</font></p></td>
<td class=td0 width=10></td>
<%
for i= 0 to 6
%>
<td class=td0 width=15 align=center><a title="星期<%=findweek(i+1)%>，访问<%=vallweek(i)%>次，<%
	'计算访问量的百分数，精确到小数后1位，小于零的在前面加字母0
	lsbf=int(vallweek(i)*1000/sumallweek)/10
	if lsbf<1 then lsbf="0" & lsbf
	Response.Write lsbf
	%>%">
<%select case voutday(i,2)
case 1%>
<font class=p12 style="letter-spacing: -1" color="red">
<%case 7%>
<font class=p12 style="letter-spacing: -1" class=p12>
<%case else%>
<font class=p12 style="letter-spacing: -1">
<%end select%>
<%=findweek(i+1)%></font></a></td>
<%
next
%>
<td class=td0 width=10></td>
<td class=td0 width=10></td>
</tr>
<tr height="5"><td class=td0 colspan=29></td></tr>
</table>

</td></tr>
<tr height="20" align=center><td class=td0><font class=p14><u>最近一周</u></font></td><td class=td0><font class=p14><u>全部时段</u></font></td></tr>
</table>

<%
rs.Close
set rs=nothing
conn.Close 
set conn=nothing
%>
<!--#include file="stat__bottom.asp"-->