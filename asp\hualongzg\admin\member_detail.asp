<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true) AND (inint(session("flag"),"8") = false) Then Response.Redirect("default.asp") %>
<!--#include file="../Connections/conn.asp" -->
<%
Dim rsSTARINFO__MMColParam
rsSTARINFO__MMColParam = "1"
if (Request.QueryString("id") <> "") then rsSTARINFO__MMColParam = Request.QueryString("id")
%>
<%
set rsSTARINFO = Server.CreateObject("ADODB.Recordset")
rsSTARINFO.ActiveConnection = MM_conn_STRING
rsSTARINFO.Source = "SELECT * FROM member WHERE id = " + Replace(rsSTARINFO__MMColParam, "'", "''") + ""
rsSTARINFO.CursorType = 0
rsSTARINFO.CursorLocation = 2
rsSTARINFO.LockType = 3
rsSTARINFO.Open()
rsSTARINFO_numRows = 0
%>
<SCRIPT RUNAT=SERVER LANGUAGE=VBSCRIPT>					
function DoDateTime(str, nNamedFormat, nLCID)				
	dim strRet								
	dim nOldLCID								
										
	strRet = str								
	If (nLCID > -1) Then							
		oldLCID = Session.LCID						
	End If									
										
	On Error Resume Next							
										
	If (nLCID > -1) Then							
		Session.LCID = nLCID						
	End If									
										
	If ((nLCID < 0) Or (Session.LCID = nLCID)) Then				
		strRet = FormatDateTime(str, nNamedFormat)			
	End If									
										
	If (nLCID > -1) Then							
		Session.LCID = oldLCID						
	End If									
										
	DoDateTime = strRet							
End Function									
</SCRIPT>
<%
if request("modxlid") = "mod" then call editsave()
sub editsave()
	set rs=server.createobject("adodb.recordset")
	sql="select xlid from member where id="&request("id")
	rs.open sql,MM_conn_STRING,3,3

	rs("xlid")=request("xlid")
	rs.update

	rs.close()
	set rs=nothing
end sub
%>
<!--#include file ="_config.asp"-->
<!--#include file ="_head.asp"-->
<script language="JavaScript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- 必须输入有效的电子信箱\n';
      } else if (test!='R') {
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (val<min || max<val) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- 没有输入任何东西\n'; }
  } if (errors) alert('输入项目缺少或者内容不正确，请修改后再提交！\n\n'+errors);
  document.MM_returnValue = (errors == '');
}
//-->
</script>
<table class=table0 width=100%>
<tr>
	<td class="p20 td01"><%=(rsSTARINFO.Fields.Item("this_name").Value)%></td>
	<td class="nopr td0" style="padding-right:30px" align=right><a href="javascript:window.print()">&lt;&lt; 打印</a> <a href="javascript:history.go(-1)"> &lt;&lt; 返回</a></td>
</tr>
</table><br>
<table class=table0 width=600 cellpadding=3 class=table0>
<form name=xlid method="POST" action="member_detail.asp?id=<%=rsSTARINFO.Fields.Item("id").Value%>"><input type=hidden name="modxlid" value="mod"><input type=hidden name="id" value="<%=rsSTARINFO.Fields.Item("id").Value%>">
<tr>
<td class=td01 nowrap width=50>学历：</td><td width=100 class=td01>
<select name="xlid" style="width:100px">
<%
Set rs = Server.CreateObject("ADODB.Recordset")
sql = "SELECT xlid as id,xlname as name from xueli order by xlid asc"
rs.open sql,MM_conn_STRING,1,1
while not rs.eof%>
<option value=<%=rs("id")%><%if rs("id") = rsSTARINFO.Fields.Item("xlid").Value then response.write(" selected")%>><%=rs("name")%></option>
<% 
  rs.MoveNext()
Wend
rs.Close()
Set rs = Nothing
%>
</select>

</td><td class=td0><input type="submit" value="修改"></td>
<td class="td01 p12" align=right>报名时间： <%= DoDateTime((rsSTARINFO.Fields.Item("add_data").Value), 2, -1) %></td>
</tr></form></table>

				<table align="left" class=table1 width=600 cellpadding=3 class=table0>
				  <tr> 
					<td nowrap align="right" class=td11>*姓名</td>
					<td class=td11> 
					  <input disabled type="text" name="this_name"size="16" value="<%=(rsSTARINFO.Fields.Item("this_name").Value)%>">
					</td>
					<td nowrap align="right" class=td11>*性别</td>
					<td class=td11> 
					  <input disabled type="text" name="sex"size="4" value="<%=(rsSTARINFO.Fields.Item("sex").Value)%>">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>考生号（准考证号）</td>
					<td class=td11 colspan=3> 
					  <input value="<%=(rsSTARINFO.Fields.Item("this_no").Value)%>" disabled type="text" name="sfzhm"size="16" class=i400>
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>*民族</td>
					<td class=td11> 
					  <input disabled type="text" name="this_mingzu"size="16" value="<%=(rsSTARINFO.Fields.Item("this_mingzu").Value)%>">
					</td>
					<td nowrap align="right" class=td11>*出生年月</td>
					<td class=td11 nowrap> 
          <input disabled type=text name=age size=16 value="<%=(rsSTARINFO.Fields.Item("age").Value)%>">
				</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>身份证号码</td>
					<td class=td11 colspan=3> 
					  <input value="<%=(rsSTARINFO.Fields.Item("sfzhm").Value)%>" disabled type="text" name="sfzhm"size="16" class=i400>
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>*籍贯</td>
					<td class=td11 nowrap> 
          <input value="<%=(rsSTARINFO.Fields.Item("jiguan").Value)%>" disabled type=text name=jiguan size=16>
					</td>
					<td nowrap align="right" class=td11>*毕业学校</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("this_biye").Value)%>" disabled type="text" name="this_biye"size="16">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>本人特长</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("this_techang").Value)%>" disabled type="text" name="this_techang"size="30">
					</td>
					<td nowrap align="right" class=td11>政治面貌</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("this_zhzh").Value)%>" disabled type="text" name="this_zhzh"size="16">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>身高</td>
					<td class=td11 colspan=3> 
					  <input value="<%=(rsSTARINFO.Fields.Item("this_sta").Value)%>" disabled type="text" name="this_sta"size="16">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>*详细通信地址</td>
					<td class=td11 colspan=3> 
					  <input value="<%=(rsSTARINFO.Fields.Item("address").Value)%>" disabled type="text" name="address"size="16" class=i400>
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>*联系电话</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("phome").Value)%>" disabled type="text" name="phome"size="16">
					</td>
					<td nowrap align="right" class=td11>*邮编</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("zip").Value)%>" disabled type="text" name="zip"size="16">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>*第一志愿</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("zhuanye1").Value)%>" disabled type="text" name="zhuanye1"size="16">
					</td>
					<td nowrap align="right" class=td11>*第二志愿</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("zhuanye2").Value)%>" disabled type="text" name="zhuanye2"size="16">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11>手机</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("BP").Value)%>" disabled type="text" name="BP"size="16">
					</td>
					<td nowrap align="right" class=td11>电子信箱</td>
					<td class=td11> 
					  <input value="<%=(rsSTARINFO.Fields.Item("email").Value)%>" disabled type="text" name="email"size="30">
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11 valign=top>个人简历</td>
					<td class=td11 colspan="3"> 
					  <textarea name="this_jianli" cols="60" class=i400 rows="5"><%=(rsSTARINFO.Fields.Item("this_jianli").Value)%></textarea>
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11 valign=top>家庭主要成员</td>
					<td class=td11 colspan="3"> 
					  <textarea name="this_home" cols="60" class=i400 rows="5"><%=(rsSTARINFO.Fields.Item("this_home").Value)%></textarea>
					</td>
				  </tr>
				  <tr> 
					<td nowrap align="right" class=td11 valign=top>奖惩情况</td>
					<td class=td11 colspan="3"> 
					  <textarea name="this_jch" cols="60" class=i400 rows="5"><%=(rsSTARINFO.Fields.Item("this_jch").Value)%></textarea>
					</td>
				  </tr>
				</table>
	</td></tr>
<tr><td height=30 align=right class=td0 style="padding-right:30px"><a href="javascript:window.print()">&lt;&lt; 打印</a> <a href="javascript:history.go(-1)">&lt;&lt; 返回</a></td></tr>
<!--#include file ="_bottom.asp"-->
<%
rsSTARINFO.Close()
%>