<%@LANGUAGE="VBSCRIPT" codepage="65001"%>
<%If (Session("isadmin") <> true AND session("ismember")=false) Then 
	Response.write("<p style='font-size:14px;padding:10px'>登录过期，请重新登录。</p>")
	Response.End
End If
%>
<!--#include file="../Connections/conn.asp" -->
<%
action = request.querystring("action")
If action = "add" Or action = "mod" then
	rels_config_id = request.querystring("rels_config_id")
	table1 = request.querystring("table1")
	table1_key = request.querystring("table1_key")
	where1 = request.querystring("where1")
	table2 = request.querystring("table2")
	table2_key = request.querystring("table2_key")
	where2 = request.querystring("where2")
	sellable = request.querystring("sellable")
End If
Select Case action
Case "add"
	mdtitle = "增加关联信息"
	current_sel = request.querystring("current_sel")
	current_selid =  request.querystring("current_selid")
Case "mod"
	mdtitle = "查看编辑关联信息"
	current_rel_id = request.querystring("current_rel_id")
Case "addok"'此处可以加入p_sort_id s_sort_id值
	set rs=server.createobject("adodb.recordset")
	sql="select rels_id,p_id_val,s_id_val,rels_descript from rels"
	rs.open sql,MM_conn_STRING,3,3
	rs.addnew
	rs("rels_id") = request.querystring("rels_config_id")
	rs("rels_descript") = VBsUnEscape(request.Form)
	If request.querystring("current_sel") = "1" then
		rs("p_id_val") = request.querystring("current_selid") 
		rs("s_id_val") = request.querystring("rel_list") 
	Else
		rs("p_id_val") = request.querystring("rel_list") 
		rs("s_id_val") = request.querystring("current_selid")
	End if
	rs.update
	rs.close()
	Set rs=nothing
Case "modok"
	current_rel_id = request.querystring("current_rel_id")
	rel_con =  VBsUnEscape(request.Form)
	set rs=server.createobject("adodb.recordset")
	sql="select rels_descript from rels where id="&current_rel_id
	rs.open sql,MM_conn_STRING,3,2
	rs("rels_descript") = rel_con
	rs.update
	rs.close()
	Set rs=nothing
End Select


%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="Owner" content="" />
<meta name="Author" content="www.soundjet.net" />
<meta name="Keywords" content="" />
<meta name="Description" content="" />
<meta name="Abstract" content="" />
<title> <%=mdtitle%> </title>
<style type="text/css">
/* <![CDATA[ */
div#relmodadd{width:400;padding-left:50px;font-size:12px;padding-top:10px}
div#relmodadd p{padding:0;margin:15px 0 5px 0}
div#relmodadd label{background-color:#d4e1f7;padding:2px 5px 1px 4px;margin-right:4px}
div#relmodadd textarea{width:400px;}
div#relmodadd textarea,div#relmodadd select,div#relmodadd option{border:1px #999 solid}
div#relmodadd a{display:block;width:40px;margin-right:5px;float:left;text-align:center;border:1px #333 solid;text-decoration:none;color:#333}
div#relmodadd a:hover{color:#f00}
/* ]]> */ 
</style>
<script type="text/javascript">
//<![CDATA[
var sellable = "<%=sellable%>";
var rels_config_id = "<%=rels_config_id%>";
var table1 = "<%=table1%>";
var table2 = "<%=table2%>";
var current_sel = "<%=current_sel%>";
var current_selid = "<%=current_selid%>";
var current_rel_id = "<%=current_rel_id%>";

function addrelok(){//参数通过JS传过去
	if(document.getElementById("rel_list").children[document.getElementById("rel_list").selectedIndex].value == ''){
		alert("请选择" + sellable);document.getElementById("rel_list").focus();return false;
	}
	window.dialogArguments.addrelok(rels_config_id,current_sel,current_selid,document.getElementById("rel_list").children[document.getElementById("rel_list").selectedIndex].value,escape(document.getElementById("rel_con").value));
	window.dialogArguments.relslist(window.dialogArguments.current_sel,window.dialogArguments.current_selid);
	window.close();
}

function modrelok(){
	window.dialogArguments.modrelok(current_rel_id,escape(document.getElementById("rel_con").value));
	window.close();
}

//]]>
</script>
</head>
<body>
<div id="relmodadd">
<%Select Case action
Case "add"

If current_sel = 1 then
	sql="select " & table2_key & " from " & table2 & " " & Split(where2,"order by")(0)
 	sql1= " and " & split(table2_key,",")(0) & " not in (select rels.s_id_val from rels where rels.rels_id=" & rels_config_id & " and p_id_val=" & current_selid & ")" & " order by " & Split(where2,"order by")(1)
	sql = sql & sql1
Else
	sql="select " & table1_key & " from " & table1 & " " & Split(where1,"order by")(0)
 	sql1= " and " & split(table1_key,",")(0) & " not in (select rels.p_id_val from rels where rels.rels_id=" & rels_config_id & " and s_id_val=" & current_selid & ")" & " order by " & Split(where1,"order by")(1)
	sql = sql & sql1
End If
%>
<p><label>关联项目</label> <%=add_itemname%> == <br />
<select id="rel_list" size="1" style="width:290px">
		<option selected="selected">选择<%=sellable%></option><%
set rs=server.createobject("adodb.recordset")
rs.open sql,MM_conn_STRING,1,1
While Not rs.eof
		%><option value="<%=rs(0)%>"><%=rs(1)%></option>
		<%
rs.movenext()
Wend
rs.close()
Set rs=nothing
%>
	</select>
</p>
<p><label>关联信息（120字以内）</label><br /><textarea id="rel_con" rows="3"></textarea></p>
<a href="#" onclick="addrelok()">增加</a>
<%Case "mod"%>
<p><label>关联项目</label><%=mod_itemname%><br />
<p><label>关联信息（120字以内）</label><br /><textarea id="rel_con" rows="3"><%=mod_itemcon%></textarea></p>
<a href="#" onclick="modrelok()">修改</a>
<%End select
%>
</div>
</body>
</html><%
Function add_itemname
	If current_sel = 1 then
		sql_add_itemname="select " & split(table1_key,",")(1) & " from " & table1 & " " & Split(where1,"order by")(0) & " and " & split(table1_key,",")(0) & "=" & current_selid
	Else
		sql_add_itemname="select " & split(table2_key,",")(1) & " from " & table2 & " " & Split(where2,"order by")(0) & " and " & split(table2_key,",")(0) & "=" & current_selid
	End If
	set rs=server.createobject("adodb.recordset")
	rs.open sql_add_itemname,MM_conn_STRING,1,1
	add_itemname = rs(0)
	rs.close()
	Set rs=nothing
End function

Function mod_itemname
	set rs=server.createobject("adodb.recordset")
	sql_mod_itemname="select " & split(table1_key,",")(1) & " from " & table1 & " " & Split(where1,"order by")(0) & "  and " & split(table1_key,",")(0) & " in (select rels.p_id_val from rels where rels.id=" & current_rel_id & ")"
	rs.open sql_mod_itemname,MM_conn_STRING,1,1
	mod_itemname = rs(0)
	rs.close()

	sql_mod_itemname="select " & split(table2_key,",")(1) & " from " & table2 & " " & Split(where2,"order by")(0) & " and " & split(table2_key,",")(0) & " in (select rels.s_id_val from rels where rels.id=" & current_rel_id & ")"
	rs.open sql_mod_itemname,MM_conn_STRING,1,1

	mod_itemname = mod_itemname & " == " & rs(0)
	rs.close()
	Set rs=nothing
End Function

Function mod_itemcon
	set rs=server.createobject("adodb.recordset")
	sql_mod_itemcon="select rels_descript from rels where id=" & current_rel_id
	rs.open sql_mod_itemcon,MM_conn_STRING,1,1
	mod_itemcon = rs(0)
	rs.close()
	Set rs=nothing
End Function

Function VBsUnEscape(str) 
    dim i,s,c 
    s="" 
    For i=1 to Len(str) 
        c=Mid(str,i,1) 
        If Mid(str,i,2)="%u" and i<=Len(str)-5 Then 
            If IsNumeric("&H" & Mid(str,i+2,4)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+2,4))) 
                i = i+5 
            Else 
                s = s & c 
            End If 
        ElseIf c="%" and i<=Len(str)-2 Then 
            If IsNumeric("&H" & Mid(str,i+1,2)) Then 
                s = s & CHRW(CInt("&H" & Mid(str,i+1,2))) 
                i = i+2 
            Else 
                s = s & c 
            End If 
        Else 
            s = s & c 
        End If 
    Next 
    VBsUnEscape = s 
End Function 

%>